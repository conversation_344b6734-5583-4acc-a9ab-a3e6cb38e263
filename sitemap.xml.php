<?php
/**
 * Dynamic Sitemap Generator for ROKA FLEX
 * Generates XML sitemap with all pages, products, services, portfolio items
 * Supports both Romanian and English languages
 */

// Set content type to XML
header('Content-Type: application/xml; charset=utf-8');

// Include configuration
require_once __DIR__ . '/config/config.php';

// Function to format date for sitemap
function formatSitemapDate($date) {
    if (empty($date)) {
        return date('Y-m-d');
    }
    return date('Y-m-d', strtotime($date));
}

// Function to get priority based on page type
function getPriority($type) {
    switch ($type) {
        case 'home':
            return '1.0';
        case 'about':
        case 'contact':
            return '0.9';
        case 'portfolio':
        case 'portfolio-special':
            return '0.8';
        case 'category':
            return '0.7';
        case 'product':
        case 'service':
            return '0.6';
        case 'portfolio-details':
        case 'portfolio-special-details':
            return '0.5';
        default:
            return '0.5';
    }
}

// Function to get change frequency
function getChangeFreq($type) {
    switch ($type) {
        case 'home':
            return 'weekly';
        case 'about':
        case 'contact':
            return 'monthly';
        case 'portfolio':
        case 'portfolio-special':
            return 'weekly';
        case 'category':
        case 'product':
        case 'service':
            return 'monthly';
        case 'portfolio-details':
        case 'portfolio-special-details':
            return 'yearly';
        default:
            return 'monthly';
    }
}

// Start XML output
echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:xhtml="http://www.w3.org/1999/xhtml">' . "\n";

// Supported languages
$languages = ['ro', 'en'];

// Static pages
$static_pages = [
    '' => 'home',           // Home page
    'about' => 'about',     // About page
    'contact' => 'contact', // Contact page
    'portfolio' => 'portfolio', // Portfolio page
    'portfolio-special' => 'portfolio-special' // Special portfolio page
];

// Generate static pages for both languages
foreach ($static_pages as $route => $type) {
    foreach ($languages as $lang) {
        $url = ROOT_URL;
        if (!empty($route)) {
            if ($lang === 'en') {
                $url .= '/en/' . $route;
            } else {
                $url .= '/' . $route;
            }
        } else {
            // Home page
            if ($lang === 'en') {
                $url .= '/en';
            }
        }
        
        echo "  <url>\n";
        echo "    <loc>" . htmlspecialchars($url) . "</loc>\n";
        echo "    <lastmod>" . formatSitemapDate(null) . "</lastmod>\n";
        echo "    <changefreq>" . getChangeFreq($type) . "</changefreq>\n";
        echo "    <priority>" . getPriority($type) . "</priority>\n";
        
        // Add alternate language links
        foreach ($languages as $alt_lang) {
            $alt_url = ROOT_URL;
            if (!empty($route)) {
                if ($alt_lang === 'en') {
                    $alt_url .= '/en/' . $route;
                } else {
                    $alt_url .= '/' . $route;
                }
            } else {
                if ($alt_lang === 'en') {
                    $alt_url .= '/en';
                }
            }
            echo "    <xhtml:link rel=\"alternate\" hreflang=\"" . $alt_lang . "\" href=\"" . htmlspecialchars($alt_url) . "\" />\n";
        }
        
        echo "  </url>\n";
    }
}

// Generate categories
try {
    $categories = query("SELECT * FROM `categorii` WHERE `visible` = 1 ORDER BY `position` ASC")->fetchAll();
    
    foreach ($categories as $category) {
        foreach ($languages as $lang) {
            $slug = slugify($category['titlu_' . $lang], $category['id']);
            $url = ROOT_URL;
            if ($lang === 'en') {
                $url .= '/en/categorie/' . $slug;
            } else {
                $url .= '/categorie/' . $slug;
            }
            
            echo "  <url>\n";
            echo "    <loc>" . htmlspecialchars($url) . "</loc>\n";
            echo "    <lastmod>" . formatSitemapDate($category['updated_at'] ?? $category['created_at'] ?? null) . "</lastmod>\n";
            echo "    <changefreq>" . getChangeFreq('category') . "</changefreq>\n";
            echo "    <priority>" . getPriority('category') . "</priority>\n";
            
            // Add alternate language links
            foreach ($languages as $alt_lang) {
                $alt_slug = slugify($category['titlu_' . $alt_lang], $category['id']);
                $alt_url = ROOT_URL;
                if ($alt_lang === 'en') {
                    $alt_url .= '/en/categorie/' . $alt_slug;
                } else {
                    $alt_url .= '/categorie/' . $alt_slug;
                }
                echo "    <xhtml:link rel=\"alternate\" hreflang=\"" . $alt_lang . "\" href=\"" . htmlspecialchars($alt_url) . "\" />\n";
            }
            
            echo "  </url>\n";
        }
    }
} catch (Exception $e) {
    // Categories table might not exist or have different structure
    error_log("Sitemap: Could not fetch categories - " . $e->getMessage());
}

// Generate products
try {
    $products = query("SELECT * FROM `produse` WHERE `visible` = 1 ORDER BY `created_at` DESC")->fetchAll();
    
    foreach ($products as $product) {
        foreach ($languages as $lang) {
            $slug = slugify($product['titlu_' . $lang], $product['id']);
            $url = ROOT_URL;
            if ($lang === 'en') {
                $url .= '/en/produs/' . $slug;
            } else {
                $url .= '/produs/' . $slug;
            }
            
            echo "  <url>\n";
            echo "    <loc>" . htmlspecialchars($url) . "</loc>\n";
            echo "    <lastmod>" . formatSitemapDate($product['updated_at'] ?? $product['created_at'] ?? null) . "</lastmod>\n";
            echo "    <changefreq>" . getChangeFreq('product') . "</changefreq>\n";
            echo "    <priority>" . getPriority('product') . "</priority>\n";
            
            // Add alternate language links
            foreach ($languages as $alt_lang) {
                $alt_slug = slugify($product['titlu_' . $alt_lang], $product['id']);
                $alt_url = ROOT_URL;
                if ($alt_lang === 'en') {
                    $alt_url .= '/en/produs/' . $alt_slug;
                } else {
                    $alt_url .= '/produs/' . $alt_slug;
                }
                echo "    <xhtml:link rel=\"alternate\" hreflang=\"" . $alt_lang . "\" href=\"" . htmlspecialchars($alt_url) . "\" />\n";
            }
            
            echo "  </url>\n";
        }
    }
} catch (Exception $e) {
    // Products table might not exist or have different structure
    error_log("Sitemap: Could not fetch products - " . $e->getMessage());
}

// Generate services
try {
    $services = query("SELECT * FROM `servicii` WHERE `visible` = 1 ORDER BY `created_at` DESC")->fetchAll();
    
    foreach ($services as $service) {
        foreach ($languages as $lang) {
            $slug = slugify($service['titlu_' . $lang], $service['id']);
            $url = ROOT_URL;
            if ($lang === 'en') {
                $url .= '/en/serviciu/' . $slug;
            } else {
                $url .= '/serviciu/' . $slug;
            }
            
            echo "  <url>\n";
            echo "    <loc>" . htmlspecialchars($url) . "</loc>\n";
            echo "    <lastmod>" . formatSitemapDate($service['updated_at'] ?? $service['created_at'] ?? null) . "</lastmod>\n";
            echo "    <changefreq>" . getChangeFreq('service') . "</changefreq>\n";
            echo "    <priority>" . getPriority('service') . "</priority>\n";
            
            // Add alternate language links
            foreach ($languages as $alt_lang) {
                $alt_slug = slugify($service['titlu_' . $alt_lang], $service['id']);
                $alt_url = ROOT_URL;
                if ($alt_lang === 'en') {
                    $alt_url .= '/en/serviciu/' . $alt_slug;
                } else {
                    $alt_url .= '/serviciu/' . $alt_slug;
                }
                echo "    <xhtml:link rel=\"alternate\" hreflang=\"" . $alt_lang . "\" href=\"" . htmlspecialchars($alt_url) . "\" />\n";
            }
            
            echo "  </url>\n";
        }
    }
} catch (Exception $e) {
    // Services table might not exist or have different structure
    error_log("Sitemap: Could not fetch services - " . $e->getMessage());
}

// Generate portfolio projects
try {
    $projects = query("SELECT * FROM `proiecte` WHERE `vizibil` = 1 ORDER BY `created_at` DESC")->fetchAll();

    foreach ($projects as $project) {
        foreach ($languages as $lang) {
            $slug = slugify($project['titlu_' . $lang], $project['id']);
            $url = ROOT_URL;
            if ($lang === 'en') {
                $url .= '/en/portfolio-details/' . $slug;
            } else {
                $url .= '/portfolio-details/' . $slug;
            }

            echo "  <url>\n";
            echo "    <loc>" . htmlspecialchars($url) . "</loc>\n";
            echo "    <lastmod>" . formatSitemapDate($project['updated_at'] ?? $project['created_at'] ?? null) . "</lastmod>\n";
            echo "    <changefreq>" . getChangeFreq('portfolio-details') . "</changefreq>\n";
            echo "    <priority>" . getPriority('portfolio-details') . "</priority>\n";

            // Add alternate language links
            foreach ($languages as $alt_lang) {
                $alt_slug = slugify($project['titlu_' . $alt_lang], $project['id']);
                $alt_url = ROOT_URL;
                if ($alt_lang === 'en') {
                    $alt_url .= '/en/portfolio-details/' . $alt_slug;
                } else {
                    $alt_url .= '/portfolio-details/' . $alt_slug;
                }
                echo "    <xhtml:link rel=\"alternate\" hreflang=\"" . $alt_lang . "\" href=\"" . htmlspecialchars($alt_url) . "\" />\n";
            }

            echo "  </url>\n";
        }
    }
} catch (Exception $e) {
    // Projects table might not exist or have different structure
    error_log("Sitemap: Could not fetch projects - " . $e->getMessage());
}

// Generate special portfolio projects
try {
    $special_projects = query("SELECT * FROM `proiecte_speciale` WHERE `vizibil` = 1 ORDER BY `created_at` DESC")->fetchAll();

    foreach ($special_projects as $project) {
        foreach ($languages as $lang) {
            $slug = slugify($project['titlu_' . $lang], $project['id']);
            $url = ROOT_URL;
            if ($lang === 'en') {
                $url .= '/en/portfolio-special-details/' . $slug;
            } else {
                $url .= '/portfolio-special-details/' . $slug;
            }

            echo "  <url>\n";
            echo "    <loc>" . htmlspecialchars($url) . "</loc>\n";
            echo "    <lastmod>" . formatSitemapDate($project['updated_at'] ?? $project['created_at'] ?? null) . "</lastmod>\n";
            echo "    <changefreq>" . getChangeFreq('portfolio-special-details') . "</changefreq>\n";
            echo "    <priority>" . getPriority('portfolio-special-details') . "</priority>\n";

            // Add alternate language links
            foreach ($languages as $alt_lang) {
                $alt_slug = slugify($project['titlu_' . $alt_lang], $project['id']);
                $alt_url = ROOT_URL;
                if ($alt_lang === 'en') {
                    $alt_url .= '/en/portfolio-special-details/' . $alt_slug;
                } else {
                    $alt_url .= '/portfolio-special-details/' . $alt_slug;
                }
                echo "    <xhtml:link rel=\"alternate\" hreflang=\"" . $alt_lang . "\" href=\"" . htmlspecialchars($alt_url) . "\" />\n";
            }

            echo "  </url>\n";
        }
    }
} catch (Exception $e) {
    // Special projects table might not exist or have different structure
    error_log("Sitemap: Could not fetch special projects - " . $e->getMessage());
}

// Close XML
echo "</urlset>\n";
?>
