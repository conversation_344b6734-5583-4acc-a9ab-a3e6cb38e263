<?php
/**
 * Manual Sitemap Generator for ROKA FLEX
 * Use this script to manually generate and save a static sitemap.xml file
 * Run this script whenever you add new content or want to update the sitemap
 */

// Include configuration
require_once __DIR__ . '/config/config.php';

echo "ROKA FLEX Sitemap Generator\n";
echo "==========================\n\n";

try {
    // Start output buffering to capture the sitemap content
    ob_start();
    
    // Include the dynamic sitemap generator
    include __DIR__ . '/sitemap.xml.php';
    
    // Get the generated content
    $sitemap_content = ob_get_clean();
    
    // Save to static sitemap.xml file
    $sitemap_file = __DIR__ . '/sitemap.xml';
    $result = file_put_contents($sitemap_file, $sitemap_content);
    
    if ($result !== false) {
        echo "✅ Sitemap generated successfully!\n";
        echo "📁 File saved to: " . $sitemap_file . "\n";
        echo "📊 File size: " . formatBytes($result) . "\n";
        
        // Count URLs in sitemap
        $url_count = substr_count($sitemap_content, '<url>');
        echo "🔗 Total URLs: " . $url_count . "\n";
        
        // Show breakdown by type
        echo "\n📋 URL Breakdown:\n";
        echo "   - Static pages: " . (substr_count($sitemap_content, 'home') + substr_count($sitemap_content, 'about') + substr_count($sitemap_content, 'contact') + substr_count($sitemap_content, 'portfolio')) . "\n";
        
        // Try to count dynamic content
        try {
            $categories = query("SELECT COUNT(*) as count FROM `categorii` WHERE `visible` = 1")->fetch();
            echo "   - Categories: " . ($categories['count'] * 2) . " (both languages)\n";
        } catch (Exception $e) {
            echo "   - Categories: Unable to count\n";
        }
        
        try {
            $products = query("SELECT COUNT(*) as count FROM `produse` WHERE `visible` = 1")->fetch();
            echo "   - Products: " . ($products['count'] * 2) . " (both languages)\n";
        } catch (Exception $e) {
            echo "   - Products: Unable to count\n";
        }
        
        try {
            $services = query("SELECT COUNT(*) as count FROM `servicii` WHERE `visible` = 1")->fetch();
            echo "   - Services: " . ($services['count'] * 2) . " (both languages)\n";
        } catch (Exception $e) {
            echo "   - Services: Unable to count\n";
        }
        
        try {
            $projects = query("SELECT COUNT(*) as count FROM `proiecte` WHERE `vizibil` = 1")->fetch();
            echo "   - Portfolio projects: " . ($projects['count'] * 2) . " (both languages)\n";
        } catch (Exception $e) {
            echo "   - Portfolio projects: Unable to count\n";
        }
        
        try {
            $special_projects = query("SELECT COUNT(*) as count FROM `proiecte_speciale` WHERE `vizibil` = 1")->fetch();
            echo "   - Special projects: " . ($special_projects['count'] * 2) . " (both languages)\n";
        } catch (Exception $e) {
            echo "   - Special projects: Unable to count\n";
        }
        
        echo "\n🌐 Languages supported: Romanian (ro), English (en)\n";
        echo "📅 Generated on: " . date('Y-m-d H:i:s') . "\n";
        echo "\n✨ Sitemap is ready for search engines!\n";
        echo "🔗 Access it at: " . ROOT_URL . "/sitemap.xml\n";
        
    } else {
        echo "❌ Error: Could not save sitemap file!\n";
        echo "Check file permissions for the web root directory.\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error generating sitemap: " . $e->getMessage() . "\n";
    echo "Please check your database connection and configuration.\n";
}

/**
 * Format bytes into human readable format
 */
function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "Sitemap generation complete!\n";
?>
