<?php
// get category id
$slug_parts = explode('-', $segments[1]);
$produs_id  = end($slug_parts);
$produs     = query("SELECT * FROM `produse` WHERE `id` = :id", [
	'id' => $produs_id
])->fetch();

if (!$produs) {
	// Redirect to 404 page if produs not found, with js
	echo '<script>window.location.href = "' . ROOT_URL . '";</script>';
	exit;
}

$imagini = query("SELECT * FROM `produse_imagini` WHERE `id_produs` = :id ORDER BY `position` DESC", [
	'id' => $produs_id
])->fetchAll();
$imagine_default = query("SELECT * FROM `produse_imagini` WHERE `id_produs` = :id AND `is_default` = 1", [
	'id' => $produs_id
])->fetch();
$sectiuni = query("SELECT * FROM `produse_sectiuni` WHERE `id_produs` = :id", [
	'id' => $produs_id
])->fetchAll();

?>
<main>
	<!-- service Details breadcrumb area start -->
	<section class="tp-breadcrumb__area pb-70 pt-50 p-relative z-index-1 fix">
		<div class="tp-breadcrumb__bg" data-background="<?= ASSETS_URL ?>/img/hero/3.png"></div>
		<div class="container">
			<div class="row align-items-center">
				<div class="col-sm-12">
					<div class="tp-breadcrumb__content text-center">
						<h3 class="tp-breadcrumb__title"><?= $produs['titlu_' . LANG] ?></h3>
					</div>
				</div>
			</div>
		</div>
	</section>
	<!-- service Details breadcrumb area end -->

	<?= render_breadcrumbs() ?>
	<!-- service details area start -->
	<section class="tp-service-details-area pt-40 pb-70">
		<div class="container">
			<div class="row">
				<div class="col-lg-4">
					<div class="tp-service-details-wrapper-left">
						<div class="tp-service-details-box mb-50">
							<ul>
								<?php foreach ($sectiuni as $sectiune) { ?>
									<li>
										<a href="#tab-sectiune-<?= $sectiune['id'] ?>"><?= $sectiune['titlu_' . LANG] ?>
											<span>
												<i class="fa-solid fa-arrow-right"></i>
											</span>
										</a>
									</li>
								<?php } ?>
							</ul>
						</div>
						<div class="tp-service-details-box mb-50">
							<div class="tp-service-details-price">
								<h4 class="tp-service-details-box-title"><?= $content['general_produse_solicita_oferta'] ?></h4>
								<a class="tp-btn mb-30" href="<?= ROOT_URL ?>/contact"><?= $content['general_produse_solicita_oferta_btn'] ?>
									<span>
										<i class="fa-light fa-arrow-up-right"></i>
									</span>
								</a>
							</div>
						</div>
						<div class="tp-service-details-box">
							<div class="tp-counter-call call d-flex align-items-center">
								<div class="tp-counter-call-icon call">
									<span>
										<i class="fa-regular fa-phone-volume"></i>
									</span>
								</div>
								<div class="tp-counter-call-info">
									<p><?= $content['general_produse_ia_legatura_expert'] ?></p>
									<span>
										<a href="tel:<?= $content['general_telefon'] ?>"><?= $content['general_telefon'] ?></a>
									</span>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="col-lg-8">
					<div class="tp-service-details-wrapper">
						<div class="mb-50">
							<h3 class="tp-service-details-title"><?= $produs['titlu_' . LANG] ?></h3>
							<div class="tp-service-details-thumb mb-50">
								<img src="<?= ADMIN_ROOT . $imagine_default['image_path'] ?>" class="w-100" alt="<?= get_image_alt('product', $produs, 'Imagine principală') ?>">
							</div>
							<?= $produs['descriere_' . LANG] ?>
						</div>
						<?php foreach ($sectiuni as $sectiune) { ?>
							<div class="mb-50" id="tab-sectiune-<?= $sectiune['id'] ?>">
								<h3 class="tp-service-details-title"><?= $sectiune['titlu_' . LANG] ?></h3>
								<?= $sectiune['continut_' . LANG] ?>
							</div>
						<?php } ?>
						<div class="mb-50" id="fisa-tehnica">
							<h3 class="tp-service-details-title"><?= $content['general_produse_fisa_tehnica_title'] ?></h3>
							<?php
							$form_btn_text = $content['general_produse_fisa_tehnica_btn'];
							include __DIR__ . '/sections/contact_form_section.php';
							?>
						</div>
						<div class="mb-50" id="galerie-imagini">
							<div class="galerie row" id="lightgallery">
								<?php
								$image_counter = 1;
								foreach ($imagini as $imagine): ?>
									<a class="col-sm-4 mb-4" href="<?= ADMIN_ROOT . $imagine['image_path'] ?? '' ?>">
										<div class="image-resizer" style="background-image: url('<?= ADMIN_ROOT . $imagine['image_path'] ?? '' ?>')"></div>
										<img class="mb-3 d-none" loading="lazy" src="<?= ADMIN_ROOT . $imagine['image_path'] ?? '' ?>" alt="<?= get_image_alt('gallery', $produs, 'Galerie produs - Imagine ' . $image_counter) ?>">
									</a>
								<?php $image_counter++; endforeach; ?>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>
	<!-- service details area end -->
</main>
<script type="text/javascript">
	document.addEventListener('DOMContentLoaded', function () {
		lightGallery(document.getElementById('lightgallery'), {
			plugins: [lgZoom, lgThumbnail],
			licenseKey: '0000-0000-000-0000',
			speed: 500,
		});
	});
</script>