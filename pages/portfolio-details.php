<?php
// get category id
$slug_parts  = explode('-', $segments[1]);
$proiect_id = end($slug_parts);
$proiect   = query("SELECT * FROM `proiecte` WHERE `id` = :id", [
	'id' => $proiect_id
])->fetch();

if (!$proiect) {
	// Redirect to 404 page if proiect not found, with js
	echo '<script>window.location.href = "' . ROOT_URL . '";</script>';
	exit;
}

?>
<main>
	<!-- Portfolio Details breadcrumb area start -->
	<section class="tp-breadcrumb__area pb-70 pt-50 p-relative z-index-1 fix">
		<div class="tp-breadcrumb__bg" data-background="<?= ASSETS_URL ?>/img/hero/3.png"></div>
		<div class="container">
			<div class="row align-items-center">
				<div class="col-sm-12">
					<div class="tp-breadcrumb__content text-center">
						<h3 class="tp-breadcrumb__title"><?= $proiect['titlu_' . LANG] ?></h3>
					</div>
				</div>
			</div>
		</div>
	</section>
	<!-- Portfolio Details breadcrumb area end -->

	<?= render_breadcrumbs() ?>

	<!-- Portfolio area start -->
	<section class="tp-portfolio-details-area pt-60 pb-90">
		<div class="container">
			<div class="galerie row" id="lightgallery">
				<?php
				$imagini = query("SELECT * FROM `proiecte_imagini` WHERE `id_proiect` = :id ORDER BY `position` ASC", [
					'id' => $proiect['id']
				])->fetchAll();
				$image_counter = 1;
				foreach ($imagini as $imagine):
					?>
					<a class="col-sm-4 mb-4" href="<?= ADMIN_ROOT . $imagine['image_path'] ?? '' ?>">
						<div class="image-resizer" style="background-image: url('<?= ADMIN_ROOT . $imagine['image_path'] ?? '' ?>')"></div>
						<img class="mb-3 d-none" loading="lazy" src="<?= ADMIN_ROOT . $imagine['image_path'] ?? '' ?>" alt="<?= get_image_alt('gallery', $proiect, 'Imagine ' . $image_counter) ?>">
					</a>
				<?php $image_counter++; endforeach; ?>
			</div>
		</div>
	</section>
	<!-- Portfolio area end -->
</main>
<script type="text/javascript">
    document.addEventListener('DOMContentLoaded', function () {
        lightGallery(document.getElementById('lightgallery'), {
            plugins:    [lgZoom, lgThumbnail],
            licenseKey: '0000-0000-000-0000',
            speed:      500,
            // ... other settings
        });
    });
</script>