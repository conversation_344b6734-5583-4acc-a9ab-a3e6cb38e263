<?php
// get category id
$slug_parts  = explode('-', $segments[1]);
$serviciu_id = end($slug_parts);
$serviciu   = query("SELECT * FROM `servicii` WHERE `id` = :id", [
	'id' => $serviciu_id
])->fetch();

if (!$serviciu) {
	// Redirect to 404 page if serviciu not found, with js
	echo '<script>window.location.href = "' . ROOT_URL . '";</script>';
	exit;
}

?>
<main>
	<!-- service Details breadcrumb area start -->
	<section class="tp-breadcrumb__area pb-70 pt-50 p-relative z-index-1 fix">
		<div class="tp-breadcrumb__bg" data-background="<?= ASSETS_URL ?>/img/hero/3.png"></div>
		<div class="container">
			<div class="row align-items-center">
				<div class="col-sm-12">
					<div class="tp-breadcrumb__content text-center">
						<h3 class="tp-breadcrumb__title"><?= $serviciu['titlu_' . LANG] ?></h3>
					</div>
				</div>
			</div>
		</div>
	</section>
	<!-- service Details breadcrumb area end -->

	<?= render_breadcrumbs() ?>

	<!-- service details area start -->
	<section class="tp-service-detils-area pt-120 pb-70">
		<div class="container">
			<div class="row">
				<div class="col-lg-4">
					<div class="tp-service-details-wrapper-left">
						<div class="tp-service-details-box mb-50">
							<h4 class="tp-service-details-box-title"></h4>
							<?php
							$servicii = query("SELECT * FROM `servicii` WHERE `visible` = 1")->fetchAll();
							?>
							<ul>
								<?php foreach ($servicii

								as $_serviciu): ?>
								<li>
									<a <?php if ($_serviciu['id'] == $serviciu['id']) {
										echo 'class="active"';
									} ?> href="<?= ROOT_URL ?>/serviciu/<?= slugify($_serviciu['titlu_' . LANG], $_serviciu['id']) ?>"><?= $_serviciu['titlu_' . LANG] ?>
										<span>
											<i class="fa-solid fa-arrow-right"></i>
										</span>
									</a>
									<?php endforeach; ?>
							</ul>
						</div>
					</div>
				</div>
				<div class="col-lg-8">
					<div class="tp-service-details-wrapper">
						<?= $serviciu['content_' . LANG] ?>
						<h3 class="tp-service-details-title"><?= $content['general_contact_form_title'] ?></h3>
						<?php include(__DIR__ . '/sections/contact_form_section.php'); ?>
					</div>
				</div>
			</div>
		</div>
	</section>
	<!-- service details area end -->
</main>
