<main>
	<!-- Portfolio breadcrumb area start -->
	<section class="tp-breadcrumb__area pb-70 pt-50 p-relative z-index-1 fix">
		<div class="tp-breadcrumb__bg" data-background="<?= ASSETS_URL ?>/img/hero/3.png"></div>
		<div class="container">
			<div class="row align-items-center">
				<div class="col-sm-12">
					<div class="tp-breadcrumb__content text-center">
						<h3 class="tp-breadcrumb__title"><?= $content['general_portfolio_sp_page_title'] ?></h3>
					</div>
				</div>
			</div>
		</div>
	</section>
	<!-- Portfolio breadcrumb area end -->

	<?= render_breadcrumbs() ?>

	<!-- project area start -->
	<section class="tp-project-area tp-project-bottom-color p-relative pt-60 pb-90">
		<div class="container">
			<div class="row pb-30">
				<div class="col-lg-12">
					<p><?= $content['general_portfolio_sp_page_description'] ?></p>
				</div>
			</div>
			<div class="row justify-content-center">
				<?php
				$proiecte = query("SELECT * FROM `proiecte_speciale` WHERE `vizibil` = 1")->fetchAll();
				foreach ($proiecte as $proiect):
					$imagine = query("SELECT * FROM `proiecte_speciale_imagini` WHERE `id_proiect` = :id AND `is_default` = 1", [
						'id' => $proiect['id']
					])->fetch();
					?>
				<div class="col-lg-4 col-md-6 pb-3">
					<div class="tp-portfolio-thumb overflow-hidden">
						<img src="<?= ADMIN_ROOT . $imagine['image_path'] ?? '' ?>" alt="<?= get_image_alt('portfolio', $proiect, 'Proiect special') ?>">
						<div class="tp-portfolio-thumb-info">
							<p><?= $content['general_portfolio_sp_page_proiect'] ?></p>
							<h4 class="tp-portfolio-title">
								<a href="<?= ROOT_URL ?>/portfolio-special-details/<?= slugify($proiect['titlu_' . LANG], $proiect['id']) ?>"><?= $proiect['titlu_' . LANG] ?></a>
							</h4>
						</div>
						<div class="tp-portfolio-btn">
							<a href="<?= ROOT_URL ?>/portfolio-special-details/<?= slugify($proiect['titlu_' . LANG], $proiect['id']) ?>">
								<i class="fa-light fa-arrow-right"></i>
							</a>
						</div>
					</div>
				</div>
				<?php endforeach; ?>
			</div>
		</div>
	</section>
	<!-- project area end -->
</main>
