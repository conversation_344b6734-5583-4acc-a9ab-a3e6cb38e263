<main>
	<!-- new hero start -->
	<div id="showcase-slider-wrappper" class="showcase-slider-wrappper">
		<div class="port-showcase-slider-spaces p-relative">
			<div class="port-showcase-slider-wrap tp-slider-parallax fix" id="showcase-slider-holder" data-pattern-img="<?= ASSETS_URL ?>/img/slider/1.png">
				<div class="swiper-container parallax-slider-active p-relative" id="showcase-slider">
					<div class="swiper-wrapper" id="trigger-slides">
						<div class="tp-line-wrapper d-none d-md-block">
							<div class="tp-line-item"></div>
						</div>
						<div class="tp-hero-bg"></div>
						<div class="swiper-slide">
							<div class="slide-wrap active overlay" data-slide="0"></div>
							<div class="container">
								<div class="row">
									<div class="col-lg-8">
										<div class="tp-hero-content p-relative">
											<div class="tp-hero-title-wrapper">
												<div class="tp-hero-title-pre">
													<div>
														<span><?= $content['home_slide1_pre_title'] ?></span>
													</div>
												</div>
												<div class="tp-hero-title">
													<div>
														<span><?= $content['home_slide1_title_line1'] ?></span>
													</div>
												</div>
											</div>
											<div class="tp-hero-btn-wrap p-relative">
												<div class="tp-hero-button-wrapper d-flex flex-wrap align-items-between overflow-visible">
													<div class="d-flex flex-column flex-md-row">
														<div class="tp-hero-btn">
															<a class="tp-btn tp-icon-style" href="<?= ROOT_URL ?>/about">
																<?= $content['home_btn_text_1'] ?>
															</a>
														</div>
														<!-- Contact Button -->
														<div class="d-flex">
															<div class="tp-hero-btn style-2">
																<a class="tp-btn tp-icon-style" href="<?= ROOT_URL ?>/categorie/smoke-vents-1">
																	<span><?= $content['home_btn_text_2'] ?></span>
																</a>
															</div>
															<div class="tp-hero-btn d-sm-none">
																<div class="tp-video-popup">
																	<a class="popup-video pulse-button tp-icon-style justify-content-end" href="https://www.youtube.com/watch?v=6xUrmYp5cV8">
																		<span>
																			<i class="fa-regular fa-play text-white"></i>
																		</span>
																	</a>
																</div>
															</div>
														</div>
													</div>
													<div class="tp-hero-btn padding-hero-btn d-none d-sm-block">
														<div class="tp-video-popup">
															<a class="popup-video pulse-button tp-icon-style justify-content-end" href="https://www.youtube.com/watch?v=6xUrmYp5cV8">
																<span>
																	<i class="fa-regular fa-play text-white"></i>
																</span>
															</a>
														</div>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="tp-slider-dot d-md-none"></div>
				</div>
			</div>
		</div>
		<!-- canvas slider -->
		<div id="canvas-slider" class="canvas-slider">
			<div class="slider-img not-img" data-slide="0">
				<div class="slider-video-background">
					<div class="slider-video-foreground">
						<iframe class="w-100 h-100" src="https://www.youtube.com/embed/6xUrmYp5cV8?si=hdJs-QOf4AptHIN3&controls=0&autoplay=1&mute=1&playlist=6xUrmYp5cV8&loop=1" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
					</div>
				</div>
			</div>
		</div>
		<!--/canvas slider -->
	</div>
	<!-- new hero end -->
	<!-- about area start -->
	<?php include(__DIR__ . '/sections/about_section.php'); ?>
	<!-- about area end -->
	<!-- offer area start -->
	<section class="tp-brand-bg p-relative z-index-1 gray-bg-2 pt-60 pb-60">
		<div class="tp-offer-shape">
			<img src="<?= ASSETS_URL ?>/img/offer/offer-1-shape.png" alt="<?= get_image_alt('icon', null, 'Element decorativ secțiune produse') ?>">
		</div>
		<div class="container">
			<div class="row">
				<div class="col-lg-9">
					<div class="tp-offer-title-wrapper mb-45">
						<span class="tp-section-title-pre-2"><?= $content['home_products_pre_title'] ?></span>
						<h5 class="tp-section-title tp-section-size text-white"><?= $content['home_products_title'] ?></h5>
					</div>
				</div>
				<div class="col-lg-3"></div>
			</div>
			<div class="tp-offer-slider d-none d-sm-block">
				<div class="tp-offer-slider-btn text-end p-relative" style="">
					<div class="tp-offer-arrow-box d-none d-md-block w-100" style="">
						<button type="button" class="tp-offer-prev p-absolute">
							<i class="fa-regular fa-arrow-left"></i>
						</button>
						<button type="button" class="tp-offer-next p-absolute">
							<i class="fa-regular fa-arrow-right"></i>
						</button>
					</div>
				</div>
				<div class="tp-offer-active swiper">
					<div class="swiper-wrapper align-items-stretch">
						<?php
						$icons = [
							3 => ASSETS_URL . '/img/icon/trape-fum.png',
							5 => ASSETS_URL . '/img/icon/trape-ventilatie.png',
							6 => ASSETS_URL . '/img/icon/trape-de-vizitare.png',
							7 => ASSETS_URL . '/img/icon/fatade.png',
							8 => ASSETS_URL . '/img/icon/luminatoare.png',
							9 => ASSETS_URL . '/img/icon/copertine.png',
						];
						$id_produse = [3, 5, 6, 7, 8, 9];
							foreach ($id_produse as $id_produs) {
								$produs   = query("SELECT * FROM `produse` WHERE `id` = :id", ['id' => $id_produs])->fetch();
								$img = query("SELECT * FROM `produse_imagini` WHERE `id_produs` = :id AND `is_default` = 1", ['id' => $id_produs])->fetch();
						?>
						<div class="swiper-slide h-auto">
							<div class="tp-offer-item h-100">
								<div class="tp-offer-item-thumb">
<!--									<img src="--><?php //= ADMIN_ROOT . $img['image_path'] ?><!--" class="homepage-product-thumb" alt="">-->
									<div class="image-resizer-product-home" style="background-image: url('<?= ADMIN_ROOT . $img['image_path'] ?>')"></div>
								</div>
								<div class="tp-offer-content">
									<div class="tp-offer-icon">
										<img src="<?= $icons[$id_produs] ?>" alt="<?= get_image_alt('icon', $produs, 'Pictogramă produs') ?>" style="width: 35%;">
									</div>
									<div class="tp-offer-button d-flex align-items-center justify-content-between">
										<h4 class="tp-offer-title">
											<a href="<?= ROOT_URL ?>/produs/<?= slugify($produs['titlu_' . LANG], $produs['id']) ?>"><?= $produs['titlu_' . LANG] ?></a>
										</h4>
										<a href="<?= ROOT_URL ?>/produs/<?= slugify($produs['titlu_' . LANG], $produs['id']) ?>" class="tp-offer-btn tp-icon-style">
											<svg class="qodef-svg--custom-arrow qodef-m-arrow" xmlns="http://www.w3.org/2000/svg" width="14.2" height="14.2" viewBox="0 0 14.2 14.2">
												<g>
													<path d="M13.2 9V1h-8M13.4.8.7 13.5"></path>
													<path d="M13.2 9V1h-8M13.4.8.7 13.5"></path>
												</g>
												<g>
													<path d="M13.2 9V1h-8M13.4.8.7 13.5"></path>
													<path d="M13.2 9V1h-8M13.4.8.7 13.5"></path>
												</g>
											</svg>
										</a>
									</div>
								</div>
							</div>
						</div>
						<?php } ?>

					</div>
				</div>
				<div class="tp-offer-pagination"></div>
			</div>
			<div class=" d-block d-sm-none">
				<div class="tp-offer-active">
					<div class="">
						<?php
							foreach ($id_produse as $id_produs) {
								$produs   = query("SELECT * FROM `produse` WHERE `id` = :id", ['id' => $id_produs])->fetch();
								$img = query("SELECT * FROM `produse_imagini` WHERE `id_produs` = :id AND `is_default` = 1", ['id' => $id_produs])->fetch();
						?>
						<div class="swiper-slide h-auto mb-20">
							<div class="tp-offer-item">
								<div class="tp-offer-item-thumb">
									<div class="image-resizer-product-home-mobile" style="background-image: url('<?= ADMIN_ROOT . $img['image_path'] ?>')"></div>
								</div>
								<div class="tp-offer-content py-2">
									<div class="tp-offer-icon mb-0">
										<img src="<?= $icons[$id_produs] ?>" alt="<?= get_image_alt('icon', $produs, 'Pictogramă produs mobil') ?>" style="width: 24%;">
									</div>
									<div class="tp-offer-button d-flex align-items-center justify-content-between">
										<h4 class="tp-offer-title">
											<a href="<?= ROOT_URL ?>/produs/<?= slugify($produs['titlu_' . LANG], $produs['id']) ?>"><?= $produs['titlu_' . LANG] ?></a>
										</h4>
										<a href="<?= ROOT_URL ?>/produs/<?= slugify($produs['titlu_' . LANG], $produs['id']) ?>" class="tp-offer-btn tp-icon-style">
											<svg class="qodef-svg--custom-arrow qodef-m-arrow" xmlns="http://www.w3.org/2000/svg" width="14.2" height="14.2" viewBox="0 0 14.2 14.2">
												<g>
													<path d="M13.2 9V1h-8M13.4.8.7 13.5"></path>
													<path d="M13.2 9V1h-8M13.4.8.7 13.5"></path>
												</g>
												<g>
													<path d="M13.2 9V1h-8M13.4.8.7 13.5"></path>
													<path d="M13.2 9V1h-8M13.4.8.7 13.5"></path>
												</g>
											</svg>
										</a>
									</div>
								</div>
							</div>
						</div>
						<?php } ?>
					</div>
				</div>
				<div class="tp-offer-pagination"></div>
			</div>
		</div>
	</section>
	<!-- offer area end -->
	<!-- feature area start -->
	<section class="tp-feature-area p-relative pt-60" style="background-color: #ffeae0;">
		<div class="tp-feature-text">
			<h2 class="tp-feature-text-title"><?= $content['home_services_title'] ?></h2>
			<img class="tp-feature-shape-1" src="<?= ASSETS_URL ?>/img/feature/feature-1-shape.png" alt="<?= get_image_alt('icon', null, 'Element decorativ secțiune servicii') ?>">
		</div>
		<div class="container">
			<div class="row">
				<div class="col-lg-4">
					<div class="tp-feature-tab-btn">
						<ul class="nav nav-pills mb-60 justify-content-center wow fadeInUp" data-wow-duration="1s" data-wow-delay=".3s" id="pills-tab" role="tablist">
							<?php
							$features   = query("SELECT `key` FROM `static_content` 
								 WHERE `key` LIKE 'home_feature_pretitle_%' 
								 ORDER BY LENGTH(`key`), `key`")->fetchAll();
							$featuresIdx = [];
							foreach (array_column($features, 'key') as $key) {
								if (preg_match('/home_feature_pretitle_(\d+)/', $key, $matches)) {
									$featuresIdx[] = (int)$matches[1];
								}
							}
							sort($featuresIdx);
							foreach ($featuresIdx as $index) {
							?>
							<li class="nav-item" role="presentation">
								<div class="nav-link <?= $index == 1 ? 'active' : '' ?>" id="pills-feature-<?= $index ?>-tab" data-bs-toggle="pill" data-bs-target="#pills-feature-<?= $index ?>" role="tab" aria-controls="pills-feature-<?= $index ?>" aria-selected="<?= $index == 1 ? 'true' : 'false' ?>">
									<p><?= $content['home_feature_pretitle_' . $index] ?></p>
									<h3 class="tp-feature-tab-title"><?= $content['home_feature_title_' . $index] ?></h3>
								</div>
							</li>
							<?php } ?>
						</ul>
					</div>
				</div>
				<div class="col-lg-8">
					<div class="tp-feature-tab-content">
						<div class="tab-content" id="pills-tabContent">
							<?php foreach ($featuresIdx as $index) {
								// if empty, skip rendering
								if (empty($content['home_feature_image_' . $index]) || empty($content['home_feature_text_' . $index]) || empty($content['home_feature_link_' . $index])) {
									continue;
								}
								?>
							<div class="tab-pane fade <?= $index == 1 ? 'show active' : '' ?>" id="pills-feature-<?= $index ?>" role="tabpanel" aria-labelledby="pills-feature-<?= $index ?>-tab">
								<div class="tp-feature-tab-thumb">
									<img src="<?= $content['home_feature_image_' . $index] ?>" alt="<?= get_image_alt('service', null, $content['home_feature_text_' . $index]) ?>">
									<div class="tp-feature-tab-box p-2 p-md-5">
										<h4 class="tp-feature-tab-title"><?= $content['home_feature_text_' . $index] ?></h4>
										<a class="tp-icon-style" href="<?= $content['home_feature_link_' . $index] ?>">
											<span><?= $content['home_learn_more_text'] ?></span>
											<svg class="qodef-svg--custom-arrow qodef-m-arrow" xmlns="http://www.w3.org/2000/svg" width="14.2" height="14.2" viewBox="0 0 14.2 14.2">
												<g>
													<path d="M13.2 9V1h-8M13.4.8.7 13.5"></path>
													<path d="M13.2 9V1h-8M13.4.8.7 13.5"></path>
												</g>
												<g>
													<path d="M13.2 9V1h-8M13.4.8.7 13.5"></path>
													<path d="M13.2 9V1h-8M13.4.8.7 13.5"></path>
												</g>
											</svg>
										</a>
									</div>
								</div>
							</div>
							<?php } ?>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>
	<!-- feature area end -->
	<!-- portfolio area start -->
	<section class="tp-portfolio-area pt-50">
		<div class="container">
			<div class="row">
				<div class="col-lg-12">
					<div class="tp-offer-title-wrapper text-center mb-30 pt-4">
						<span class="tp-section-title-pre"><?= $content['home_portfolio_pre_title'] ?></span>
						<h3 class="tp-section-title"><?= $content['home_portfolio_title'] ?></h3>
					</div>
				</div>
			</div>
		</div>
		<div class="container-fluid gx-0">
			<div class="tp-portfolio-slider ">
				<div class="tp-offer-slider-btn text-end p-relative" style="">
					<div class="tp-offer-arrow-box w-100" style="">
						<button type="button" class="tp-offer-prev p-absolute">
							<i class="fa-regular fa-arrow-left"></i>
						</button>
						<button type="button" class="tp-offer-next p-absolute">
							<i class="fa-regular fa-arrow-right"></i>
						</button>
					</div>
				</div>
				<div class="tp-portfolio-active swiper slider-drag">
					<div class="swiper-wrapper">
						<?php
						$portfolio   = query("SELECT `id`, `titlu_" . LANG . "` AS `titlu` FROM `proiecte` WHERE `vizibil` = 1")->fetchAll();
						foreach ($portfolio as $item) {
							$img = query("SELECT * FROM `proiecte_imagini` WHERE `id_proiect` = :id AND `is_default` = 1", ['id' => $item['id']])->fetch();
						?>
						<div class="swiper-slide">
							<div class="tp-portfolio-thumb overflow-hidden">
								<img src="<?= ADMIN_ROOT . $img['image_path'] ?>" alt="">
								<div class="tp-portfolio-thumb-info">
									<p><?= $content['home_generic_proiect'] ?></p>
									<h4 class="tp-portfolio-title">
										<a href="<?= ROOT_URL . '/portfolio-details/' . slugify($item['titlu'], $item['id']) ?>"><?= $item['titlu'] ?></a>
									</h4>
								</div>
								<div class="tp-portfolio-btn">
									<a href="<?= ROOT_URL . '/portfolio-details/' . slugify($item['titlu'], $item['id']) ?>">
										<i class="fa-light fa-arrow-right"></i>
									</a>
								</div>
							</div>
						</div>
						<?php } ?>
					</div>
					<!--  <div class="swiper-button-prev"></div>-->
					<!--  <div class="swiper-button-next"></div>-->
				</div>
			</div>
		</div>
	</section>
	<!-- portfolio area end -->

	<!-- testimonials start -->
	<?php include(__DIR__ . '/sections/reviews_section.php'); ?>
	<!-- testimonials end -->
</main>