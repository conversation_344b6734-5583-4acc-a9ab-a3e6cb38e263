<?php
// get category id
$slug_parts  = explode('-', $segments[1]);
$category_id = end($slug_parts);
$categorie   = query("SELECT * FROM `categorii` WHERE `id` = :id", [
	'id' => $category_id
])->fetch();

if (!$categorie) {
	// Redirect to 404 page if category not found, with js
	echo '<script>window.location.href = "' . ROOT_URL . '";</script>';
	exit;
}

/**
 * Get products in a category and all its subcategories
 * @param int $categoryId The parent category ID
 * @return array Array of products
 */
function getProductsInCategory($categoryId) {
    // First get all child category IDs (recursively)
    $categoryIds = getChildCategoryIds($categoryId);
    $categoryIds[] = $categoryId; // Include the parent category

    // Prepare placeholders for the IN clause
    $placeholders = implode(',', array_fill(0, count($categoryIds), '?'));

    // Get products in these categories
    $query = "SELECT p.*
              FROM produse p
              JOIN categorii_produse cp ON p.id = cp.id_produs
              WHERE cp.id_categorie IN ($placeholders)
              AND p.visible = 1
              ORDER BY p.created_at DESC";

    $products = query($query, $categoryIds)->fetchAll();

    return $products;
}

/**
 * Recursively get all child category IDs
 * @param int $parentId The parent category ID
 * @return array Array of child category IDs
 */
function getChildCategoryIds($parentId) {
    $children = query(
        "SELECT id FROM categorii WHERE parent_id = ?",
        [$parentId]
    )->fetchAll(PDO::FETCH_COLUMN);

    $ids = [];
    foreach ($children as $childId) {
        $ids[] = $childId;
        $ids = array_merge($ids, getChildCategoryIds($childId));
    }

    return $ids;
}

?>
<main>
	<!-- about breadcrumb area start -->
	<section class="tp-breadcrumb__area pb-70 pt-50 p-relative z-index-1 fix">
		<div class="tp-breadcrumb__bg" data-background="<?= ASSETS_URL ?>/img/hero/3.png"></div>
		<div class="container">
			<div class="row align-items-center">
				<div class="col-sm-12">
					<div class="tp-breadcrumb__content text-center">
						<h3 class="tp-breadcrumb__title"><?= $categorie['titlu_' . LANG] ?></h3>
					</div>
				</div>
			</div>
		</div>
	</section>
	<!-- about breadcrumb area end -->

	<?= render_breadcrumbs() ?>
	<div class="breadcrumbs-category pt-50">
		<div class="container">
			<div class="d-md-flex gap-2 border-0 pb-0 mb-0">
				<?php
				$same_level = query("SELECT * FROM `categorii` WHERE `parent_id` = :parent_id ORDER BY `position`", [
					'parent_id' => $categorie['parent_id'],
				])->fetchAll();
				foreach ($same_level as $category) { ?>
					<a href="<?= ROOT_URL ?>/categorie/<?= slugify($category['titlu_' . LANG], $category['id']) ?>" class="breadcrumb-category-item <?php if ($category['id'] == $categorie['id']) { echo " product-add-cart-btn-3"; } ?> ">
						<?= $category['titlu_' . LANG] ?>
					</a>
				<?php } ?>
			</div>
		</div>
	</div>
	<section class="tp-blog-breadcrumb-area pt-30 pb-90">
		<div class="container">
			<div class="row pb-30">
				<div class="col-lg-12">
					<p><?= $categorie['descriere_' . LANG] ?></p>
				</div>
			</div>
			<div class="row">
				<?php
				/* list all products in this category or in all the children categories */
				$produse = getProductsInCategory($categorie['id']);
				?>
				<?php if (count($produse) > 0) { ?>
					<?php foreach ($produse as $produs) {
						$imagine = query("SELECT * FROM `produse_imagini` WHERE `id_produs` = :id_produs AND `is_default` = 1", [
							'id_produs' => $produs['id']
						])->fetch();
						?>
					<div class="col-lg-4 col-md-6">
						<div class="tp-blog-2-item p-relative mb-30">
							<div class="tp-blog-2-shape">
								<img src="<?= ASSETS_URL ?>/img/blog/home-2/blog-2-shape.png" alt="<?= get_image_alt('icon', null, 'Element decorativ produs') ?>">
							</div>
							<div class="tp-blog-2-thumb p-relative fix">
								<a href="<?= ROOT_URL ?>/produs/<?= slugify($produs['titlu_' . LANG], $produs['id']) ?>"><img src="<?= ADMIN_ROOT . $imagine['image_path'] ?>" alt="<?= get_image_alt('product', $produs) ?>"></a>
								<div class="tp-blog-2-thumb-icon">
									<a href="<?= ROOT_URL ?>/produs/<?= slugify($produs['titlu_' . LANG], $produs['id']) ?>">
										<i class="fa-regular fa-eye"></i>
									</a>
								</div>
							</div>
							<div class="tp-blog-2-content">
								<h4 class="tp-blog-2-title">
									<a href="<?= ROOT_URL ?>/produs/<?= slugify($produs['titlu_' . LANG], $produs['id']) ?>"><?= $produs['titlu_' . LANG] ?></a>
								</h4>
								<a class="tp-icon-style" href="<?= ROOT_URL ?>/produs/<?= slugify($produs['titlu_' . LANG], $produs['id']) ?>">
									<span><?= $content['general_produse_mai_multe_detalii'] ?></span>
									<svg class="qodef-svg--custom-arrow qodef-m-arrow" xmlns="http://www.w3.org/2000/svg" width="14.2" height="14.2" viewBox="0 0 14.2 14.2">
										<g>
											<path d="M13.2 9V1h-8M13.4.8.7 13.5"></path>
											<path d="M13.2 9V1h-8M13.4.8.7 13.5"></path>
										</g>
										<g>
											<path d="M13.2 9V1h-8M13.4.8.7 13.5"></path>
											<path d="M13.2 9V1h-8M13.4.8.7 13.5"></path>
										</g>
									</svg>
								</a>
							</div>
						</div>
					</div>
					<?php } ?>
				<?php } else { ?>
					<div class="col-lg-12">
						<div class="alert alert-warning text-center" role="alert">
							<?= $content['general_produse_no_products_found'] ?>
						</div>
					</div>
				<?php } ?>
			</div>
		</div>
	</section>
	<!-- counter area start -->
	<section class="tp-counter-2-area tp-counter-2-bg p-relative pt-90 pb-60">
		<div class="tp-counter-2-shape">
			<img src="<?= ASSETS_URL ?>/img/counter/counter-2-shape.png" alt="">
		</div>
		<div class="container">
			<div class="row align-items-center">
				<div class="col-lg-8 text-center text-md-start">
					<div class="tp-counter-2-wrapper p-relative mb-80">
						<h3 class="tp-counter-2-title"><?= $content['general_stats_section_title'] ?></h3>
					</div>
				</div>
				<div class="col-lg-4 ">
					<div class="tp-counter-2-btn p-relative d-flex justify-content-around justify-content-lg-end mb-80">
						<a class="tp-btn tp-icon-style" href="<?= ROOT_URL ?>/contact"><?= $content['general_stats_section_btn_text'] ?>
							<span>
								<svg class="qodef-svg--custom-arrow qodef-m-arrow" xmlns="http://www.w3.org/2000/svg" width="14.2" height="14.2" viewBox="0 0 14.2 14.2">
									<g>
										<path d="M13.2 9V1h-8M13.4.8.7 13.5"></path>
										<path d="M13.2 9V1h-8M13.4.8.7 13.5"></path>
									</g>
									<g>
										<path d="M13.2 9V1h-8M13.4.8.7 13.5"></path>
										<path d="M13.2 9V1h-8M13.4.8.7 13.5"></path>
									</g>
								</svg>
							</span>
						</a>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-lg-3 col-sm-6">
					<div class="tp-counter-2-item one text-center p-relative mb-30">
						<h4 class="tp-counter-2-item-title purecounter" data-purecounter-duration="2" data-purecounter-end="280">0</h4>
						<span><?= $content['general_stats_section_item1_text'] ?></span>
					</div>
				</div>
				<div class="col-lg-3 col-sm-6">
					<div class="tp-counter-2-item two text-center p-relative mb-30">
						<h4 class="tp-counter-2-item-title purecounter" data-purecounter-duration="1.6" data-purecounter-end="80">0</h4>
						<span class="w-75"><?= $content['general_stats_section_item2_text'] ?></span>
					</div>
				</div>
				<div class="col-lg-3 col-sm-6">
					<div class="tp-counter-2-item three text-center p-relative mb-30">
						<h4 class="tp-counter-2-item-title purecounter" data-purecounter-duration="1.2" data-purecounter-end="100">0</h4>
						<span class="w-75"><?= $content['general_stats_section_item3_text'] ?></span>
					</div>
				</div>
				<div class="col-lg-3 col-sm-6">
					<div class="tp-counter-2-item four text-center text-sm-end p-relative mb-30">
						<h4 class="tp-counter-2-item-title purecounter" data-purecounter-duration="0.8" data-purecounter-end="15">0</h4>
						<span class="text-center text-sm-end"><?= $content['general_stats_section_item4_text'] ?></span>
					</div>
				</div>
			</div>
		</div>
	</section>
	<!-- counter area end -->
	<!-- testimonials start -->
	<?php include(__DIR__ . '/sections/reviews_section.php'); ?>
	<!-- testimonials end -->
</main>
