<?php

class LazyContentLoader implements ArrayAccess {
	private $lang;
	private $loadedContent = [];

	public function __construct($lang) {
		$this->lang = $lang;
	}

	public function get($key) {
		if (!isset($this->loadedContent[$key])) {
			$content                   = query("SELECT `content_" . $this->lang . "` as `content` 
								 FROM `static_content` 
								 WHERE `key` = :key", [
				'key' => $key
			])->fetchColumn();
			// If content is not found, error_log it
			if (empty($content)) {
				error_log("Content for key '$key' not found in static_content table.");
			}
			$this->loadedContent[$key] = $content ?: $key;
		}
		// Format the content if needed
		$this->loadedContent[$key] = $this->formatContent($this->loadedContent[$key]);
		return $this->loadedContent[$key];
	}

	public function offsetExists($offset) {
		return true; // Always return true to prevent undefined index notices
	}

	public function offsetGet($offset) {
		if (!isset($this->loadedContent[$offset])) {
			$this->loadedContent[$offset] = $this->get($offset);
		}
		return $this->loadedContent[$offset];
	}

	public function offsetSet($offset, $value) {
		// Not implemented - read-only access
	}

	public function offsetUnset($offset) {
		// Not implemented - read-only access
	}

	private function formatContent($content) {
		if ($content && is_string($content) && strpos($content, 'IMG_PATH:') === 0) {
			// It's an image path, return it with URL_SUBFOLDER
			$path = substr($content, 9); // Remove "IMG_PATH:" prefix
			return ADMIN_ROOT . $path;
		} else {
			// convert new lines to HTML line breaks
//			$content = nl2br($content);
//			$content = str_replace("\n", "<br>", $content);
		}

		// It's regular content, return as is
		return $content;
	}
}