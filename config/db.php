<?php

// Database connection settings
$servername = "localhost";
$username = "root";
$password = "";
$database = "rokaflex";

$pdo = new PDO("mysql:host=$servername;dbname=$database", $username, $password);
$pdo->setAttribute(\PDO::ATTR_DEFAULT_FETCH_MODE, \PDO::FETCH_ASSOC);
$pdo->exec("set names utf8");

function query($sql, $params = []) {
    global $pdo;
    $stmt = $pdo->prepare($sql);
    try {
        $pdo->beginTransaction();
        $stmt->execute($params);
        $pdo->commit();
    } catch (\Exception $e) {
        $pdo->rollback();
        error_log(date("Y-m-d H:i:s") . ' | error pdo: ' . $e->getMessage() . "\n", 3, __DIR__ . '/PDO_error_log');
        throw $e;
    }
    return $stmt;
}