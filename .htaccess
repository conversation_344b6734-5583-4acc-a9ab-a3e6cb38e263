Options +FollowSymLinks -MultiViews
<IfModule mod_rewrite.c>
RewriteEngine On

RewriteCond %{HTTPS} off
RewriteRule ^ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Sitemap rewrite rule
RewriteRule ^sitemap\.xml$ sitemap.xml.php [L]

RewriteCond %{REQUEST_FILENAME} -f
# [OR]
#RewriteCond %{REQUEST_FILENAME} -d
RewriteRule ^ - [L]

RewriteRule ^(en|ro)/(.*)$ index.php?route=$2&lang=$1 [L,QSA]

RewriteRule ^(.*)$ index.php?route=$1&document_root=%{DOCUMENT_ROOT} [L,QSA]
</IfModule>