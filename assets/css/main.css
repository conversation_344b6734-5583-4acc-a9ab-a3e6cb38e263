@charset "UTF-8";
/*-----------------------------------------------------------------------------------

    Template Name: Dhora – Movie Studio & Filmmaker HTML Template
    Author: Theme_Pure
    Support: <EMAIL>
    Description: Dhora – Movie Studio & Filmmaker HTML Template
    Version: 1.0

-----------------------------------------------------------------------------------

/************ TABLE OF CONTENTS ***************

	-----------------
    01. THEME CSS
	-----------------
		1.1 Theme Default
		1.2 Common Classes
		1.3 Default Spacing

	-----------------
    02. COMPONENTS css
	-----------------
		2.1 Back to top
		2.2 Theme Settings
		2.3 Buttons
		2.4 Animations
		2.5 Preloader
		2.6 Background 
		2.7 Nice Select
		2.8 Pagination
		2.9 Offcanvas
		2.10 Breadcrumb
		2.11 Accordion
		2.12 Tab
		2.13 Modal
		2.14 Section Title
		2.15 Search
	-----------------
    03. HEADER CSS
	-----------------
		3.1 Header Style 1

    ---------------------------------
	04. MENU CSS
	---------------------------------
		4.1 Main menu css
		4.2 Mean menu css
		4.3 Mobile css

	---------------------------------
	05. BLOG CSS
	---------------------------------
		5.1 Postbox css
		5.2 Recent Post css
		5.3 Sidebar css

	---------------------------------
	06. FOOTER CSS
	---------------------------------
		6.1 Footer Style 1
		6.2 Footer Style 2
		6.3 Footer Style 3

	---------------------------------
	07. PAGES CSS
	---------------------------------
		7.1 Hero Css
		7.2 About Css
		7.3 Awerds
		7.4 Counter Css
		7.5 Video Play Css
		7.6 Brand Css
		7.7 Company Css
		7.8 Contact Css
		7.9 Cta Css
		8.0 Faq Css
		8.1 Feature Css
		8.2 History Css
		8.3 Offer Css
		8.4 Portfolio Css
		8.5 Pricing Css
		8.6 Project Css
		8.7 Service Css
		8.8 Shop Css
		8.9 Team Css
		9.0 Testimonial Css



**********************************************/
/*----------------------------------------*/
/*  1.1 Theme Default
/*----------------------------------------*/
@import url("https://fonts.googleapis.com/css2?family=DM+Sans:opsz,wght@9..40,400;9..40,500;9..40,600;9..40,700;9..40,800&family=Plus+Jakarta+Sans:wght@400;500;600;700;800&display=swap");

.product__thumb img, .product__details-thumb-nav .nav-link::after, .product__details-tab-nav .tp-tab-line, .tp-project-tab-button button::after, .tp-offer-item, .mean-container .mean-nav ul li > a.mean-expand i, .tp-product-tab-2 .nav-tabs .nav-link .tp-product-tab-tooltip, .tp-tab-line, .back-to-top-wrapper, a,
button,
p,
input,
select,
textarea,
li,
.transition-3 {
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-pagination ul li a svg, .tp-pagination ul li span svg, .tp-theme-toggle-main span svg {
    -webkit-transform: translateY(-2px);
    -moz-transform: translateY(-2px);
    -ms-transform: translateY(-2px);
    -o-transform: translateY(-2px);
    transform: translateY(-2px);
}

/* transform */
.tp-product-tab-2 .nav-tabs .nav-link:not(:first-child)::after {
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    transform: translateY(-50%);
}

:root {
    /**
  @font family declaration
  */
    --tp-ff-body: 'Plus Jakarta Sans', sans-serif;
    --tp-ff-heading: 'Plus Jakarta Sans', sans-serif;
    --tp-ff-p: 'DM Sans', sans-serif;
    --tp-ff-fontawesome: "Font Awesome 6 Pro";
    /**
  @color declaration
  */
    --tp-common-white: #ffffff;
    --tp-common-black: #000;
    --tp-common-gray: #FEF3DF;
    --tp-theme-primary: rgb(255, 94, 20);
    --tp-theme-secondary: rgb(0, 35, 90);
    --tp-theme-1: rgb(10, 18, 41);
    --tp-theme-2: #F94D1C;
    --tp-theme-3: #1B2335;
    --tp-theme-4: #eb003d;
    --tp-heading-primary: rgb(0, 35, 90);
    --tp-heading-secondary: #1F242C;
    --tp-heading-1: #2B2B5E;
    --tp-heading-2: #010d14;
    --tp-text-body: rgb(86, 89, 105);
    --tp-text-1: #E3E3E3;
    --tp-text-2: #f8f8f8;
    --tp-border-primary: #FEF3DF;
    --tp-border-secondary: #F0F2F4;
    --tp-border-1: #e6ebf0;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/*---------------------------------
	typography css start
---------------------------------*/
body {
    font-family: var(--tp-ff-body);
    font-size: 16px;
    font-weight: normal;
    color: var(--tp-text-body);
    line-height: 24px;
}

a {
    text-decoration: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: var(--tp-ff-heading);
    color: var(--tp-heading-primary);
    margin-top: 0px;
    font-weight: 600;
    line-height: 1.2;
    -webkit-transition: color 0.3s 0s ease-out;
    -moz-transition: color 0.3s 0s ease-out;
    -ms-transition: color 0.3s 0s ease-out;
    -o-transition: color 0.3s 0s ease-out;
    transition: color 0.3s 0s ease-out;
}

h1 {
    font-size: 40px;
}

h2 {
    font-size: 36px;
}

h3 {
    font-size: 28px;
}

h4 {
    font-size: 24px;
}

h5 {
    font-size: 20px;
}

h6 {
    font-size: 16px;
}

ul {
    margin: 0px;
    padding: 0px;
}

p {
    font-family: var(--tp-ff-p);
    font-size: 16px;
    font-weight: 400;
    line-height: 30px;
    margin-bottom: 0px;
    color: var(--tp-text-body);
}

a:not([href]):not([class]),
a:not([href]):not([class]):hover {
    color: inherit;
    text-decoration: none;
}

a:focus,
.button:focus {
    text-decoration: none;
    outline: none;
}

a:focus,
a:hover {
    color: inherit;
    text-decoration: none;
}

a,
button {
    color: inherit;
    outline: none;
    border: none;
    background: transparent;
}

button:hover {
    cursor: pointer;
}

button:focus {
    outline: 0;
}

.uppercase {
    text-transform: uppercase;
}

.capitalize {
    text-transform: capitalize;
}

input[type=text],
input[type=email],
input[type=tel],
input[type=number],
input[type=password],
input[type=url],
textarea {
    outline: none;
    background-color: #fff;
    height: 60px;
    width: 100%;
    line-height: 60px;
    font-size: 14px;
    color: var(--tp-common-black);
    padding-left: 26px;
    padding-right: 26px;
    border: 1px solid #E0E2E3;
}

input[type=text]::-webkit-input-placeholder,
input[type=email]::-webkit-input-placeholder,
input[type=tel]::-webkit-input-placeholder,
input[type=number]::-webkit-input-placeholder,
input[type=password]::-webkit-input-placeholder,
input[type=url]::-webkit-input-placeholder,
textarea::-webkit-input-placeholder { /* Chrome/Opera/Safari */
    color: #95999D;
}

input[type=text]::-moz-placeholder,
input[type=email]::-moz-placeholder,
input[type=tel]::-moz-placeholder,
input[type=number]::-moz-placeholder,
input[type=password]::-moz-placeholder,
input[type=url]::-moz-placeholder,
textarea::-moz-placeholder { /* Firefox 19+ */
    color: #95999D;
}

input[type=text]:-moz-placeholder,
input[type=email]:-moz-placeholder,
input[type=tel]:-moz-placeholder,
input[type=number]:-moz-placeholder,
input[type=password]:-moz-placeholder,
input[type=url]:-moz-placeholder,
textarea:-moz-placeholder { /* Firefox 4-18 */
    color: #95999D;
}

input[type=text]:-ms-input-placeholder,
input[type=email]:-ms-input-placeholder,
input[type=tel]:-ms-input-placeholder,
input[type=number]:-ms-input-placeholder,
input[type=password]:-ms-input-placeholder,
input[type=url]:-ms-input-placeholder,
textarea:-ms-input-placeholder { /* IE 10+  Edge*/
    color: #95999D;
}

input[type=text]::placeholder,
input[type=email]::placeholder,
input[type=tel]::placeholder,
input[type=number]::placeholder,
input[type=password]::placeholder,
input[type=url]::placeholder,
textarea::placeholder { /* MODERN BROWSER */
    color: #95999D;
}

[dir=rtl] input[type=text],
[dir=rtl] input[type=email],
[dir=rtl] input[type=tel],
[dir=rtl] input[type=number],
[dir=rtl] input[type=password],
[dir=rtl] input[type=url],
[dir=rtl] textarea {
    text-align: right;
}

input[type=text]:focus,
input[type=email]:focus,
input[type=tel]:focus,
input[type=number]:focus,
input[type=password]:focus,
input[type=url]:focus,
textarea:focus {
    border-color: var(--tp-common-black);
}

input[type=text]:focus::placeholder,
input[type=email]:focus::placeholder,
input[type=tel]:focus::placeholder,
input[type=number]:focus::placeholder,
input[type=password]:focus::placeholder,
input[type=url]:focus::placeholder,
textarea:focus::placeholder {
    opacity: 0;
}

textarea {
    line-height: 1.4;
    padding-top: 17px;
    padding-bottom: 17px;
}

input[type=color] {
    appearance: none;
    -moz-appearance: none;
    -webkit-appearance: none;
    background: none;
    border: 0;
    cursor: pointer;
    height: 100%;
    width: 100%;
    padding: 0;
    border-radius: 50%;
}

*::-moz-selection {
    background: var(--tp-common-black);
    color: var(--tp-common-white);
    text-shadow: none;
}

::-moz-selection {
    background: var(--tp-common-black);
    color: var(--tp-common-white);
    text-shadow: none;
}

::selection {
    background: var(--tp-common-black);
    color: var(--tp-common-white);
    text-shadow: none;
}

*::-moz-placeholder {
    color: var(--tp-common-black);
    font-size: 14px;
    opacity: 1;
}

*::placeholder {
    color: var(--tp-common-black);
    font-size: 14px;
    opacity: 1;
}

.tp-page-wrapper {
    position: relative;
    z-index: 2;
    margin-bottom: 578px;
    background: var(--tp-common-white);
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-page-wrapper {
        margin-bottom: 0;
    }
}

.tp-icon-style {
    display: inline-block;
    width: auto;
    height: auto;
    min-width: unset;
    aspect-ratio: unset;
    --qode-hover-move-x: 110%;
    --qode-hover-move-y: 100%;
    background-color: transparent;
    color: var(--tp-theme-primary);
    stroke-width: 2;
    fill: none;
}

.tp-icon-style svg {
    position: relative;
    display: inline-block;
    width: auto;
    height: 12px;
    margin-left: 5px;
    bottom: 0;
    flex-shrink: 0;
    stroke: currentColor;
}

.tp-icon-style svg g {
    transition: transform 0.38s cubic-bezier(0.37, 0.08, 0.02, 0.93), opacity 0.18s ease-out;
}

.tp-icon-style svg g:nth-of-type(1) {
    transform: translateX(0) translateY(0);
    opacity: 1;
    transition-delay: 0.15s, 0.15s;
}

.tp-icon-style svg g:nth-of-type(2) {
    transform: translateX(calc(-1 * var(--qode-hover-move-x))) translateY(var(--qode-hover-move-y));
    opacity: 0.5;
    transition-delay: 0s, 0s;
}

.tp-icon-style:hover {
    color: var(--tp-theme-primary);
    background-color: transparent;
}

.tp-icon-style:hover g:nth-of-type(1) {
    transform: translateX(var(--qode-hover-move-x)) translateY(calc(-1 * var(--qode-hover-move-y))) translateZ(0);
    opacity: 0;
    transition-delay: 0s, 0s;
}

.tp-icon-style:hover g:nth-of-type(2) {
    transform: translateX(0) translateY(0) translateZ(0);
    opacity: 1;
    transition-delay: 0.15s, 0.15s;
}

/*---------------------------------
    1.2 Common Classes
---------------------------------*/
.w-img img {
    width: 100%;
}

.m-img img {
    max-width: 100%;
}

.fix {
    overflow: hidden;
}

.clear {
    clear: both;
}

.z-index-1 {
    z-index: 1;
}

.z-index-11 {
    z-index: 11;
}

.overflow-y-visible {
    overflow-x: hidden;
    overflow-y: visible;
}

.p-relative {
    position: relative;
}

.p-absolute {
    position: absolute;
}

.include-bg {
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
}

.gray-bg {
    background: #f8f8f8;
}

.gray-bg-2 {
    background: #f6f6f6;
}

.blue-bg {
    background: var(--tp-theme-secondary);
}

.orange-bg {
    background: var(--tp-theme-primary);
}

.black-bg {
    background-color: rgb(18, 25, 45);
}

.grid__item-img {
    display: block;
    height: 500px;
    width: 500px;
}

@media (min-width: 1400px) {
    .container-large {
        max-width: 1812px;
    }
}

@media (min-width: 1400px) {
    .container-custom {
        max-width: 1325px;
    }
}

@media (min-width: 1400px) {
    .container-1750 {
        max-width: 1750px;
    }
}

.demo {
    -webkit-transition: color 0.3s 0s linear;
    -moz-transition: color 0.3s 0s linear;
    -ms-transition: color 0.3s 0s linear;
    -o-transition: color 0.3s 0s linear;
    transition: color 0.3s 0s linear;
    -webkit-transition: color 0.3s linear, transform 0.2s ease;
    -moz-transition: color 0.3s linear, transform 0.2s ease;
    -ms-transition: color 0.3s linear, transform 0.2s ease;
    -o-transition: color 0.3s linear, transform 0.2s ease;
    transition: color 0.3s linear, transform 0.2s ease;
}

[dir=rtl] .demo {
    margin-left: 0;
    margin-right: 15px;
}

div.demo img {
    margin-left: 10px;
}

.tp-cursor-point-area {
    cursor: none;
}

.mouseCursor {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    border-radius: 50%;
    transform: translateZ(0);
    visibility: hidden;
    text-align: center;
}

.mouseCursor.cursor-big {
    width: 60px;
    height: 60px;
}

.mouseCursor.cursor-big.cursor-outer {
    display: none;
}

.cursor-inner {
    margin-left: -3px;
    margin-top: -3px;
    width: 0px;
    height: 0px;
    z-index: 10000001;
    background-color: var(--tp-theme-secondary);
    transition: width 0.3s ease-in-out, height 0.3s ease-in-out, margin 0.3s ease-in-out, opacity 0.3s ease-in-out;
}

.cursor-inner span {
    line-height: 55px;
    opacity: 0;
    font-size: 20px;
    color: var(--tp-common-white);
}

.cursor-inner.cursor-big span {
    opacity: 1;
}

.effect::after {
    position: absolute;
    content: "";
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 0;
    border-radius: 100%;
    -webkit-animation: effectPlay 2s infinite;
    border: 2px solid rgb(255, 255, 255);
    box-shadow: 0px 14px 24px -1px rgba(20, 0, 77, 0.31);
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
}

.class {
    stroke-dasharray: 189px, 191px;
    stroke-dashoffset: 0px;
}

/* gutter for x axis */
.tp-gx-20 {
    --bs-gutter-x: 20px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-gx-20 {
        --bs-gutter-x: 20px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .tp-gx-20 {
        --bs-gutter-x: 20px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-gx-20 {
        --bs-gutter-x: 15px;
    }
}

@media (max-width: 575px) {
    .tp-gx-20 {
        --bs-gutter-x: 10px;
    }
}

.tp-gx-20 [class*=col-] {
    padding-right: calc(var(--bs-gutter-x) * 0.5);
    padding-left: calc(var(--bs-gutter-x) * 0.5);
    margin-top: var(--bs-gutter-y);
}

/* gutter for x axis */
.tp-gx-30 {
    --bs-gutter-x: 30px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-gx-30 {
        --bs-gutter-x: 30px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .tp-gx-30 {
        --bs-gutter-x: 30px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-gx-30 {
        --bs-gutter-x: 15px;
    }
}

@media (max-width: 575px) {
    .tp-gx-30 {
        --bs-gutter-x: 10px;
    }
}

.tp-gx-30 [class*=col-] {
    padding-right: calc(var(--bs-gutter-x) * 0.5);
    padding-left: calc(var(--bs-gutter-x) * 0.5);
    margin-top: var(--bs-gutter-y);
}

/* gutter for x axis */
.tp-gx-40 {
    --bs-gutter-x: 40px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-gx-40 {
        --bs-gutter-x: 40px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .tp-gx-40 {
        --bs-gutter-x: 30px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-gx-40 {
        --bs-gutter-x: 30px;
    }
}

@media (max-width: 575px) {
    .tp-gx-40 {
        --bs-gutter-x: 15px;
    }
}

.tp-gx-40 [class*=col-] {
    padding-right: calc(var(--bs-gutter-x) * 0.5);
    padding-left: calc(var(--bs-gutter-x) * 0.5);
    margin-top: var(--bs-gutter-y);
}

/*---------------------------------
    1.3 Default Spacing
---------------------------------*/
.pt-280 {
    padding-top: 280px;
}

.pt-210 {
    padding-top: 210px;
}

.pb-210 {
    padding-bottom: 210px;
}

.pb-210 {
    padding-bottom: 210px;
}

.pb-300 {
    padding-bottom: 300px;
}

.pb-350 {
    padding-bottom: 350px;
}

@media only screen and (min-width: 992px) {
    .pt-md-120 {
        padding-top: 120px !important;
    }
}

/*----------------------------------------*/
/*  2.1 Back to top
/*----------------------------------------*/
.back-to-top-wrapper {
    position: fixed;
    right: 50px;
    bottom: 0;
    height: 44px;
    width: 44px;
    cursor: pointer;
    display: block;
    border-radius: 50%;
    z-index: 99;
    opacity: 0;
    visibility: hidden;
}

@media (max-width: 575px) {
    .back-to-top-wrapper {
        right: 20px;
        bottom: 20px;
    }
}

.back-to-top-wrapper.back-to-top-btn-show {
    visibility: visible;
    opacity: 1;
    bottom: 50px;
}

.back-to-top-btn {
    display: inline-block;
    width: 44px;
    height: 44px;
    line-height: 44px;
    text-align: center;
    border-radius: 5px;
    background: var(--tp-theme-primary);
    box-shadow: 0px 8px 16px rgba(3, 4, 28, 0.3);
    color: var(--tp-common-white);
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.back-to-top-btn svg {
    -webkit-transform: translateY(-2px);
    -moz-transform: translateY(-2px);
    -ms-transform: translateY(-2px);
    -o-transform: translateY(-2px);
    transform: translateY(-2px);
}

.back-to-top-btn:hover {
    -webkit-transform: translateY(-4px);
    -moz-transform: translateY(-4px);
    -ms-transform: translateY(-4px);
    -o-transform: translateY(-4px);
    transform: translateY(-4px);
}

.back-to-top-btn.pink {
    background: var(--tp-theme-4);
}

/*----------------------------------------*/
/*  2.2 Theme Settings
/*----------------------------------------*/
.tp-theme-settings-area {
    position: fixed;
    top: 50%;
    left: 0;
    width: 240px;
    background-color: #fff;
    border: 1px solid #EAEAEF;
    -webkit-transform: translateY(-50%) translateX(-100%);
    -moz-transform: translateY(-50%) translateX(-100%);
    -ms-transform: translateY(-50%) translateX(-100%);
    -o-transform: translateY(-50%) translateX(-100%);
    transform: translateY(-50%) translateX(-100%);
    z-index: 991;
    direction: ltr;
    border-bottom-right-radius: 4px;
}

.tp-theme-settings-area.settings-opened {
    -webkit-transform: translateY(-50%) translateX(0%);
    -moz-transform: translateY(-50%) translateX(0%);
    -ms-transform: translateY(-50%) translateX(0%);
    -o-transform: translateY(-50%) translateX(0%);
    transform: translateY(-50%) translateX(0%);
}

.tp-theme-settings-area.settings-opened .tp-theme-settings-gear {
    opacity: 0;
}

.tp-theme-settings-area.settings-opened .tp-theme-settings-close {
    opacity: 1;
}

.tp-theme-settings-open {
    position: absolute;
    top: -1px;
    left: 100%;
}

.tp-theme-settings-open button {
    background-color: var(--tp-common-white);
    border: 1px solid #EAEAEF;
    border-left: 0;
    width: 60px;
    height: 60px;
    line-height: 60px;
    text-align: center;
    font-size: 24px;
    color: var(--tp-common-black);
    position: relative;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
}

.tp-theme-settings-gear {
    display: inline-block;
    -webkit-animation: tp-theme-setting-spin 4s linear infinite;
    -moz-animation: tp-theme-setting-spin 4s linear infinite;
    -ms-animation: tp-theme-setting-spin 4s linear infinite;
    -o-animation: tp-theme-setting-spin 4s linear infinite;
    animation: tp-theme-setting-spin 4s linear infinite;
}

@-webkit-keyframes tp-theme-setting-spin {
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@-moz-keyframes tp-theme-setting-spin {
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@-ms-keyframes tp-theme-setting-spin {
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@keyframes tp-theme-setting-spin {
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

.tp-theme-settings-close {
    display: inline-block;
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    opacity: 0;
}

.tp-theme-header-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 30px;
}

.tp-theme-wrapper {
    padding: 20px 30px 30px;
}

.tp-theme-toggle {
    text-align: center;
}

.tp-theme-toggle-main {
    display: inline-block;
    width: 74px;
    margin: auto;
    position: relative;
    z-index: 1;
    background-color: transparent;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 5px;
    border-radius: 100px;
}

.tp-theme-toggle-light, .tp-theme-toggle-dark {
    display: inline-block;
    width: 26px;
    height: 26px;
    line-height: 26px;
    color: #D9D9D9;
}

.tp-theme-toggle-light.active, .tp-theme-toggle-dark.active {
    color: var(--tp-common-black);
}

.tp-theme-toggle input {
    display: none;
}

.tp-theme-toggle:hover {
    cursor: pointer;
}

.tp-theme-toggle label {
    color: var(--tp-common-white);
    font-size: 14px;
    font-weight: 500;
}

.tp-theme-toggle label:hover {
    cursor: pointer;
}

.tp-theme-toggle #tp-theme-toggler {
    display: none;
}

.tp-theme-toggle #tp-theme-toggler:checked + i {
    right: calc(50% - 4px);
}

.tp-theme-toggle-slide {
    position: absolute;
    top: 50%;
    right: 4px;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    transform: translateY(-50%);
    width: 50%;
    height: 26px;
    color: var(--tp-common-black);
    background-color: var(--tp-common-white);
    border-radius: 30px;
    -webkit-transform: translate3d(0, 0);
    transform: translate3d(0, 0);
    -webkit-transition: all 0.2s 0s cubic-bezier(0.25, 1, 0.5, 1);
    -moz-transition: all 0.2s 0s cubic-bezier(0.25, 1, 0.5, 1);
    -ms-transition: all 0.2s 0s cubic-bezier(0.25, 1, 0.5, 1);
    -o-transition: all 0.2s 0s cubic-bezier(0.25, 1, 0.5, 1);
    transition: all 0.2s 0s cubic-bezier(0.25, 1, 0.5, 1);
    z-index: -1;
}

.tp-theme-dir {
    text-align: center;
}

.tp-theme-dir-main {
    display: inline-block;
    width: 160px;
    margin: auto;
    position: relative;
    z-index: 1;
    background-color: #f0f0f5;
    padding: 4px;
    border-radius: 20px;
}

.tp-theme-dir-ltr, .tp-theme-dir-rtl {
    display: inline-block;
    width: 48%;
    height: 26px;
    line-height: 26px;
}

.tp-theme-dir input {
    display: none;
}

.tp-theme-dir:hover {
    cursor: pointer;
}

.tp-theme-dir label {
    color: var(--tp-common-black);
    font-size: 14px;
    font-weight: 500;
}

.tp-theme-dir label:hover {
    cursor: pointer;
}

.tp-theme-dir #tp-dir-toggler {
    display: none;
}

.tp-theme-dir #tp-dir-toggler:checked + i {
    right: calc(50% - 4px);
}

.tp-theme-dir-slide {
    position: absolute;
    top: 50%;
    right: 4px;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    transform: translateY(-50%);
    width: 50%;
    height: 26px;
    color: var(--tp-common-black);
    background-color: var(--tp-common-white);
    border-radius: 30px;
    -webkit-transform: translate3d(0, 0);
    transform: translate3d(0, 0);
    -webkit-transition: all 0.2s 0s cubic-bezier(0.25, 1, 0.5, 1);
    -moz-transition: all 0.2s 0s cubic-bezier(0.25, 1, 0.5, 1);
    -ms-transition: all 0.2s 0s cubic-bezier(0.25, 1, 0.5, 1);
    -o-transition: all 0.2s 0s cubic-bezier(0.25, 1, 0.5, 1);
    transition: all 0.2s 0s cubic-bezier(0.25, 1, 0.5, 1);
    z-index: -1;
}

.tp-theme-color-item.active button::before {
    opacity: 1;
    visibility: visible;
}

.tp-theme-color-btn {
    width: 100%;
    height: 40px;
    line-height: 40px;
    text-align: center;
    position: relative;
}

.tp-theme-color-btn::before {
    position: absolute;
    content: "\f00c";
    font-weight: 600;
    font-family: var(--tp-ff-fontawesome);
    color: var(--tp-common-white);
    font-size: 16px;
    left: 50%;
    top: 50%;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    opacity: 0;
    visibility: hidden;
}

.tp-theme-color-btn.tp-color-settings-btn[data-color="#F50963"] {
    background-color: #F50963;
}

.tp-theme-color-btn.tp-color-settings-btn[data-color="#008080"] {
    background-color: #008080;
}

.tp-theme-color-btn.tp-color-settings-btn[data-color="#F31E5E"] {
    background-color: #F31E5E;
}

.tp-theme-color-btn.tp-color-settings-btn[data-color="#AB6C56"] {
    background-color: #AB6C56;
    color: black;
}

.tp-theme-color-btn.tp-color-settings-btn[data-color="#4353FF"] {
    background-color: #4353FF;
}

.tp-theme-color-btn.tp-color-settings-btn[data-color="#3661FC"] {
    background-color: #3661FC;
}

.tp-theme-color-btn.tp-color-settings-btn[data-color="#2CAE76"] {
    background-color: #2CAE76;
}

.tp-theme-color-btn.tp-color-settings-btn[data-color="#FF5A1B"] {
    background-color: #FF5A1B;
}

.tp-theme-color-btn.tp-color-settings-btn[data-color="#03041C"] {
    background-color: #03041C;
}

.tp-theme-color-btn.tp-color-settings-btn[data-color="#ED212C"] {
    background-color: #ED212C;
}

.tp-theme-color-input {
    margin-top: 15px;
}

.tp-theme-color-input h6 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 5px;
}

.tp-theme-color-input label {
    display: inline-block;
    width: 100%;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background-color: var(--tp-theme-1);
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-theme-color-input label:hover {
    cursor: pointer;
}

.tp-theme-color-input input {
    display: none;
}

/*----------------------------------------*/
/*  2.3 Buttons
/*----------------------------------------*/
.tp-btn {
    position: relative;
    display: inline-block;
    font-weight: 700;
    font-size: 16px;
    padding: 12px 18px;
    color: var(--tp-common-white);
    background-color: var(--tp-theme-primary);
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-btn span {
    display: inline-block;
    font-weight: 700;
    margin-left: 1px;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-btn:hover {
    color: var(--tp-common-white);
    background: var(--tp-theme-secondary);
}

.tp-btn:focus {
    color: var(--tp-common-white);
}

.tp-btn.pink {
    background: var(--tp-theme-4);
}

/* hamburger btn */
.tp-hamburger-btn {
    height: 16px;
    width: 22px;
    line-height: 16px;
    cursor: pointer;
    position: relative;
    display: inline-block;
}

.tp-hamburger-btn span {
    background: var(--tp-common-black);
    border-radius: 3px;
    content: "";
    position: absolute;
    width: 22px;
    height: 2px;
    left: 0;
    -webkit-transition: 0.3s ease-in-out;
    -moz-transition: 0.3s ease-in-out;
    -o-transition: 0.3s ease-in-out;
    transition: 0.3s ease-in-out;
}

.tp-hamburger-btn span::before, .tp-hamburger-btn span::after {
    background: var(--tp-common-black);
    border-radius: 3px;
    content: "";
    position: absolute;
    width: 10px;
    height: 2px;
    margin-top: 13px;
    left: 0;
    -webkit-transition: 0.3s ease-in-out;
    -moz-transition: 0.3s ease-in-out;
    -o-transition: 0.3s ease-in-out;
    transition: 0.3s ease-in-out;
}

.tp-hamburger-btn span::before {
    margin-top: -7px;
}

.tp-hamburger-btn span::after {
    margin-top: 7px;
    width: 13px;
}

.tp-hamburger-btn.active span {
    background: transparent;
}

.tp-hamburger-btn.active span::before {
    margin-top: 0;
    -webkit-transform: rotate(45deg);
    -moz-transform: rotate(45deg);
    -o-transform: rotate(45deg);
    transform: rotate(45deg);
}

.tp-hamburger-btn.active span::after {
    margin-top: 0;
    -webkit-transform: rotate(-45deg);
    -moz-transform: rotate(-45deg);
    -o-transform: rotate(-45deg);
    transform: rotate(-45deg);
}

.tp-hamburger-btn-white span {
    background-color: var(--tp-common-white);
}

.tp-hamburger-btn-white span::after, .tp-hamburger-btn-white span::before {
    background-color: var(--tp-common-white);
}

/*----------------------------------------*/
/*  2.7 Carousel
/*----------------------------------------*/
/** Swiper styles **/
.tp-showcase-arrow-box {
    position: absolute;
    top: 50%;
    right: 80px;
    z-index: 2;
    transform: translateY(-40%);
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .tp-showcase-arrow-box {
        right: 40px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-showcase-arrow-box {
        display: none;
    }
}

.tp-showcase-arrow-box button {
    display: block;
    margin-bottom: 10px;
    height: 60px;
    width: 60px;
    font-size: 20px;
    transform: translateY(-50%);
    color: var(--tp-common-white);
    background: var(--tp-theme-secondary);
    z-index: 2;
}

.tp-showcase-arrow-box button:hover {
    color: var(--tp-theme-secondary);
    background: var(--tp-common-white);
}

.tp-slider-dot {
    z-index: 3;
}

.showcase-slider-wrappper {
    height: 820px;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .showcase-slider-wrappper {
        height: 650px;
    }
}

.showcase-slider-wrappper .swiper-slide-active .tp-hero-3-content {
    opacity: 1;
}

.showcase-slider-wrappper .swiper-slide-active .tp-hero-title div span {
    opacity: 1;
    transform: translateY(0px);
    -webkit-transform: translateY(0px);
}

.parallax-slider-active.bg-shape::after {
    position: absolute;
    content: "";
    top: 0;
    left: 0;
    height: 100%;
    width: 50%;
    /*animation: tpLeftToRight 3s;*/
    background: rgba(255, 94, 20, 0.9);
    /*clip-path: polygon(0 0, 70% 0, 100% 100%, 0% 100%);*/
}

#canvas-slider {
    width: 100%;
    height: 100%;
    position: relative;
    top: 0;
    right: 0;
    overflow: hidden;
    -webkit-transition: width 0.5s ease-in-out 0;
    transition: width 0.5s ease-in-out 0s;
    z-index: 1;
}

#canvas-slider .slider-img {
    position: absolute;
    height: 100%;
    width: 100%;
    background-size: cover;
    background-position: center center;
    background-color: #222;
    /*visibility: hidden;*/
    top: 0;
    left: 0;
    z-index: 0;
    /*opacity: 0;*/
    -webkit-transform: scale(1.01);
    transform: scale(1.01);
    -webkit-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
}

#showcase-slider-holder {
    width: 100%;
    height: 820px;
    position: absolute;
    overflow: hidden;
    opacity: 1;
    z-index: 2;
}

.port-showcase-slider-item {
    height: 820px;
}

.tp-slider__thumb-bg {
    position: absolute;
    width: 100%;
    height: 820px;
    object-fit: cover;
}

.parallax-slider-active .swiper-slide {
    position: relative;
    overflow: hidden;
    width: 100%;
    height: 820px;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .parallax-slider-active .swiper-slide {
        height: 650px;
    }
}

.parallax-slider-active .swiper-container {
    width: 100%;
    height: 820px;
}

.swiper-container .tp-video-popup span {
    background: var(--tp-theme-primary);

}

/*----------------------------------------*/
/*  2.4 Animations
/*----------------------------------------*/
/* pulse effect animation */
@-webkit-keyframes tp-pulse {
    0% {
        -moz-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
    }
    70% {
        -moz-box-shadow: 0 0 0 45px rgba(255, 255, 255, 0);
        box-shadow: 0 0 0 45px rgba(255, 255, 255, 0);
    }
    100% {
        -moz-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    }
}

@-moz-keyframes tp-pulse {
    0% {
        -moz-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
    }
    70% {
        -moz-box-shadow: 0 0 0 45px rgba(255, 255, 255, 0);
        box-shadow: 0 0 0 45px rgba(255, 255, 255, 0);
    }
    100% {
        -moz-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    }
}

@-ms-keyframes tp-pulse {
    0% {
        -moz-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
    }
    70% {
        -moz-box-shadow: 0 0 0 45px rgba(255, 255, 255, 0);
        box-shadow: 0 0 0 45px rgba(255, 255, 255, 0);
    }
    100% {
        -moz-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    }
}

@keyframes tp-pulse {
    0% {
        -moz-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
    }
    70% {
        -moz-box-shadow: 0 0 0 45px rgba(255, 255, 255, 0);
        box-shadow: 0 0 0 45px rgba(255, 255, 255, 0);
    }
    100% {
        -moz-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    }
}

@-webkit-keyframes tp-pulse-2 {
    0% {
        -moz-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
    }
    70% {
        -moz-box-shadow: 0 0 0 45px rgba(255, 255, 255, 0);
        box-shadow: 0 0 0 45px rgba(255, 255, 255, 0);
    }
    100% {
        -moz-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    }
}

@-moz-keyframes tp-pulse-2 {
    0% {
        -moz-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
    }
    70% {
        -moz-box-shadow: 0 0 0 45px rgba(255, 255, 255, 0);
        box-shadow: 0 0 0 45px rgba(255, 255, 255, 0);
    }
    100% {
        -moz-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    }
}

@-ms-keyframes tp-pulse-2 {
    0% {
        -moz-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
    }
    70% {
        -moz-box-shadow: 0 0 0 45px rgba(255, 255, 255, 0);
        box-shadow: 0 0 0 45px rgba(255, 255, 255, 0);
    }
    100% {
        -moz-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    }
}

@keyframes tp-pulse-2 {
    0% {
        -moz-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
    }
    70% {
        -moz-box-shadow: 0 0 0 45px rgba(255, 255, 255, 0);
        box-shadow: 0 0 0 45px rgba(255, 255, 255, 0);
    }
    100% {
        -moz-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    }
}

@-webkit-keyframes tp-shake {
    10%, 90% {
        -webkit-transform: translate3d(-1px, 0, 0);
        transform: translate3d(-1px, 0, 0);
    }
    20%, 80% {
        -webkit-transform: translate3d(2px, 0, 0);
        transform: translate3d(2px, 0, 0);
    }
    30%, 50%, 70% {
        -webkit-transform: translate3d(-4px, 0, 0);
        transform: translate3d(-4px, 0, 0);
    }
    40%, 60% {
        -webkit-transform: translate3d(4px, 0, 0);
        transform: translate3d(4px, 0, 0);
    }
}

@-moz-keyframes tp-shake {
    10%, 90% {
        -webkit-transform: translate3d(-1px, 0, 0);
        transform: translate3d(-1px, 0, 0);
    }
    20%, 80% {
        -webkit-transform: translate3d(2px, 0, 0);
        transform: translate3d(2px, 0, 0);
    }
    30%, 50%, 70% {
        -webkit-transform: translate3d(-4px, 0, 0);
        transform: translate3d(-4px, 0, 0);
    }
    40%, 60% {
        -webkit-transform: translate3d(4px, 0, 0);
        transform: translate3d(4px, 0, 0);
    }
}

@-ms-keyframes tp-shake {
    10%, 90% {
        -webkit-transform: translate3d(-1px, 0, 0);
        transform: translate3d(-1px, 0, 0);
    }
    20%, 80% {
        -webkit-transform: translate3d(2px, 0, 0);
        transform: translate3d(2px, 0, 0);
    }
    30%, 50%, 70% {
        -webkit-transform: translate3d(-4px, 0, 0);
        transform: translate3d(-4px, 0, 0);
    }
    40%, 60% {
        -webkit-transform: translate3d(4px, 0, 0);
        transform: translate3d(4px, 0, 0);
    }
}

@keyframes tp-shake {
    10%, 90% {
        -webkit-transform: translate3d(-1px, 0, 0);
        transform: translate3d(-1px, 0, 0);
    }
    20%, 80% {
        -webkit-transform: translate3d(2px, 0, 0);
        transform: translate3d(2px, 0, 0);
    }
    30%, 50%, 70% {
        -webkit-transform: translate3d(-4px, 0, 0);
        transform: translate3d(-4px, 0, 0);
    }
    40%, 60% {
        -webkit-transform: translate3d(4px, 0, 0);
        transform: translate3d(4px, 0, 0);
    }
}

@-webkit-keyframes borderanimate2 {
    0% {
        transform: translate(-50%, -50%) scale(0.8);
    }
    60% {
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(2.5);
        opacity: 0;
    }
}

@-moz-keyframes borderanimate2 {
    0% {
        transform: translate(-50%, -50%) scale(0.8);
    }
    60% {
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(2.5);
        opacity: 0;
    }
}

@-ms-keyframes borderanimate2 {
    0% {
        transform: translate(-50%, -50%) scale(0.8);
    }
    60% {
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(2.5);
        opacity: 0;
    }
}

@keyframes borderanimate2 {
    0% {
        transform: translate(-50%, -50%) scale(0.8);
    }
    60% {
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(2.5);
        opacity: 0;
    }
}

@-webkit-keyframes tp-rotate-center {
    0% {
        -webkit-transform: rotate(0);
        -moz-transform: rotate(0);
        -ms-transform: rotate(0);
        -o-transform: rotate(0);
        transform: rotate(0);
    }
    100% {
        -webkit-transform: rotate(360deg);
        -moz-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        -o-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@-moz-keyframes tp-rotate-center {
    0% {
        -webkit-transform: rotate(0);
        -moz-transform: rotate(0);
        -ms-transform: rotate(0);
        -o-transform: rotate(0);
        transform: rotate(0);
    }
    100% {
        -webkit-transform: rotate(360deg);
        -moz-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        -o-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@-ms-keyframes tp-rotate-center {
    0% {
        -webkit-transform: rotate(0);
        -moz-transform: rotate(0);
        -ms-transform: rotate(0);
        -o-transform: rotate(0);
        transform: rotate(0);
    }
    100% {
        -webkit-transform: rotate(360deg);
        -moz-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        -o-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@keyframes tp-rotate-center {
    0% {
        -webkit-transform: rotate(0);
        -moz-transform: rotate(0);
        -ms-transform: rotate(0);
        -o-transform: rotate(0);
        transform: rotate(0);
    }
    100% {
        -webkit-transform: rotate(360deg);
        -moz-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        -o-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@-webkit-keyframes tp-mobile-view {
    0%, 10% {
        -webkit-transform: translateY(0%);
        -moz-transform: translateY(0%);
        -ms-transform: translateY(0%);
        -o-transform: translateY(0%);
        transform: translateY(0%);
    }
    50%, 60% {
        -webkit-transform: translateY(-57%);
        -moz-transform: translateY(-57%);
        -ms-transform: translateY(-57%);
        -o-transform: translateY(-57%);
        transform: translateY(-57%);
    }
    90%, 100% {
        -webkit-transform: translateY(0%);
        -moz-transform: translateY(0%);
        -ms-transform: translateY(0%);
        -o-transform: translateY(0%);
        transform: translateY(0%);
    }
}

@-moz-keyframes tp-mobile-view {
    0%, 10% {
        -webkit-transform: translateY(0%);
        -moz-transform: translateY(0%);
        -ms-transform: translateY(0%);
        -o-transform: translateY(0%);
        transform: translateY(0%);
    }
    50%, 60% {
        -webkit-transform: translateY(-57%);
        -moz-transform: translateY(-57%);
        -ms-transform: translateY(-57%);
        -o-transform: translateY(-57%);
        transform: translateY(-57%);
    }
    90%, 100% {
        -webkit-transform: translateY(0%);
        -moz-transform: translateY(0%);
        -ms-transform: translateY(0%);
        -o-transform: translateY(0%);
        transform: translateY(0%);
    }
}

@-ms-keyframes tp-mobile-view {
    0%, 10% {
        -webkit-transform: translateY(0%);
        -moz-transform: translateY(0%);
        -ms-transform: translateY(0%);
        -o-transform: translateY(0%);
        transform: translateY(0%);
    }
    50%, 60% {
        -webkit-transform: translateY(-57%);
        -moz-transform: translateY(-57%);
        -ms-transform: translateY(-57%);
        -o-transform: translateY(-57%);
        transform: translateY(-57%);
    }
    90%, 100% {
        -webkit-transform: translateY(0%);
        -moz-transform: translateY(0%);
        -ms-transform: translateY(0%);
        -o-transform: translateY(0%);
        transform: translateY(0%);
    }
}

@keyframes tp-mobile-view {
    0%, 10% {
        -webkit-transform: translateY(0%);
        -moz-transform: translateY(0%);
        -ms-transform: translateY(0%);
        -o-transform: translateY(0%);
        transform: translateY(0%);
    }
    50%, 60% {
        -webkit-transform: translateY(-57%);
        -moz-transform: translateY(-57%);
        -ms-transform: translateY(-57%);
        -o-transform: translateY(-57%);
        transform: translateY(-57%);
    }
    90%, 100% {
        -webkit-transform: translateY(0%);
        -moz-transform: translateY(0%);
        -ms-transform: translateY(0%);
        -o-transform: translateY(0%);
        transform: translateY(0%);
    }
}

@-webkit-keyframes tp-svg-line {
    100% {
        stroke-dashoffset: 350;
    }
}

@-moz-keyframes tp-svg-line {
    100% {
        stroke-dashoffset: 350;
    }
}

@-ms-keyframes tp-svg-line {
    100% {
        stroke-dashoffset: 350;
    }
}

@keyframes tp-svg-line {
    100% {
        stroke-dashoffset: 350;
    }
}

@-webkit-keyframes tp-border-loader {
    0% {
        stroke-dashoffset: -356px;
        stroke-dasharray: 356px, 366px;
    }
    95% {
        stroke-dashoffset: 0;
        stroke-dasharray: 356px, 366px;
    }
    100% {
        stroke-dashoffset: 0;
        stroke-dasharray: 0, 366px;
    }
}

@-moz-keyframes tp-border-loader {
    0% {
        stroke-dashoffset: -356px;
        stroke-dasharray: 356px, 366px;
    }
    95% {
        stroke-dashoffset: 0;
        stroke-dasharray: 356px, 366px;
    }
    100% {
        stroke-dashoffset: 0;
        stroke-dasharray: 0, 366px;
    }
}

@-ms-keyframes tp-border-loader {
    0% {
        stroke-dashoffset: -356px;
        stroke-dasharray: 356px, 366px;
    }
    95% {
        stroke-dashoffset: 0;
        stroke-dasharray: 356px, 366px;
    }
    100% {
        stroke-dashoffset: 0;
        stroke-dasharray: 0, 366px;
    }
}

@keyframes tp-border-loader {
    0% {
        stroke-dashoffset: -356px;
        stroke-dasharray: 356px, 366px;
    }
    95% {
        stroke-dashoffset: 0;
        stroke-dasharray: 356px, 366px;
    }
    100% {
        stroke-dashoffset: 0;
        stroke-dasharray: 0, 366px;
    }
}

@-moz-keyframes headerSlideDown {
    0% {
        margin-top: -150px;
    }
    100% {
        margin-top: 0;
    }
}

@-ms-keyframes headerSlideDown {
    0% {
        margin-top: -150px;
    }
    100% {
        margin-top: 0;
    }
}

@-webkit-keyframes headerSlideDown {
    0% {
        margin-top: -150px;
    }
    100% {
        margin-top: 0;
    }
}

@keyframes headerSlideDown {
    0% {
        margin-top: -150px;
    }
    100% {
        margin-top: 0;
    }
}

@-moz-keyframes headerSlideUp {
    0% {
        margin-top: 0;
    }
    100% {
        margin-top: -150px;
    }
}

@-ms-keyframes headerSlideUp {
    0% {
        margin-top: 0;
    }
    100% {
        margin-top: -150px;
    }
}

@-webkit-keyframes headerSlideUp {
    0% {
        margin-top: 0;
    }
    100% {
        margin-top: -150px;
    }
}

@keyframes headerSlideUp {
    0% {
        margin-top: 0;
    }
    100% {
        margin-top: -150px;
    }
}

@keyframes icon-bounce {
    0%, 100%, 20%, 50%, 80% {
        -webkit-transform: translateY(0);
        -moz-transform: translateY(0);
        -ms-transform: translateY(0);
        -o-transform: translateY(0);
        transform: translateY(0);
    }
    40% {
        -webkit-transform: translateY(-10px);
        -moz-transform: translateY(-10px);
        -ms-transform: translateY(-10px);
        -o-transform: translateY(-10px);
        transform: translateY(-10px);
    }
    60% {
        -webkit-transform: translateY(-5px);
        -moz-transform: translateY(-5px);
        -ms-transform: translateY(-5px);
        -o-transform: translateY(-5px);
        transform: translateY(-5px);
    }
}

/* 04. ========= btn-arrow-animation ========== */
@-webkit-keyframes tfLeftToRight {
    49% {
        transform: translateX(30%);
    }
    50% {
        opacity: 0;
        transform: translateX(-30%);
    }
    100% {
        opacity: 1;
    }
}

@-moz-keyframes tfLeftToRight {
    49% {
        transform: translateX(30%);
    }
    50% {
        opacity: 0;
        transform: translateX(-30%);
    }
    100% {
        opacity: 1;
    }
}

@-ms-keyframes tfLeftToRight {
    49% {
        transform: translateX(30%);
    }
    50% {
        opacity: 0;
        transform: translateX(-30%);
    }
    100% {
        opacity: 1;
    }
}

@keyframes tfLeftToRight {
    49% {
        transform: translateX(30%);
    }
    50% {
        opacity: 0;
        transform: translateX(-30%);
    }
    100% {
        opacity: 1;
    }
}

@-webkit-keyframes tpLeftToRight {
    0% {
        transform: translateX(-20%);
    }
    100% {
        transform: translateX(0%);
    }
}

@-webkit-keyframes shine {
    100% {
        left: 125%;
    }
}

@-moz-keyframes shine {
    100% {
        left: 125%;
    }
}

@-ms-keyframes shine {
    100% {
        left: 125%;
    }
}

@keyframes shine {
    100% {
        left: 125%;
    }
}

@keyframes effectPlay {
    70% {
        opacity: 0;
        -webkit-transform: scale(1.6);
        transform: scale(1.6);
    }
    100% {
        opacity: 0;
        -webkit-transform: scale(0);
        transform: scale(0);
    }
}

@keyframes moveAlways {
    0% {
        transform: translate(0px, 0px);
    }
    20% {
        transform: translate(20px, -5px);
    }
    40% {
        transform: translate(50px, 20px);
    }
    60% {
        transform: translate(20px, 50px);
    }
    80% {
        transform: translate(-20px, 30px);
    }
    100% {
        transform: translate(0px, 0px);
    }
}

@keyframes scale_up_down {
    0% {
        -webkit-transform: scale(0.9);
        transform: scale(0.9);
    }
    100% {
        -webkit-transform: scale(1);
        transform: scale(1);
    }
}

@keyframes borderanimate {
    0% {
        transform: translate(-50%, -50%) scale(0.8);
    }
    60% {
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0;
    }
}

@-webkit-keyframes borderanimate {
    0% {
        transform: translate(-50%, -50%) scale(0.8);
    }
    60% {
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0;
    }
}

@keyframes borderanimate2 {
    0% {
        transform: translate(-50%, -50%) scale(1.1);
    }
    60% {
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0;
    }
}

@-webkit-keyframes borderanimate2 {
    0% {
        transform: translate(-50%, -50%) scale(1.1);
    }
    60% {
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0;
    }
}

@keyframes hoverfilter {
    0% {
        filter: brightness(100%) blur(0px);
    }
    25% {
        filter: brightness(125%) blur(2px);
    }
    100% {
        filter: brightness(100%) blur(0px);
    }
}

@keyframes tp-grain {
    0%, 100% {
        transform: translate(0, 0);
    }
    10% {
        transform: translate(-5%, -10%);
    }
    30% {
        transform: translate(3%, -15%);
    }
    50% {
        transform: translate(12%, 9%);
    }
    70% {
        transform: translate(9%, 4%);
    }
    90% {
        transform: translate(-1%, 7%);
    }
}

@-webkit-keyframes scrollText {
    from {
        transform: translateX(0%);
    }
    to {
        transform: translateX(-50%);
    }
}

@keyframes scrollText {
    from {
        transform: translateX(0%);
    }
    to {
        transform: translateX(-50%);
    }
}

@keyframes tptranslateX2 {
    0% {
        -webkit-transform: translateX(-30px);
        -moz-transform: translateX(-30px);
        -ms-transform: translateX(-30px);
        -o-transform: translateX(-30px);
        transform: translateX(-30px);
    }
    100% {
        -webkit-transform: translatXY(20px);
        -moz-transform: translateX(20px);
        -ms-transform: translateX(20px);
        -o-transform: translateX(20px);
        transform: translateX(20px);
    }
}

@keyframes updown-two {
    0% {
        transform: translateY(-20px);
    }
    100% {
        transform: translateY(0px);
    }
}

@-webkit-keyframes line_anim {
    0% {
        top: 0;
        opacity: 1;
    }
    50% {
        top: 50%;
    }
    90% {
        opacity: 1;
    }
    100% {
        top: 100%;
        opacity: 0.5;
    }
}

@-moz-keyframes line_anim {
    0% {
        top: 0;
        opacity: 1;
    }
    50% {
        top: 50%;
    }
    90% {
        opacity: 1;
    }
    100% {
        top: 100%;
        opacity: 0.5;
    }
}

@-ms-keyframes line_anim {
    0% {
        top: 0;
        opacity: 1;
    }
    50% {
        top: 50%;
    }
    90% {
        opacity: 1;
    }
    100% {
        top: 100%;
        opacity: 0.5;
    }
}

@keyframes line_anim {
    0% {
        top: 0;
        opacity: 1;
    }
    50% {
        top: 50%;
    }
    90% {
        opacity: 1;
    }
    100% {
        top: 100%;
        opacity: 0.5;
    }
}

@-webkit-keyframes line_anim_2 {
    0% {
        opacity: 1;
        bottom: 0;
    }
    50% {
        bottom: 50%;
    }
    90% {
        opacity: 1;
    }
    100% {
        bottom: 100%;
        opacity: 0.5;
    }
}

@-moz-keyframes line_anim_2 {
    0% {
        opacity: 1;
        bottom: 0;
    }
    50% {
        bottom: 50%;
    }
    90% {
        opacity: 1;
    }
    100% {
        bottom: 100%;
        opacity: 0.5;
    }
}

@-ms-keyframes line_anim_2 {
    0% {
        opacity: 1;
        bottom: 0;
    }
    50% {
        bottom: 50%;
    }
    90% {
        opacity: 1;
    }
    100% {
        bottom: 100%;
        opacity: 0.5;
    }
}

@keyframes line_anim_2 {
    0% {
        opacity: 1;
        bottom: 0;
    }
    50% {
        bottom: 50%;
    }
    90% {
        opacity: 1;
    }
    100% {
        bottom: 100%;
        opacity: 0.5;
    }
}

@keyframes circle-animations {
    0% {
        -webkit-transform: rotate(0deg);
        -moz-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        -o-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        -moz-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        -o-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@keyframes MoveUpInitial {
    100% {
        transform: translate3d(0, -105%, 0);
    }
    0% {
        transform: translate3d(0, 100%, 0);
    }
    100% {
        transform: translate3d(0, 0, 0);
    }
}

/*----------------------------------------*/
/*  2.5 Preloader
/*----------------------------------------*/
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    #loading-center-absolute {
        width: 40%;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    #loading-center-absolute {
        width: 40%;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    #loading-center-absolute {
        width: 45%;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    #loading-center-absolute {
        width: 50%;
    }
}

@media (max-width: 575px) {
    #loading-center-absolute {
        width: 90%;
    }
}

#loading {
    background-color: #fff;
    height: 100%;
    width: 100%;
    position: fixed;
    z-index: 999999;
    margin-top: 0px;
    top: 0px;
}

#loading-center {
    width: 100%;
    height: 100%;
    position: relative;
}

#loading-center-absolute {
    position: absolute;
    left: 50%;
    top: 50%;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}

.tp-preloader-logo {
    width: 180px;
    height: 180px;
    line-height: 180px;
    position: relative;
    text-align: center;
    margin: auto;
}

.tp-preloader-circle {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.tp-preloader-circle svg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    -webkit-animation: tp-rotate 5s linear infinite;
    -moz-animation: tp-rotate 5s linear infinite;
    -ms-animation: tp-rotate 5s linear infinite;
    -o-animation: tp-rotate 5s linear infinite;
    animation: tp-rotate 5s linear infinite;
}

.tp-preloader-circle svg circle:last-child {
    stroke: var(--tp-theme-primary);
    stroke-dashoffset: 0;
    stroke-dasharray: 1128, 3150;
    -webkit-animation: tp-loading 4s linear infinite;
    -moz-animation: tp-loading 4s linear infinite;
    -ms-animation: tp-loading 4s linear infinite;
    -o-animation: tp-loading 4s linear infinite;
    animation: tp-loading 4s linear infinite;
    transform-origin: center center;
}

@-webkit-keyframes tp-loading {
    0% {
        stroke-dashoffset: 0;
        stroke-dasharray: 0, 3150;
    }
    100% {
        stroke-dashoffset: -1131;
        stroke-dasharray: 1128, 3138;
    }
}

@-moz-keyframes tp-loading {
    0% {
        stroke-dashoffset: 0;
        stroke-dasharray: 0, 3150;
    }
    100% {
        stroke-dashoffset: -1131;
        stroke-dasharray: 1128, 3138;
    }
}

@-ms-keyframes tp-loading {
    0% {
        stroke-dashoffset: 0;
        stroke-dasharray: 0, 3150;
    }
    100% {
        stroke-dashoffset: -1131;
        stroke-dasharray: 1128, 3138;
    }
}

@keyframes tp-loading {
    0% {
        stroke-dashoffset: 0;
        stroke-dasharray: 0, 3150;
    }
    100% {
        stroke-dashoffset: -1131;
        stroke-dasharray: 1128, 3138;
    }
}

@-webkit-keyframes tp-rotate {
    0% {
        -webkit-transform: rotate(0deg);
        -moz-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        -o-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        -moz-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        -o-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@-moz-keyframes tp-rotate {
    0% {
        -webkit-transform: rotate(0deg);
        -moz-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        -o-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        -moz-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        -o-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@-ms-keyframes tp-rotate {
    0% {
        -webkit-transform: rotate(0deg);
        -moz-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        -o-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        -moz-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        -o-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@keyframes tp-rotate {
    0% {
        -webkit-transform: rotate(0deg);
        -moz-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        -o-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        -moz-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        -o-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

.tp-preloader-circle img {
    vertical-align: middle;
}

.tp-preloader-content {
    text-align: center;
}

.tp-preloader-title {
    font-size: 100px;
    font-weight: 500;
    line-height: 1;
    margin-bottom: 0px;
}

@media (max-width: 575px) {
    .tp-preloader-title {
        font-size: 70px;
    }
}

.tp-preloader-subtitle {
    font-size: 16px;
    margin-bottom: 0;
    color: var(--tp-common-black);
}

/*----------------------------------------*/
/*  2.6 Background
/*----------------------------------------*/
.grey-bg {
    background-color: var(--tp-grey-1);
}

.grey-bg-2 {
    background-color: var(--tp-grey-2);
}

.grey-bg-5 {
    background-color: var(--tp-grey-5);
}

.grey-bg-6 {
    background-color: var(--tp-grey-6);
}

.grey-bg-7 {
    background-color: var(--tp-grey-7);
}

.grey-bg-8 {
    background-color: var(--tp-grey-8);
}

.grey-bg-9 {
    background-color: var(--tp-grey-9);
}

.white-bg {
    background-color: var(--tp-common-white);
}

.black-bg {
    background-color: var(--tp-common-black);
}

.khaki-bg {
    background-color: var(--tp-khaki-1);
}

.green-dark-bg {
    background-color: var(--tp-green-dark);
}

.theme-bg {
    background-color: var(--tp-theme-primary);
}

[data-bg-color=footer-bg-grey] {
    background-color: var(--tp-footer-grey-1);
}

[data-bg-color=footer-bg-white] {
    background-color: var(--tp-common-white);
}

/*----------------------------------------*/
/*  2.8 Nice Select
/*----------------------------------------*/
/*----------------------------------------*/
/*  2.9 Pagination
/*----------------------------------------*/
.tp-pagination ul {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
}

.tp-pagination ul li {
    display: inline-block;
}

.tp-pagination ul li:not(:last-child) {
    margin-right: 6px;
}

.tp-pagination ul li a, .tp-pagination ul li span {
    display: inline-block;
    width: 40px;
    height: 40px;
    line-height: 38px;
    text-align: center;
    border: 1px solid rgba(2, 11, 24, 0.1);
    font-size: 16px;
    font-weight: 500;
    color: var(--tp-text-body);
}

.tp-pagination ul li a:hover, .tp-pagination ul li a.current, .tp-pagination ul li span:hover, .tp-pagination ul li span.current {
    background: var(--tp-theme-primary);
    border-color: var(--tp-theme-primary);
    color: var(--tp-common-white);
}

.tp-offer-pagination .swiper-horizontal > .swiper-pagination-bullets, .swiper-pagination-bullets.swiper-pagination-horizontal, .swiper-pagination-custom, .swiper-pagination-fraction {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 50px;
    width: auto;
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-offer-pagination .swiper-horizontal > .swiper-pagination-bullets, .swiper-pagination-bullets.swiper-pagination-horizontal, .swiper-pagination-custom, .swiper-pagination-fraction {
        bottom: 80px;
    }
}

@media (max-width: 575px) {
    .tp-offer-pagination .swiper-horizontal > .swiper-pagination-bullets, .swiper-pagination-bullets.swiper-pagination-horizontal, .swiper-pagination-custom, .swiper-pagination-fraction {
        bottom: 35px;
    }
}

.tp-offer-pagination .swiper-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet, .swiper-pagination-bullets.swiper-pagination-horizontal .swiper-pagination-bullet, .swiper-pagination-custom .swiper-pagination-bullet, .swiper-pagination-fraction .swiper-pagination-bullet {
    width: 12px;
    height: 12px;
    background: #dadada;
    display: inline-block;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    opacity: 1;
    margin-right: 6px;
}

.tp-offer-pagination .swiper-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet-active, .swiper-pagination-bullets.swiper-pagination-horizontal .swiper-pagination-bullet-active, .swiper-pagination-custom .swiper-pagination-bullet-active, .swiper-pagination-fraction .swiper-pagination-bullet-active {
    transform: scale(1.2);
    background: var(--tp-theme-secondary);
}

/*----------------------------------------*/
/*  2.10 Offcanvas
/*----------------------------------------*/
.offcanvas__area {
    position: fixed;
    right: 0;
    left: auto;
    width: 400px;
    height: 100%;
    -webkit-transform: translateX(calc(100% + 80px));
    -moz-transform: translateX(calc(100% + 80px));
    -ms-transform: translateX(calc(100% + 80px));
    -o-transform: translateX(calc(100% + 80px));
    transform: translateX(calc(100% + 80px));
    background: var(--tp-theme-3) none repeat scroll 0 0;
    transition: all 0.4s ease-in-out;
    opacity: 0;
    visibility: hidden;
    z-index: 999;
    overflow-y: scroll;
    overscroll-behavior-y: contain;
    scrollbar-width: none;
}

.offcanvas__area::-webkit-scrollbar {
    display: none; /* for Chrome, Safari, and Opera */
}

.offcanvas__area.offcanvas-opened {
    -webkit-transform: translateX(0);
    -moz-transform: translateX(0);
    -ms-transform: translateX(0);
    -o-transform: translateX(0);
    transform: translateX(0);
    visibility: visible;
    opacity: 1;
    z-index: 1030;
}

@media (max-width: 575px) {
    .offcanvas__area {
        width: 100%;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .offcanvas__top {
        margin-bottom: 30px;
    }
}

.offcanvas__wrapper {
    padding: 40px 50px 40px 50px;
    min-height: 100%;
}

@media (max-width: 575px) {
    .offcanvas__wrapper {
        padding: 30px;
    }
}

.offcanvas__close-btn {
    position: absolute;
    top: 55px;
    right: 40px;
    display: inline-block;
    font-size: 16px;
    height: 40px;
    width: 40px;
    line-height: 40px;
    color: var(--tp-common-white);
    background-color: var(--tp-theme-primary);
}

@media (max-width: 575px) {
    .offcanvas__close-btn {
        right: 30px;
        top: 40px;
    }
}

.offcanvas__close-btn svg {
    -webkit-transform: translateY(-1px);
    -moz-transform: translateY(-1px);
    -ms-transform: translateY(-1px);
    -o-transform: translateY(-1px);
    transform: translateY(-1px);
    transition: all 0.3s ease-in-out;
}

.offcanvas__close-btn:hover svg {
    transform: rotate(45deg);
}

.offcanvas__contact {
    margin-bottom: 30px;
}

.offcanvas__contact-content {
    margin-bottom: 10px;
}

.offcanvas__contact-content-icon i {
    color: var(--tp-theme-primary);
    margin-right: 10px;
}

.offcanvas__contact-content-content a {
    font-size: 16px;
    color: var(--tp-common-white);
}

.offcanvas__contact-content-content a:hover {
    color: var(--tp-theme-primary);
}

.offcanvas__text p {
    color: var(--tp-common-white);
}

.offcanvas__social {
    margin-bottom: 22px;
    padding-bottom: 40px;
}

.offcanvas__social .icon {
    display: inline-block;
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background: var(--tp-theme-primary);
    color: var(--tp-common-white);
    margin-right: 5px;
    transition: all 0.3s ease-in-out;
}

.offcanvas__social .icon:hover {
    transform: translateY(-8px);
}

.offcanvas__title {
    font-size: 26px;
    font-weight: 700;
    margin-bottom: 15px;
    color: var(--tp-common-white);
}

.cartmini__area {
    position: fixed;
    right: 0;
    left: auto;
    width: 400px;
    height: 100%;
    opacity: 0;
    visibility: hidden;
    z-index: 999;
    overflow-y: scroll;
    overscroll-behavior-y: contain;
    scrollbar-width: none;
    background: var(--tp-common-white) none repeat scroll 0 0;
    -webkit-transform: translateX(calc(100% + 80px));
    -moz-transform: translateX(calc(100% + 80px));
    -ms-transform: translateX(calc(100% + 80px));
    -o-transform: translateX(calc(100% + 80px));
    transform: translateX(calc(100% + 80px));
    transition: all 0.4s ease-in-out;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .cartmini__area {
        right: 0;
        left: auto;
        -webkit-transform: translateX(calc(100% + 80px));
        -moz-transform: translateX(calc(100% + 80px));
        -ms-transform: translateX(calc(100% + 80px));
        -o-transform: translateX(calc(100% + 80px));
        transform: translateX(calc(100% + 80px));
    }
}

.cartmini__area::-webkit-scrollbar {
    display: none; /* for Chrome, Safari, and Opera */
}

.cartmini__area.cartmini-opened {
    -webkit-transform: translateX(0);
    -moz-transform: translateX(0);
    -ms-transform: translateX(0);
    -o-transform: translateX(0);
    transform: translateX(0);
    visibility: visible;
    opacity: 1;
}

@media (max-width: 575px) {
    .cartmini__area {
        width: 100%;
    }
}

.cartmini__wrapper {
    position: relative;
    padding: 40px;
    z-index: 999;
    min-height: 100%;
}

@media (max-width: 575px) {
    .cartmini__wrapper {
        padding: 30px;
    }
}

.cartmini__close {
    position: absolute;
    top: 35px;
    right: 30px;
}

@media (max-width: 575px) {
    .cartmini__close {
        right: 20px;
        top: 20px;
    }
}

.cartmini__close-btn {
    position: relative;
    display: inline-block;
    font-size: 16px;
    height: 44px;
    width: 44px;
    line-height: 40px;
    background-color: #f5f5f5;
    color: var(--tp-text-1);
    z-index: 2;
}

.cartmini__close-btn::after {
    position: absolute;
    content: "";
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    border-radius: 10px;
    background: var(--tp-gradient-primary);
    opacity: 0;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    z-index: 0;
}

.cartmini__close-btn svg {
    position: relative;
    z-index: 2;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    -webkit-transform: translateY(-1px);
    -moz-transform: translateY(-1px);
    -ms-transform: translateY(-1px);
    -o-transform: translateY(-1px);
    transform: translateY(-1px);
}

.cartmini__close-btn:hover::after {
    opacity: 1;
}

.cartmini__close-btn:hover svg {
    color: var(--tp-theme-primary);
    transform: rotate(45deg);
}

/*----------------------------------------
    Body Overlay
-----------------------------------------*/
.body-overlay {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 99;
    height: 100%;
    width: 100%;
    opacity: 0;
    visibility: hidden;
    background-color: var(--tp-common-black);
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.body-overlay.opened {
    opacity: 0.7;
    visibility: visible;
}

/*----------------------------------------*/
/*  2.11 Breadcrumb
/*----------------------------------------*/
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
    .tp-breadcrumb__area {
        padding-top: 50px;
        padding-bottom: 70px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-breadcrumb__area {
        padding-top: 55px;
        padding-bottom: 65px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-breadcrumb__area.mb-120 {
        margin-bottom: 0;
    }
}

.tp-breadcrumb__overlay {
    background: rgba(149, 145, 145, 0.05);
}

.tp-breadcrumb__bg {
    position: absolute;
    top: 0px;
    left: 0px;
    height: 100%;
    width: 100%;
    background-repeat: no-repeat;
    background-position: center;
    z-index: -1;
}

.tp-breadcrumb__title {
    font-size: 70px;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 0px;
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-breadcrumb__title {
        font-size: 42px;
    }
}

.tp-breadcrumb__title.white {
    color: var(--tp-common-white);
}

/*----------------------------------------*/
/*  2.12 Accordion
/*----------------------------------------*/
.tp-accordion .accordion-item {
    border: 0;
    background: var(--tp-theme-secondary);
    margin-bottom: 28px;
}

.tp-accordion .accordion-item:last-of-type {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
}

.tp-accordion .accordion-button {
    color: var(--tp-heading-primary);
    font-weight: 500;
    font-size: 24px;
    background: var(--tp-theme-secondary);
    border-top: 1px solid #8295B3;
    border-bottom: 1px solid #8295B3;
    padding: 20px 20px 20px 0px;
    border-radius: 0 !important;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-accordion .accordion-button::after {
    position: absolute;
    content: "\f105";
    font-family: var(--tp-ff-fontawesome);
    top: 48%;
    right: 15px;
    height: 0;
    width: 0;
    font-weight: 900;
    font-size: 14px;
    -webkit-transform: rotate(-90deg);
    -moz-transform: rotate(-90deg);
    -ms-transform: rotate(-90deg);
    -o-transform: rotate(-90deg);
    transform: rotate(-90deg);
    background: var(--tp-theme-secondary);
    color: var(--tp-heading-primary);
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-accordion .accordion-button.collapsed::after {
    right: 0;
    color: var(--tp-theme-primary);
    -webkit-transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    -o-transform: rotate(90deg);
    transform: rotate(90deg);
}

.tp-accordion .accordion-button:not(.collapsed) {
    background: var(--tp-theme-secondary);
    color: var(--tp-common-white);
    box-shadow: none;
    border: none;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-accordion .accordion-button:focus {
    box-shadow: none;
}

.tp-accordion .accordion-body {
    padding: 0;
}

/*----------------------------------------*/
/*  2.13 Tab
/*----------------------------------------*/
.tp-tab .nav-tabs {
    padding: 0;
    margin: 0;
    border: 0;
}

.tp-tab .nav-tabs .nav-link {
    padding: 0;
    margin: 0;
    border: 0;
}

.tp-product-tab-2 .nav-tabs .nav-link {
    font-size: 20px;
    color: #A0A2A4;
    position: relative;
}

@media (max-width: 575px) {
    .tp-product-tab-2 .nav-tabs .nav-link {
        font-size: 15px;
    }
}

.tp-product-tab-2 .nav-tabs .nav-link:not(:first-child) {
    margin-left: 28px;
}

.tp-product-tab-2 .nav-tabs .nav-link:not(:first-child)::after {
    position: absolute;
    left: -17px;
    top: 50%;
    content: "";
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #CED2D6;
}

.tp-product-tab-2 .nav-tabs .nav-link.active {
    color: var(--tp-theme-secondary);
}

.tp-product-tab-2 .nav-tabs .nav-link.active .tp-product-tab-tooltip {
    opacity: 1;
    visibility: visible;
}

.tp-product-tab-2 .nav-tabs .nav-link .tp-product-tab-tooltip {
    position: absolute;
    top: -24px;
    right: 0;
    background-color: var(--tp-theme-secondary);
    color: var(--tp-common-white);
    font-size: 12px;
    line-height: 1;
    display: inline-block;
    padding: 4px 9px;
    border-radius: 4px;
    visibility: hidden;
    opacity: 0;
    font-weight: 700;
}

.tp-product-tab-2 .nav-tabs .nav-link .tp-product-tab-tooltip::after {
    position: absolute;
    content: "";
    bottom: 0;
    bottom: -5px;
    left: 7px;
    width: 13px;
    height: 6px;
    background-color: var(--tp-theme-secondary);
    clip-path: polygon(100% 0, 0 0, 28% 100%);
}

.tp-product-tab-3 .nav-tabs .nav-link {
    font-size: 16px;
    padding: 0 0;
}

@media (max-width: 575px) {
    .tp-product-tab-3 .nav-tabs .nav-link {
        font-size: 15px;
    }
}

.tp-product-tab-3 .nav-tabs .nav-link:not(:first-child) {
    margin-left: 23px;
}

.tp-product-tab-3 .nav-tabs .nav-link:not(:first-child)::after {
    width: 4px;
    height: 4px;
    left: -14px;
}

.tp-product-tab-3 .nav-tabs .nav-link.active {
    color: var(--tp-common-black);
}

.tp-product-tab-3 .nav-tabs .nav-link.active .tp-product-tab-tooltip {
    opacity: 1;
    visibility: visible;
}

.tp-product-tab-3 .nav-tabs .nav-link .tp-product-tab-tooltip {
    background-color: var(--tp-theme-primary);
}

.tp-product-tab-3 .nav-tabs .nav-link .tp-product-tab-tooltip::after {
    background-color: var(--tp-theme-primary);
}

.tp-product-tab-5 .nav-tabs .nav-link {
    padding: 0 5px;
}

@media (max-width: 575px) {
    .tp-product-tab-5 .nav-tabs .nav-link {
        font-size: 15px;
    }
}

.tp-product-tab-5 .nav-tabs .nav-link:not(:last-child)::after {
    left: -15px;
}

.tp-product-tab-5 .nav-tabs .nav-link:not(:first-child) {
    margin-left: 22px;
}

.tp-product-tab-5 .nav-tabs .nav-link.active {
    color: var(--tp-common-black);
}

.tp-product-tab-5 .nav-tabs .nav-link.active .tp-product-tab-tooltip {
    opacity: 1;
    visibility: visible;
}

.tp-product-tab-5 .nav-tabs .nav-link .tp-product-tab-tooltip {
    background-color: var(--tp-theme-green);
}

.tp-product-tab-5 .nav-tabs .nav-link .tp-product-tab-tooltip::after {
    background-color: var(--tp-theme-green);
}

.tp-tab-line {
    position: absolute;
    content: "";
    left: 0;
    bottom: -1px;
    width: 38%;
    height: 2px;
    background-color: var(--tp-common-black);
}

/*----------------------------------------*/
/*  2.14 Modal
/*----------------------------------------*/
/*----------------------------------------*/
/*  2.15 Section Title
/*----------------------------------------*/
.tp-section-title {
    font-weight: 700;
    font-size: 55px;
    line-height: 1.2;
    letter-spacing: -0.8px;
    margin-bottom: 20px;
    font-family: var(--tp-ff-heading);
    color: var(--tp-heading-primary);
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-section-title br {
        display: none;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-section-title {
        font-size: 45px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-section-title {
        font-size: 40px;
    }
}

.tp-section-size {
    font-size: 45px;
}

@media (max-width: 575px) {
    .tp-section-title {
        font-size: 26px;
    }
}

.tp-section-title.pink {
    color: var(--tp-heading-2);
}

.tp-section-title-pre {
    position: relative;
    font-size: 14px;
    font-weight: 500;
    line-height: 38px;
    margin-bottom: 8px;
    text-transform: uppercase;
    display: inline-block;
    color: var(--tp-theme-primary);
    padding-left: 15px;
}

.tp-section-title-pre::after {
    position: absolute;
    content: "";
    height: 6px;
    width: 6px;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    border: 1px solid var(--tp-theme-secondary);
}

.tp-section-title-pre-2 {
    position: relative;
    font-size: 14px;
    font-weight: 500;
    line-height: 38px;
    margin-bottom: 8px;
    text-transform: uppercase;
    display: inline-block;
    color: var(--tp-theme-primary);
}

.tp-section-title-pre-2.pink {
    color: var(--tp-theme-4);
}

.tp-home-2-section-text {
    font-size: 180px;
    font-weight: 700;
    text-transform: uppercase;
    color: rgba(0, 35, 90, 0.039);
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-home-2-section-text {
        font-size: 145px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-home-2-section-text {
        font-size: 100px;
    }
}

@media (max-width: 575px) {
    .tp-home-2-section-text {
        font-size: 60px;
    }
}

/*----------------------------------------*/
/*  2.16 Search css start
/*----------------------------------------*/
.search-area {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    visibility: hidden;
    transition: all 0.8s ease-in-out;
    -webkit-transform: translateY(calc(-100% - 80px));
    -moz-transform: translateY(calc(-100% - 80px));
    -ms-transform: translateY(calc(-100% - 80px));
    -o-transform: translateY(calc(-100% - 80px));
    transform: translateY(calc(-100% - 80px));
    background: rgba(0, 0, 0, 0.9) none repeat scroll 0 0;
    animation: 0.95s ease 0s normal forwards 1 running headerSlideDown;
    z-index: 999;
    overflow-y: scroll;
    overscroll-behavior-y: contain;
    scrollbar-width: none;
}

.search-area::-webkit-scrollbar {
    display: none; /* for Chrome, Safari, and Opera */
}

.search-area.search-opened {
    -webkit-transform: translateY(0);
    -moz-transform: translateY(0);
    -ms-transform: translateY(0);
    -o-transform: translateY(0);
    transform: translateY(0);
    visibility: visible;
    opacity: 1;
}

.search-wrapper {
    padding: 30px;
    min-height: 100%;
}

@media (max-width: 575px) {
    .search-wrapper {
        padding: 20px;
    }
}

.search-close {
    position: absolute;
    top: 35px;
    right: 30px;
}

@media (max-width: 575px) {
    .search-close {
        right: 20px;
        top: 20px;
    }
}

.search-close-btn {
    position: relative;
}

.search-close-btn svg {
    height: 20px;
    width: 20px;
    font-weight: 600;
    color: var(--tp-common-white);
    position: relative;
    z-index: 2;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.search-close-btn:hover svg {
    opacity: 0.8;
}

.search-close-btn svg {
    -webkit-transform: translateY(-1px);
    -moz-transform: translateY(-1px);
    -ms-transform: translateY(-1px);
    -o-transform: translateY(-1px);
    transform: translateY(-1px);
}

.search-content .heading {
    color: var(--tp-common-white);
}

.search-content .search-input {
    padding-right: 60px;
    font-size: 16px;
}

.search-content .search-input:focus {
    border: 1px solid var(--tp-theme-primary);
}

.search-content .search-input::placeholder {
    font-size: 16px;
}

.search-icon {
    position: absolute;
    top: 20px;
    right: 20px;
    color: var(--tp-theme-primary);
}

.search-icon:hover {
    color: var(--tp-theme-secondary);
}

/* HEADER CSS */
/*----------------------------------------*/
/*  3.1 Header Style 1
/*----------------------------------------*/
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-header-logo img {
        width: 100%;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-header-logo img {
        max-width: 100%;
    }
}

.tp-header-logo .black {
    display: none;
}

.tp-header-logo.scrolled .black {
    display: block;
}

.tp-header-logo.scrolled .white {
    display: none;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-header-main {
        padding-top: 10px;
        padding-bottom: 10px;
    }
}

.tp-header-main-menu {
    padding-left: 94px;
}

.tp-header-main-sticky {
    top: -1px !important;
    border-bottom: 1px solid var(--tp-theme-secondary);
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .tp-header-main-menu {
        padding-left: 30px;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-header-main-menu {
        padding-left: 20px;
    }
}

.tp-header-top {
    background: var(--tp-theme-secondary);
}

.tp-header-top-right {
    position: relative;
}

.tp-header-top-right-color {
    position: relative;
    padding: 8px 0px 8px 40px;
}

.tp-header-top-right-color::before {
    position: absolute;
    content: "";
    left: 0;
    top: 0;
    height: 100%;
    width: 2000px;
    background-color: var(--tp-theme-1);
}

.tp-header-top-right-color p {
    font-size: 16px;
    z-index: 1;
    margin-right: 24px;
    color: var(--tp-common-white);
}

.tp-header-top-center p {
    font-size: 16px;
    color: var(--tp-common-white);
}

.tp-header-top-info a {
    font-family: var(--tp-ff-p);
    font-weight: 400;
    font-size: 16px;
    color: var(--tp-common-white);
}

.tp-header-top-info a span {
    display: inline-block;
    margin-right: 8px;
    color: var(--tp-common-white);
}

.tp-header-top-info a:not(:last-of-type) {
    margin-right: 55px;
}

@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-header-top-info a:not(:last-of-type) {
        margin-right: 30px;
    }
}

.tp-header-top-info a:hover {
    color: var(--tp-theme-primary);
}

.tp-header-top-btn span {
    position: relative;
    display: inline-block;
    margin-right: 15px;
}

.tp-header-top-btn span:not(:last-of-type)::before {
    position: absolute;
    content: "/";
    top: 0;
    right: -10px;
    color: #aad5ff;
}

.tp-header-top-btn span a {
    font-size: 14px;
    color: #aad5ff;
}

.tp-header-top-btn span a::after {
    position: absolute;
    content: "";
    bottom: 0;
    left: 0;
    height: 1px;
    width: 100%;
    opacity: 0;
    transform: translateY(1px);
    transition: 0.15s all cubic-bezier(0.39, 0.575, 0.565, 1);
    transition-property: opacity, transform, -webkit-transform;
    background: var(--tp-theme-primary);
}

.tp-header-top-btn span a:hover::after {
    opacity: 1;
    transform: translateY(-3px) translateZ(0);
}

.tp-header-top-social a {
    position: relative;
    font-size: 14px;
    margin-left: 18px;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    color: var(--tp-common-white);
}

.tp-header-top-social a:hover {
    color: var(--tp-theme-primary);
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-header-btn {
        padding-left: 20px;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .tp-header-btn .tp-btn {
        padding: 10px 16px;
    }
}

.tp-header-contact-inner {
    border-right: 1px solid #ededed;
    padding-right: 30px;
    margin-right: 30px;
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .tp-header-contact-inner {
        margin-right: 20px;
        padding-right: 20px;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-header-contact-inner {
        margin-right: 0px;
        padding-right: 20px;
    }
}

.tp-header-contact-content p {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 2px;
    color: var(--tp-text-body);
}

.tp-header-contact-content span {
    display: block;
    font-size: 16px;
    font-weight: 700;
    color: var(--tp-heading-primary);
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-header-contact-content span:hover {
    color: var(--tp-theme-primary);
}

.tp-header-contact-icon:not(:last-of-type) {
    margin-right: 20px;
}

.tp-header-contact-icon span, .tp-header-contact-icon button {
    position: relative;
    display: inline-block;
    cursor: pointer;
    height: 60px;
    width: 60px;
    line-height: 60px;
    text-align: center;
    border: 1px dashed;
    border-color: rgb(202, 202, 202);
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-header-contact-icon span:hover, .tp-header-contact-icon button:hover {
    background: var(--tp-theme-secondary);
    border: 1px solid var(--tp-theme-secondary);
}

.tp-header-contact-icon span:hover i, .tp-header-contact-icon button:hover i {
    color: var(--tp-common-white);
}

.tp-header-contact-icon span i, .tp-header-contact-icon button i {
    font-size: 22px;
    font-weight: 500;
    color: var(--tp-heading-primary);
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-header-sticky-cloned {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 98;
    visibility: hidden;
    transform: translateY(-100%);
    background-color: var(--tp-common-white);
    box-shadow: 0px 4px 10px rgba(3, 4, 28, 0.1);
    transition: transform 500ms ease, visibility 500ms ease;
}

.tp-header-sticky-cloned.tp-header-pinned {
    transform: translateY(0%);
    visibility: visible;
}

.tp-header-onepage {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    background: var(--tp-common-white);
    box-shadow: 0px 4px 10px rgba(3, 4, 28, 0.1);
    animation: 0.95s ease 0s normal forwards 1 running headerSlideDown;
    transition: 0.3s ease;
    z-index: 10;
}

.tp-header-sticky-onepage {
    display: none;
}

.tp-header-sticky-onepage.tp-header-onepage {
    display: inline-block;
}

.tp-header-soon-logo {
    padding-top: 50px;
}

.tp-header-3-top {
    background-repeat: no-repeat;
    background-size: cover;
}

.tp-header-3-top-right .tp-header-top-right-color::after {
    position: absolute;
    content: "";
    left: 0;
    top: 0;
    height: 100%;
    width: 2000px;
    background-color: var(--tp-theme-secondary);
}

.tp-header-3-top-right .tp-header-top-right-color::before {
    content: none;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-header-3-main-menu {
        padding-left: 20px;
    }
}

.tp-header-3-main-menu .tp-main-menu ul li.has-dropdown > a::after {
    content: none;
}

@media only screen and (min-width: 1701px) and (max-width: 1800px) {
    .tp-header-3-main-menu .tp-main-menu ul li:not(:last-of-type) {
        margin-right: 35px;
    }
}

@media only screen and (min-width: 1600px) and (max-width: 1700px) {
    .tp-header-3-main-menu .tp-main-menu ul li:not(:last-of-type) {
        margin-right: 28px;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .tp-header-3-main-menu .tp-main-menu ul li:not(:last-of-type) {
        margin-right: 15px;
    }
}

.tp-header-3-search {
    position: relative;
    margin-right: 30px;
}

.tp-header-3-search button {
    position: absolute;
    top: 18px;
    right: 20px;
}

.tp-header-3-search button i {
    font-size: 18px;
    font-weight: 500;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    color: var(--tp-heading-primary);
}

.tp-header-3-search button:hover i {
    color: var(--tp-theme-primary);
}

.tp-header-3-search input {
    font-size: 16px;
    font-weight: 400;
    color: #565969;
    padding-right: 50px;
    border-color: rgb(202, 202, 202);
}

.tp-header-3-search input::placeholder {
    font-size: 16px;
    font-weight: 400;
    color: #565969;
}

.tp-header-3-search input:focus {
    border-color: var(--tp-theme-primary);
}

.tp-header-3-cart button {
    margin-right: 30px;
}

.tp-header-3-cart button i {
    font-size: 18px;
    font-weight: 500;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    color: var(--tp-heading-primary);
}

.tp-header-3-cart button:hover i {
    color: var(--tp-theme-primary);
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .tp-header-3-btn .tp-btn {
        padding: 18px 14px;
    }
}

.tp-header-4-swiper {
    padding-top: 100px;
}

.tp-header-4-transparent {
    position: absolute !important;
    left: 0;
    margin: auto;
    top: 0;
    width: 100%;
    z-index: 98;
    background: transparent;
}

.tp-header-6-top {
    background: var(--tp-common-black);
}

.tp-header-6-top-right .tp-header-top-right-color::before {
    content: none;
}

.tp-header-7-top-social p {
    font-size: 16px;
    font-weight: 400;
    margin-right: 30px;
    color: var(--tp-common-white);
}

.tp-header-7-top-social a {
    font-size: 14px;
    color: var(--tp-common-white);
}

.tp-header-7-top-social a:not(:last-of-type) {
    margin-right: 20px;
}

.tp-header-7-top-social a:hover {
    color: var(--tp-theme-primary);
}

.tp-header-7-top-right ul li {
    list-style: none;
    display: inline-block;
    padding: 13px 0;
}

.tp-header-7-top-right ul li:not(:last-of-type) {
    margin-right: 25px;
}

.tp-header-7-top-right ul li a {
    font-size: 16px;
    font-weight: 400;
    color: var(--tp-common-white);
}

.tp-header-7-top-right ul li a:hover {
    color: var(--tp-theme-primary);
}

.tp-header-7-top-right ul li span {
    font-size: 20px;
    font-weight: 400;
    cursor: pointer;
    color: var(--tp-common-white);
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-header-7-top-right ul li span:hover {
    color: var(--tp-theme-primary);
}

.tp-header-7-logo-middle {
    position: absolute;
    top: 0px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
}

.tp-header-7-logo-middle a {
    padding: 10px 52px 45px;
    display: inline-block;
    background: var(--tp-theme-secondary);
    clip-path: polygon(0px 0px, 100% 0px, 100.34% 74.57%, 49.34% 98.96%, 0px 73.29%);
}

.tp-header-7-main-menu .tp-main-menu nav ul li:nth-child(3) {
    margin-right: 560px;
}

.tp-header-7-main-menu .tp-main-menu nav ul li a {
    padding: 38px 0 38px 0;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-header-7-main-menu .tp-main-menu nav ul li .has-homemenu {
        left: 0;
    }
}

.tp-header-7-main-menu .tp-main-menu nav ul li.has-dropdown a::after {
    content: none;
}

.tp-header-9-sticky {
    padding: 25px 0;
}

.tp-header-9-btn {
    margin-right: 255px;
}

@media only screen and (min-width: 1600px) and (max-width: 1700px) {
    .tp-header-9-btn {
        margin-right: 225px;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .tp-header-9-btn {
        margin-right: 140px;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-header-9-btn {
        margin-right: 80px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-header-9-btn {
        margin-right: 25px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-header-9-btn {
        margin-right: 30px;
    }
}

.tp-header-9-btn .tp-btn {
    border: 1px solid rgb(255, 255, 255);
    background: transparent;
}

.tp-header-9-hamburger button {
    position: relative;
    cursor: pointer;
    height: 60px;
    width: 60px;
    line-height: 60px;
    text-align: center;
    display: inline-block;
    background: var(--tp-theme-primary);
}

.tp-header-9-hamburger button span {
    display: inline-block;
    position: absolute;
    right: 0px;
    height: 2px;
    width: 25px;
    opacity: 1;
    z-index: 1;
    border-radius: 10px;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    background: var(--tp-common-white);
}

.tp-header-9-hamburger button span:nth-child(1) {
    top: 22px;
    left: auto;
    right: 12px;
    transform: translateX(-50%);
    width: 15px;
}

.tp-header-9-hamburger button span:nth-child(2) {
    top: 30px;
    left: 50%;
    transform: translateX(-50%);
    width: 21px;
}

.tp-header-9-hamburger button span:nth-child(3) {
    top: 38px;
    left: auto;
    right: 14px;
    transform: translateX(-50%);
    width: 10px;
}

.tp-header-9-hamburger button:hover {
    border-radius: 5px;
}

.tp-header-9-hamburger button:hover span {
    width: 25px;
    transform: translateX(-35%);
}

.tp-header-9-hamburger button:hover span:nth-child(3) {
    right: 12px;
}

.hamburger-btn {
    width: 35px;
    height: 20px;
    cursor: pointer;
    background: transparent;
    border: 0;
    outline: 0;
    text-align: end;
    transform: translateY(0%);
}

.hamburger-btn:hover span:nth-child(1) {
    width: 20px;
}

.hamburger-btn span {
    display: inline-block;
    position: absolute;
    right: 0px;
    height: 2px;
    width: 25px;
    opacity: 1;
    z-index: 1;
    border-radius: 10px;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    background: var(--tp-theme-primary);
}

.hamburger-btn span:nth-child(1) {
    top: 0;
    width: 10px;
}

.hamburger-btn span:nth-child(2) {
    top: 10px;
    width: 20px;
    right: -10px;
    -webkit-transform: translateX(-50%);
    -moz-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    -o-transform: translateX(-50%);
    transform: translateX(-50%);
}

.tp-header-onepage {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    background: var(--tp-common-white);
    box-shadow: 0px 4px 10px rgba(3, 4, 28, 0.1);
    animation: 0.95s ease 0s normal forwards 1 running headerSlideDown;
    transition: 0.3s ease;
    z-index: 10;
}

/*----------------------------------------*/
/*  4.1 Main menu css
/*----------------------------------------*/
.tp-main-menu ul li {
    position: relative;
    list-style: none;
    display: inline-block;
    transition: none;
}

.tp-main-menu ul li:not(:last-of-type) {
    margin-right: 40px;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-main-menu ul li:not(:last-of-type) {
        margin-right: 25px;
    }
}

.tp-main-menu ul li a {
    display: inline-block;
    padding: 14px 0 4px 0;
    font-size: 16px;
    font-weight: 600;
    text-transform: uppercase;
    color: var(--tp-theme-secondary);
}

.tp-main-menu ul li.has-dropdown > a {
    position: relative;
}

.tp-main-menu ul li.has-dropdown > a::after {
    position: absolute;
    display: inline-block;
    top: 61%;
    transform: translateY(-50%);
    left: -10px;
    content: "";
    height: 6px;
    width: 6px;
    border: 1px solid var(--tp-theme-secondary);
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-main-menu ul li .submenu {
    position: absolute;
    top: 100%;
    left: 0;
    /*width: 200px;*/
    width: 290px;
    z-index: 99;
    opacity: 0;
    visibility: hidden;
    padding: 20px 0 30px;
    background: var(--tp-common-white);
    box-shadow: 0px 30px 70px 0px rgba(11, 6, 70, 0.08);
    -webkit-transition: 0.3s;
    -o-transition: 0.3s;
    transition: all 0.3s;
    -webkit-transition: 0.2s ease-out;
    transition: all 0.2s ease-out;
    -webkit-transform: rotateX(-90deg);
    -ms-transform: rotateX(-90deg);
    transform: rotateX(-90deg);
    -webkit-transform-origin: 0 0;
    -moz-transform-origin: 0 0;
    -ms-transform-origin: 0 0;
    -o-transform-origin: 0 0;
    transform-origin: 0 0;
    border-top: 5px solid var(--tp-theme-primary);
}

.tp-main-menu ul li .submenu li {
    display: block;
    width: 100%;
    margin: 0;
    padding: 5px 22px;
    transform: translateY(5px);
    transition: all 0.2s cubic-bezier(0.4, 0.28, 0.31, 1.28) 0s;
}

.tp-main-menu ul li .submenu li.has-dropdown > a::after {
    position: absolute;
    top: 50%;
    right: 25px;
    -webkit-transform: translateY(-50%) rotate(-90deg);
    -moz-transform: translateY(-50%) rotate(-90deg);
    -ms-transform: translateY(-50%) rotate(-90deg);
    -o-transform: translateY(-50%) rotate(-90deg);
    transform: translateY(-50%) rotate(-90deg);
}

.tp-main-menu ul li .submenu li:last-child a {
    border-bottom: 0;
}

.tp-main-menu ul li .submenu li a {
    position: relative;
    /*font-size: 14px;*/
    font-size: 13px;
    width: 100%;
    z-index: 1;
    padding: 0;
    transition: none;
    color: var(--tp-theme-secondary);
}

.tp-main-menu ul li .submenu li a::before {
    position: absolute;
    content: "";
    top: 12PX;
    left: 0px;
    height: 2px;
    width: 0;
    background: var(--tp-theme-primary);
    transition: all 0.3s ease-in-out;
}

.tp-main-menu ul li .submenu li a span {
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-main-menu ul li .submenu li .submenu {
    left: 120%;
    top: 0;
    visibility: hidden;
    opacity: 0;
}

.tp-main-menu ul li .submenu li:hover > a {
    color: var(--tp-theme-primary);
}

.tp-main-menu ul li .submenu li:hover > a span {
    margin-left: 14px;
}

.tp-main-menu ul li .submenu li:hover > a::before {
    width: 10px;
}

.tp-main-menu ul li .submenu li:hover > .submenu {
    left: 100%;
    visibility: visible;
    opacity: 1;
}

.tp-main-menu ul li:hover > a {
    color: var(--tp-theme-primary);
}

.tp-main-menu ul li:hover > a::after {
    background: var(--tp-theme-primary);
    border: 1px solid var(--tp-theme-primary);
}

.tp-main-menu ul li:hover .submenu {
    opacity: 1;
    visibility: visible;
    -webkit-transform: rotateX(0);
    -ms-transform: rotateX(0);
    transform: rotateX(0);
}

.tp-main-menu ul li .has-homemenu {
    width: 1000px;
    padding: 30px 30px 10px 30px;
    opacity: 0;
    visibility: hidden;
    background-color: var(--tp-common-white);
    box-shadow: rgba(149, 157, 165, 0.4) 0px 8px 24px;
    border-top: 5px solid var(--tp-theme-primary);
}

.tp-main-menu ul li .has-homemenu .homemenu {
    position: relative;
    padding: 0px 10px;
    margin-bottom: 20px;
}

.tp-main-menu ul li .has-homemenu .homemenu-thumb {
    position: relative;
    margin-bottom: 8px;
    border: 1px solid rgba(185, 182, 182, 0.44);
    box-shadow: rgba(149, 157, 165, 0.28) 0px 1px 2px;
}

.tp-main-menu ul li .has-homemenu .homemenu-thumb img {
    width: 100%;
}

.tp-main-menu ul li .has-homemenu .homemenu-thumb::before {
    position: absolute;
    content: "";
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    opacity: 0;
    background: rgba(38, 34, 33, 0.7);
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-main-menu ul li .has-homemenu .homemenu-thumb:hover .homemenu-btn {
    opacity: 1;
    visibility: visible;
}

.tp-main-menu ul li .has-homemenu .homemenu-thumb:hover .homemenu-btn .menu-btn.show-1 {
    transform: translateY(0);
}

.tp-main-menu ul li .has-homemenu .homemenu-thumb:hover .homemenu-btn .menu-btn.show-2 {
    transform: translateY(0);
}

.tp-main-menu ul li .has-homemenu .homemenu-thumb:hover::before {
    opacity: 1;
}

.tp-main-menu ul li .has-homemenu .homemenu-title a {
    padding: 0;
    font-size: 15px;
    font-weight: 600;
    color: var(--tp-theme-secondary);
}

.tp-main-menu ul li .has-homemenu .homemenu-title a:hover {
    color: var(--tp-theme-primary);
}

.tp-main-menu ul li .has-homemenu .homemenu-btn {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    opacity: 0;
    text-align: center;
    transform: translateY(-50%);
    visibility: hidden;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-main-menu ul li .has-homemenu .homemenu-btn .menu-btn {
    position: relative;
    font-size: 14px;
    text-transform: uppercase;
    padding: 10px 20px;
    width: 128px;
    color: var(--tp-common-white);
    background: var(--tp-theme-primary);
    overflow: hidden;
}

.tp-main-menu ul li .has-homemenu .homemenu-btn .menu-btn.show-1 {
    transform: translateY(15px);
}

.tp-main-menu ul li .has-homemenu .homemenu-btn .menu-btn.show-2 {
    transform: translateY(20px);
}

.tp-main-menu ul li .has-homemenu .homemenu-btn .menu-btn::after {
    position: absolute;
    content: "";
    top: 0;
    left: -75%;
    z-index: 2;
    display: block;
    width: 50%;
    height: 100%;
    background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 100%);
    transform: skewX(25deg);
}

.tp-main-menu ul li .has-homemenu .homemenu-btn .menu-btn:hover::after {
    animation: shine 800ms;
}

.tp-main-menu.home-6 ul li:hover > a {
    color: var(--tp-theme-4);
}

.tp-main-menu.home-6 ul li:hover > a:hover {
    color: var(--tp-theme-4);
}

.tp-main-menu.home-6 ul li:hover > a::after {
    background: var(--tp-theme-4);
    border: 1px solid var(--tp-theme-4);
}

.tp-main-menu.home-6 ul li .has-homemenu {
    border-top: 5px solid var(--tp-theme-4);
}

.tp-main-menu.home-6 ul li .has-homemenu .homemenu-btn .menu-btn {
    background: var(--tp-theme-4);
}

.tp-main-menu.home-6 ul li .submenu {
    border-top: 5px solid var(--tp-theme-4);
}

.tp-main-menu.home-6 ul li .submenu li a::before {
    background: var(--tp-theme-4);
}

.tp-main-menu.home-6 ul li .submenu li:hover > a {
    color: var(--tp-theme-4);
}

/*----------------------------------------*/
/*  4.2 Meanmenu css
/*----------------------------------------*/
.mean-remove {
    display: none !important;
}

.mean-container {
    margin-bottom: 40px;
}

.mean-container a.meanmenu-reveal {
    width: 22px;
    height: 22px;
    padding: 13px 13px 11px 13px;
    position: absolute;
    top: 0;
    right: 0;
    cursor: pointer;
    color: #fff;
    text-decoration: none;
    font-size: 16px;
    text-indent: -9999em;
    line-height: 22px;
    font-size: 1px;
    font-weight: 700;
    display: none !important;
}

.mean-container a.meanmenu-reveal span {
    display: block;
    background: #fff;
    height: 3px;
    margin-top: 3px;
}

.mean-container .mean-push {
    float: left;
    width: 100%;
    padding: 0;
    margin: 0;
    clear: both;
}

.mean-container .mean-nav {
    background: none;
    margin-top: 0;
    float: left;
    width: 100%;
    background: transparent;
}

.mean-container .mean-nav .wrapper {
    width: 100%;
    padding: 0;
    margin: 0;
}

.mean-container .mean-nav > ul {
    padding: 0;
    margin: 0;
    width: 100%;
    list-style-type: none;
    display: block !important;
}

.mean-container .mean-nav > ul > li:first-child > a {
    border-top: 0;
}

.mean-container .mean-nav ul {
    padding: 0;
    margin: 0;
    width: 100%;
    list-style-type: none;
}

.mean-container .mean-nav ul li {
    position: relative;
    float: left;
    width: 100%;
}

.mean-container .mean-nav ul li.dropdown-opened > a, .mean-container .mean-nav ul li.dropdown-opened > span {
    color: var(--tp-theme-primary);
}

.mean-container .mean-nav ul li.dropdown-opened > a.mean-expand.mean-clicked, .mean-container .mean-nav ul li.dropdown-opened > span.mean-expand.mean-clicked {
    color: var(--tp-theme-primary);
    -webkit-transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    -o-transform: rotate(90deg);
    transform: rotate(90deg);
}

.mean-container .mean-nav ul li.dropdown-opened > a.mean-expand.mean-clicked i, .mean-container .mean-nav ul li.dropdown-opened > span.mean-expand.mean-clicked i {
    color: var(--tp-theme-primary);
}

.mean-container .mean-nav ul li.has-dropdown:hover > a {
    color: var(--tp-theme-primary);
}

.mean-container .mean-nav ul li.has-dropdown:hover .mean-expand {
    color: var(--tp-theme-primary);
}

.mean-container .mean-nav ul li.mean-last {
    border-bottom: none;
    margin-bottom: 0;
}

.mean-container .mean-nav ul li > a.mean-expand i {
    display: inline-block;
}

.mean-container .mean-nav ul li > a > i {
    display: none;
}

.mean-container .mean-nav ul li a {
    display: block;
    float: left;
    width: 90%;
    padding: 10px 5%;
    margin: 0;
    text-align: left;
    color: #fff;
    border-top: 1px solid #e0e3ed;
    text-decoration: none;
    width: 100%;
    padding: 10px 0;
    color: var(--tp-common-black);
    border-top: 1px solid #ebebeb;
    font-size: 14px;
    line-height: 1.5;
    font-weight: 500;
}

[dir=rtl] .mean-container .mean-nav ul li a {
    float: right;
    text-align: right;
}

.mean-container .mean-nav ul li a:hover {
    color: var(--tp-theme-primary);
}

.mean-container .mean-nav ul li a:hover i {
    color: var(--tp-common-white);
}

.mean-container .mean-nav ul li a.mean-expand {
    margin-top: 1px;
    text-align: center;
    position: absolute;
    right: 0;
    top: 0;
    z-index: 2;
    font-weight: 700;
    background: transparent;
    border: none !important;
    font-size: 14px;
    margin-top: 5px;
    padding: 0 !important;
    line-height: 14px;
    height: 30px;
    width: auto;
    line-height: 30px;
    color: var(--tp-common-black);
    line-height: 30px;
    top: 0;
    font-weight: 400;
}

[dir=rtl] .mean-container .mean-nav ul li a.mean-expand {
    right: auto;
    left: 0;
    text-align: center;
}

.mean-container .mean-nav ul li a.mean-expand:hover {
    color: var(--tp-theme-primary);
    border-color: var(--tp-theme-primary);
}

.mean-container .mean-nav ul li a.mean-expand:hover i {
    color: var(--tp-theme-primary);
}

.mean-container .mean-nav ul li a.mean-expand.mean-clicked {
    color: var(--tp-theme-primary);
}

.mean-container .mean-nav ul li a.mean-expand.mean-clicked:hover {
    color: var(--tp-theme-primary);
}

.mean-container .mean-nav ul li a.mean-expand.mean-clicked:hover i {
    color: var(--tp-theme-primary);
}

.mean-container .mean-nav ul li li a {
    width: 80%;
    padding: 10px 10%;
    text-shadow: none !important;
    visibility: visible;
}

.mean-container .mean-nav ul li li li a {
    width: 70%;
    padding: 10px 15%;
}

.mean-container .mean-nav ul li li li li a {
    width: 60%;
    padding: 10px 20%;
}

.mean-container .mean-nav ul li li li li li a {
    width: 50%;
    padding: 10px 25%;
}

.mean-container .mean-bar, .mean-container .mean-bar * {
    /* Fix for box sizing on Foundation Framework etc. */
    -webkit-box-sizing: content-box;
    -moz-box-sizing: content-box;
    box-sizing: content-box;
}

/*----------------------------------------*/
/*  4.3 Mobilemenu css
/*----------------------------------------*/
.tp-mobile-menu {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    background-color: var(--tp-common-white);
    padding: 13px 0 5px;
    z-index: 999;
    border-top: 1px solid var(--tp-border-primary);
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-mobile-menu.is-sticky {
    bottom: -120px;
    visibility: hidden;
    opacity: 0;
}

.tp-mobile-menu.is-sticky.bottom-menu-sticky {
    visibility: visible;
    opacity: 1;
    bottom: 0;
}

.tp-mobile-item-btn {
    font-size: 28px;
    text-align: center;
}

.tp-mobile-item-btn span {
    display: block;
    line-height: 1;
    font-size: 13px;
    margin-top: 3px;
}

.tp-mobile-item-btn:hover {
    color: var(--tp-common-black);
}

.tp-main-menu-mobile .tp-submenu {
    display: none;
}

.tp-main-menu-mobile .tp-mega-menu .shop-mega-menu-title {
    margin: 0;
    padding-top: 7px;
}

.tp-main-menu-mobile .tp-mega-menu.shop-mega-menu {
    padding: 0 !important;
    padding-left: 19px !important;
    padding-top: 10px !important;
}

.tp-main-menu-mobile .tp-mega-menu .shop-mega-menu-img {
    margin: 7px 0;
}

.tp-main-menu-mobile ul {
    position: static;
    display: block;
    box-shadow: none;
}

.tp-main-menu-mobile ul li {
    list-style: none;
    position: relative;
    width: 100%;
    padding: 0;
}

.tp-main-menu-mobile ul li:not(:last-child) a {
    border-bottom: 1px solid rgba(234, 235, 237, 0.7);
}

.tp-main-menu-mobile ul li.has-dropdown > a .dropdown-toggle-btn {
    position: absolute;
    right: 0;
    top: 50%;
    font-size: 16px;
    color: var(--tp-common-white);
    z-index: 1;
    width: 30px;
    height: 30px;
    text-align: center;
    line-height: 28px;
    border: 1px solid rgb(255, 255, 255);
    transform: translateY(-15px);
}

.tp-main-menu-mobile ul li.has-dropdown > a .dropdown-toggle-btn i {
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-main-menu-mobile ul li.has-dropdown > a .dropdown-toggle-btn.dropdown-opened i {
    -webkit-transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    -o-transform: rotate(90deg);
    transform: rotate(90deg);
}

.tp-main-menu-mobile ul li.has-dropdown > a .dropdown-toggle-btn:hover {
    background-color: var(--tp-theme-primary);
    border-color: var(--tp-theme-primary);
    color: var(--tp-common-white);
}

.tp-main-menu-mobile ul li.has-dropdown > a .dropdown-toggle-btn:hover i {
    color: var(--tp-common-white);
}

.tp-main-menu-mobile ul li.has-dropdown > a.expanded {
    color: var(--tp-theme-primary);
}

.tp-main-menu-mobile ul li.has-dropdown > a.expanded .dropdown-toggle-btn.dropdown-opened {
    background-color: var(--tp-theme-primary);
    border-color: var(--tp-theme-primary);
    color: var(--tp-common-white);
}

.tp-main-menu-mobile ul li.has-dropdown > a.expanded .dropdown-toggle-btn.dropdown-opened i {
    color: var(--tp-common-white);
}

.tp-main-menu-mobile ul li.has-dropdown:hover > a::after {
    color: var(--tp-theme-green);
}

.tp-main-menu-mobile ul li:last-child a span {
    border-bottom: 0;
}

.tp-main-menu-mobile ul li > a {
    display: block;
    font-size: 16px;
    color: var(--tp-common-white);
    position: relative;
    padding: 10px 0;
    padding-right: 20px;
}

.tp-main-menu-mobile ul li > a svg {
    transform: translateY(2px);
}

.tp-main-menu-mobile ul li > a > i {
    display: inline-block;
    width: 11%;
    margin-right: 13px;
    -webkit-transform: translateY(4px);
    -moz-transform: translateY(4px);
    -ms-transform: translateY(4px);
    -o-transform: translateY(4px);
    transform: translateY(4px);
    font-size: 21px;
    line-height: 1;
}

.tp-main-menu-mobile ul li > a .menu-text {
    font-size: 16px;
    line-height: 11px;
    border-bottom: 1px solid #EAEBED;
    width: 82%;
    display: inline-block;
    padding: 19px 0 17px;
}

.tp-main-menu-mobile ul li img {
    width: 100%;
}

.tp-main-menu-mobile ul li ul {
    padding: 0;
}

.tp-main-menu-mobile ul li ul li {
    padding: 0;
}

.tp-main-menu-mobile ul li ul li a {
    margin-left: auto;
    width: 93%;
    padding: 10px 5%;
    text-shadow: none !important;
    visibility: visible;
    padding-left: 0;
    padding-right: 20px;
}

.tp-main-menu-mobile ul li ul li li a {
    width: 88%;
    padding: 10px 7%;
    padding-left: 0;
    padding-right: 20px;
}

.tp-main-menu-mobile ul li ul li li li a {
    width: 83%;
    padding: 10px 9%;
    padding-left: 0;
    padding-right: 20px;
}

.tp-main-menu-mobile ul li ul li li li li a {
    width: 68%;
    padding: 10px 11%;
    padding-left: 0;
    padding-right: 20px;
}

.tp-main-menu-mobile ul li:hover > a {
    color: var(--tp-theme-primary);
}

.tp-main-menu-mobile ul li:hover > a::after {
    color: var(--tp-theme-primary);
}

.tp-main-menu-mobile ul li:hover > a .dropdown-toggle-btn i {
    color: var(--tp-theme-primary);
}

.tp-main-menu-mobile ul li:hover .mega-menu {
    visibility: visible;
    opacity: 1;
    top: 0;
}

.tp-main-menu-mobile ul li .mega-menu, .tp-main-menu-mobile ul li .submenu {
    position: static;
    min-width: 100%;
    padding: 0;
    box-shadow: none;
    visibility: visible;
    opacity: 1;
    display: none;
}

.tp-main-menu-mobile ul li .mega-menu li, .tp-main-menu-mobile ul li .submenu li {
    float: none;
    display: block;
    width: 100%;
    padding: 0;
}

.tp-main-menu-mobile ul li .mega-menu li:hover a .dropdown-toggle-btn, .tp-main-menu-mobile ul li .submenu li:hover a .dropdown-toggle-btn {
    color: var(--tp-theme-primary);
}

.tp-main-menu-mobile ul li .tp-submenu .homemenu {
    padding: 0px 10px;
    position: relative;
    margin-bottom: 20px;
}

.tp-main-menu-mobile ul li .tp-submenu .homemenu-thumb {
    position: relative;
    margin-bottom: 12px;
}

.tp-main-menu-mobile ul li .tp-submenu .homemenu-thumb::before {
    position: absolute;
    content: "";
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    opacity: 0;
    background: rgba(38, 34, 33, 0.7);
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-main-menu-mobile ul li .tp-submenu .homemenu-thumb:hover .homemenu-btn {
    opacity: 1;
    visibility: visible;
}

.tp-main-menu-mobile ul li .tp-submenu .homemenu-thumb:hover .homemenu-btn .menu-btn.show-1 {
    transform: translateY(0);
}

.tp-main-menu-mobile ul li .tp-submenu .homemenu-thumb:hover .homemenu-btn .menu-btn.show-2 {
    transform: translateY(0);
}

.tp-main-menu-mobile ul li .tp-submenu .homemenu-thumb:hover::before {
    opacity: 1;
}

.tp-main-menu-mobile ul li .tp-submenu .homemenu-title {
    font-size: 15px;
    font-weight: 600;
}

.tp-main-menu-mobile ul li .tp-submenu .homemenu-title a {
    border: none;
    color: var(--tp-common-white);
}

.tp-main-menu-mobile ul li .tp-submenu .homemenu-title a:hover {
    color: var(--tp-theme-primary);
}

.tp-main-menu-mobile ul li .tp-submenu .homemenu-btn {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    opacity: 0;
    text-align: center;
    transform: translateY(-50%);
    visibility: hidden;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-main-menu-mobile ul li .tp-submenu .homemenu-btn .menu-btn {
    font-size: 14px;
    text-transform: uppercase;
    padding: 10px 15px;
    display: inline-block;
    width: 115px;
    border: none;
    background-size: 200% auto;
    color: var(--tp-common-white);
    background: var(--tp-theme-primary);
    overflow: hidden;
}

.tp-main-menu-mobile ul li .tp-submenu .homemenu-btn .menu-btn.show-1 {
    transform: translateY(15px);
}

.tp-main-menu-mobile ul li .tp-submenu .homemenu-btn .menu-btn.show-2 {
    transform: translateY(20px);
}

.tp-main-menu-mobile ul li .tp-submenu .homemenu-btn .menu-btn::after {
    position: absolute;
    content: "";
    top: 0;
    left: -75%;
    z-index: 2;
    display: block;
    width: 50%;
    height: 100%;
    background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 100%);
    transform: skewX(25deg);
}

.tp-main-menu-mobile ul li .tp-submenu .homemenu-btn .menu-btn:hover::after {
    animation: shine 800ms;
}

.tp-main-menu-mobile .tp-main-menu-content ul li:not(:last-child) .home-menu-title a {
    border-bottom: none;
}

.tp-main-menu-mobile * ul, .tp-main-menu-mobile * li {
    transition: none !important;
}

/*----------------------------------------*/
/*  5.4 Blog css
/*----------------------------------------*/
.tp-blog-shape {
    position: absolute;
    bottom: 0;
    right: 0;
}

.tp-blog-item {
    position: relative;
    background: var(--tp-common-white);
}

.tp-blog-item:hover .tp-blog-thumb img {
    transform: scale(1.1);
}

.tp-blog-item:hover .tp-blog-content::after {
    width: 100%;
    left: 0;
    right: auto;
}

.tp-blog-thumb {
    overflow: hidden;
}

.tp-blog-thumb img {
    -webkit-transition: 1s;
    -moz-transition: 1s;
    -ms-transition: 1s;
    -o-transition: 1s;
    transition: 1s;
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-blog-thumb img {
        width: 100%;
    }
}

.tp-blog-content {
    position: relative;
    padding: 30px 30px 35px;
}

.tp-blog-content::after {
    position: absolute;
    content: "";
    width: 0%;
    height: 2px;
    bottom: 0px;
    left: auto;
    right: 0;
    background-color: var(--tp-theme-primary);
    transition: all 0.3s ease-out 0s;
}

.tp-blog-content-info {
    margin-bottom: 18px;
    display: flex;
}

.tp-blog-content-info span {
    position: relative;
    font-size: 16px;
    font-weight: 600;
    color: #565969;
    display: inline-block;
}

.tp-blog-content-info span:not(:last-of-type) {
    margin-right: 30px;
}

.tp-blog-content-info span:not(:last-of-type)::after {
    position: absolute;
    content: "";
    top: 50%;
    right: -18px;
    height: 6px;
    width: 6px;
    display: inline-block;
    transform: translateY(-50%);
    border: 1px solid var(--tp-theme-secondary);
}

.tp-blog-title {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 35px;
    letter-spacing: -0.96px;
    color: var(--tp-theme-secondary);
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-blog-title {
        font-size: 22px;
    }
}

.tp-blog-title.home-5 {
    font-weight: 700;
}

.tp-blog-title a:hover {
    color: var(--tp-theme-primary);
}

.tp-blog-btn .tp-btn {
    padding: 11px 43px;
    color: var(--tp-theme-primary);
    background: var(--tp-text-2);
}

.tp-blog-btn .tp-btn svg {
    margin-right: 2px;
}

.tp-blog-btn .tp-btn:hover {
    color: var(--tp-common-white);
    background: var(--tp-theme-primary);
}

.tp-blog-2-bg::before {
    position: absolute;
    content: "";
    height: 64%;
    width: 100%;
    top: 0;
    left: 0;
    background: #f7f7f7;
    z-index: -1;
}

.tp-blog-2-item:hover .tp-blog-2-shape {
    opacity: 1;
}

.tp-blog-2-item:hover .tp-blog-2-thumb::before {
    opacity: 0.8;
}

.tp-blog-2-item:hover .tp-blog-2-thumb img {
    transform: scale(1.1);
}

.tp-blog-2-item:hover .tp-blog-2-thumb-icon {
    opacity: 1;
    visibility: visible;
}

.tp-blog-2-item:hover .tp-blog-2-content .tp-icon-style {
    color: var(--tp-theme-primary);
}

.tp-blog-2-thumb::before {
    position: absolute;
    content: "";
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    opacity: 0;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    pointer-events: none;
    z-index: 1;
    background-color: rgb(0, 35, 90);
}

.tp-blog-2-thumb img {
    width: 100%;
    height: 338px;
    -webkit-transition: 1s;
    -moz-transition: 1s;
    -ms-transition: 1s;
    -o-transition: 1s;
    transition: 1s;
}

.tp-blog-2-thumb-icon {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 1;
    opacity: 0;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-blog-2-thumb-icon i {
    font-size: 20px;
    color: var(--tp-common-white);
}

.tp-blog-2-content {
    padding: 30px 30px 40px;
    background-color: rgb(255, 255, 255);
    box-shadow: 0px 4px 60px 0px rgba(18, 14, 14, 0.06);
}

.tp-blog-2-content a {
    position: relative;
}

.tp-blog-2-content-info {
    margin-bottom: 15px;
}

.tp-blog-2-content-info span {
    display: block;
    font-size: 16px;
    font-weight: 400;
    color: #565969;
}

.tp-blog-2-content-info span i {
    font-size: 14px;
    margin-right: 8px;
    color: var(--tp-theme-primary);
}

.tp-blog-2-content-info span:not(:last-of-type) {
    margin-right: 30px;
}

.tp-blog-2-content .tp-icon-style {
    color: #565969;
}

.tp-blog-2-content.pink span i {
    color: var(--tp-theme-4);
}

.tp-blog-2-content.pink:hover .tp-icon-style {
    color: var(--tp-theme-4);
}

.tp-blog-2-content.pink .tp-blog-2-title:hover a {
    color: var(--tp-theme-4);
}

.tp-blog-2-shape {
    position: absolute;
    top: 37%;
    left: -20px;
    opacity: 0.5;
    z-index: -1;
    transform: translateY(-50%);
}

@media only screen and (min-width: 1701px) and (max-width: 1800px), only screen and (min-width: 1600px) and (max-width: 1700px), only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-blog-2-shape {
        left: -15px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-blog-2-shape {
        display: none;
    }
}

.tp-blog-2-title {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 30px;
    color: var(--tp-theme-secondary);
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-blog-2-title {
        font-size: 22px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 575px) {
    .tp-blog-2-title {
        font-size: 20px;
    }
}

.tp-blog-2-title a:hover {
    color: var(--tp-theme-primary);
}

.tp-blog-3-item:hover .tp-blog-3-thumb::before {
    -webkit-transform: scale(1, 1);
    transform: scale(1, 1);
    -webkit-transform-origin: left center;
    transform-origin: left center;
}

.tp-blog-3-item:hover .tp-blog-3-icon {
    top: 50%;
    opacity: 1;
    visibility: visible;
}

.tp-blog-3-thumb::before {
    position: absolute;
    content: "";
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    transform: scale(0, 1);
    -webkit-transform: scale(0, 1);
    -webkit-transform-origin: right center;
    transform-origin: right center;
    transition: transform 0.5s ease, -webkit-transform 0.5s ease;
    background-position: center center;
    background: rgba(10, 18, 41, 0.6);
    z-index: 1;
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-blog-3-thumb img {
        width: 100%;
    }
}

.tp-blog-3-content {
    position: relative;
    z-index: 1;
    margin: 0 29px;
    margin-top: -30px;
    padding: 30px 22px;
    background-color: rgb(255, 255, 255);
    box-shadow: 0px 4px 60px 0px rgba(18, 14, 14, 0.06);
}

@media only screen and (min-width: 992px) and (max-width: 1199px), (max-width: 575px) {
    .tp-blog-3-content {
        margin: 0 10px;
        margin-top: -30px;
        padding: 20px;
    }
}

.tp-blog-3-icon {
    position: absolute;
    top: 30%;
    left: 50%;
    z-index: 3;
    opacity: 0;
    visibility: hidden;
    transition: 0.5s;
    transform: translate(-50%, -50%);
}

.tp-blog-3-icon a {
    display: inline-block;
    line-height: 65px;
    height: 60px;
    width: 60px;
    border-radius: 50%;
    text-align: center;
    font-size: 20px;
    color: var(--tp-common-white);
    background: var(--tp-theme-primary);
}

.tp-blog-3-title {
    font-size: 22px;
    font-weight: 700;
    line-height: 28px;
    letter-spacing: 0.02px;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-blog-3-title {
        font-size: 20px;
    }
}

.tp-blog-3-title a:hover {
    background-size: 0% 1px, 100% 1px;
}

.tp-blog-3-title a {
    background-repeat: no-repeat;
    background-size: 0% 1px, 0 1px;
    background-position: 100% 100%, 0 100%;
    transition: background-size 0.4s linear;
    background-image: linear-gradient(#072b64, #072b64), linear-gradient(#072b64, #072b64);
}

.tp-blog-3-meta span {
    font-size: 16px;
    font-weight: 400;
    color: var(--tp-text-body);
}

.tp-blog-3-meta span:not(:last-of-type) {
    margin-right: 30px;
}

.tp-blog-3-meta span i {
    margin-right: 8px;
    transform: translateY(0px);
    color: var(--tp-theme-primary);
}

.tp-blog-6-bg {
    position: absolute;
    top: 0;
    left: 0;
    height: 64%;
    width: 100%;
    z-index: -1;
}

/*----------------------------------------*/
/*  5.1 Postbox css
/*----------------------------------------*/
@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-postbox-area {
        padding-top: 80px;
        padding-bottom: 80px;
    }
}

.tp-postbox-wrapper {
    padding-right: 30px;
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-postbox-wrapper {
        padding-right: 0;
        margin-bottom: 50px;
    }
}

.tp-postbox-thumb img {
    width: 100%;
}

.tp-postbox-thumb-video {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.tp-postbox-thumb-video a {
    height: 90px;
    width: 90px;
    line-height: 90px;
    font-size: 26px;
    text-align: center;
    border-radius: 50%;
    display: inline-block;
    color: var(--tp-common-white);
    background: rgba(0, 35, 90, 0.651);
}

.tp-postbox-thumb-video a::after {
    position: absolute;
    content: "";
    left: 50%;
    top: 50%;
    width: 100%;
    height: 100%;
    background: rgba(0, 35, 90, 0.651);
    /*animation: borderanimate2 2s linear infinite;*/
    z-index: -1;
    border-radius: 50%;
}

.tp-postbox-content {
    padding: 20px 0;
}

.tp-postbox-content p {
    margin-bottom: 40px;
}

.tp-postbox-meta {
    margin-bottom: 15px;
}

.tp-postbox-meta span {
    font-size: 16px;
    font-weight: 500;
    color: #565969;
}

.tp-postbox-title {
    font-size: 36px;
    font-weight: 700;
    letter-spacing: -1px;
    margin-bottom: 15px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-postbox-title {
        font-size: 30px;
    }
}

.tp-postbox-title a:hover {
    color: var(--tp-theme-primary);
}

.tp-postbox-text {
    margin-bottom: 30px;
}

.tp-postbox-text p {
    line-height: 30px;
}

.tp-postbox-read-more .tp-btn {
    padding: 12px 51px;
}

.tp-postbox-nav button {
    position: absolute;
    top: 50%;
    left: -35px;
    height: 100px;
    width: 100px;
    border-radius: 50%;
    font-size: 26px;
    transform: translateY(-50%);
    color: var(--tp-common-black);
    background: var(--tp-common-white);
    z-index: 2;
}

.tp-postbox-nav button:hover {
    color: var(--tp-common-white);
    background: var(--tp-theme-primary);
}

.tp-postbox-nav button.tp-blog-next-1 {
    right: -35px;
    left: auto;
}

.tp-postbox-blockquote {
    margin-bottom: 50px;
}

.tp-postbox-photo {
    margin-bottom: 50px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-postbox-photo {
        flex-wrap: wrap;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-postbox-photo-thumb:not(:last-of-type) {
        margin-bottom: 20px;
    }
}

@media (max-width: 575px) {
    .tp-postbox-photo-thumb img {
        width: 100%;
    }
}

@media (max-width: 575px) {
    .tp-postbox-photo-thumb {
        margin-right: 0;
    }
}

.tp-postbox-share-wrapper {
    padding-bottom: 30px;
    border-bottom: 1px solid rgba(15, 13, 29, 0.102);
}

.tp-postbox-share-social a {
    border-radius: 50%;
}

.tp-postbox-share-social .tp-footer-widget-social a:not(:last-of-type) {
    margin-right: 10px;
}

.tp-postbox-comment .tp-postbox-title {
    margin-bottom: 50px;
}

.tp-postbox-comment ul li {
    list-style: none;
}

.tp-postbox-comment ul li.children {
    margin-left: 82px;
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-postbox-comment ul li.children {
        margin-left: 0;
    }
}

.tp-postbox-comment ul li .tp-postbox-comment-box {
    border-bottom: 1px solid rgba(15, 13, 29, 0.102);
    padding-bottom: 55px;
    margin-bottom: 30px;
}

@media (max-width: 575px) {
    .tp-postbox-comment ul li .tp-postbox-comment-box-inner {
        flex-wrap: wrap;
    }
}

.tp-postbox-comment ul li .tp-postbox-comment-avater {
    margin-right: 45px;
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-postbox-comment ul li .tp-postbox-comment-avater {
        margin-right: 30px;
    }
}

@media (max-width: 575px) {
    .tp-postbox-comment ul li .tp-postbox-comment-avater {
        margin-bottom: 10px;
    }
}

.tp-postbox-comment ul li .tp-postbox-comment-avater img {
    border-radius: 50%;
}

.tp-postbox-comment ul li .tp-postbox-comment-content p {
    margin-bottom: 16px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-postbox-comment ul li .tp-postbox-comment-content p br {
        display: none;
    }
}

.tp-postbox-comment ul li .tp-postbox-comment-name {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 10px;
}

.tp-postbox-comment ul li .tp-postbox-comment-date {
    margin-bottom: 16px;
}

.tp-postbox-comment ul li .tp-postbox-comment-reply a {
    font-size: 14px;
    font-weight: 600;
    color: var(--tp-theme-primary);
}

.tp-postbox-comment ul li .tp-postbox-comment-reply a i {
    font-weight: 600;
    margin-right: 5px;
}

.tp-postbox-comment ul li .tp-postbox-comment-reply a:hover {
    color: var(--tp-theme-secondary);
}

.tp-postbox-gellary-box {
    padding: 45px 60px;
    background: var(--tp-theme-secondary);
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-postbox-gellary-box {
        padding: 40px 25px;
    }
}

@media (max-width: 575px) {
    .tp-postbox-gellary-box {
        flex-wrap: wrap;
    }
}

@media (max-width: 575px) {
    .tp-postbox-gellary-thumb {
        margin-bottom: 30px;
    }
}

.tp-postbox-gellary-thumb img {
    border-radius: 50%;
}

.tp-postbox-gellary-content p {
    font-size: 16px;
    margin-bottom: 20px;
    line-height: 26px;
    color: var(--tp-common-white);
}

.tp-postbox-gellary-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    color: var(--tp-common-white);
}

.tp-postbox-gellary-social a {
    color: var(--tp-common-white);
}

.tp-postbox-gellary-social a:not(:last-of-type) {
    margin-right: 20px;
}

.tp-postbox-reply .tp-postbox-title {
    margin-bottom: 50px;
}

.tp-postbox-reply .tp-postbox-input {
    margin-bottom: 30px;
}

.tp-postbox-reply .tp-postbox-input input, .tp-postbox-reply .tp-postbox-input textarea {
    font-size: 16px;
    font-weight: 400;
    height: 58px;
    color: #565969;
    background-color: rgb(248, 248, 248);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.tp-postbox-reply .tp-postbox-input input::placeholder, .tp-postbox-reply .tp-postbox-input textarea::placeholder {
    font-size: 16px;
    font-weight: 400;
    color: #565969;
}

.tp-postbox-reply .tp-postbox-input input:focus, .tp-postbox-reply .tp-postbox-input textarea:focus {
    border: 1px solid var(--tp-theme-primary);
}

.tp-postbox-reply .tp-postbox-input textarea {
    height: 148px;
    resize: none;
}

.tp-postbox-share-social .tp-footer-widget-social a {
    overflow: hidden;
}

.tp-postbox-share-social .tp-footer-widget-social a::after {
    border-radius: 50%;
}

blockquote {
    position: relative;
    background: rgba(255, 255, 255, 0.0500000007);
    margin: 0;
}

blockquote p {
    font-size: 26px;
    font-weight: 700;
    margin: 0 !important;
    color: var(--tp-theme-secondary);
    font-family: var(--tp-ff-heading);
}

@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    blockquote p br {
        display: none;
    }
}

blockquote cite {
    position: relative;
    font-style: normal;
    margin-top: 20px;
    display: block;
}

blockquote cite::before {
    position: relative;
    content: "";
    padding-bottom: 0px;
    display: inline-block;
    height: 2px;
    width: 30px;
    text-align: center;
    top: -4px;
    margin-right: 10px;
    background: var(--tp-theme-primary);
}

blockquote img {
    margin-right: 30px;
}

/*----------------------------------------*/
/*  5.2 Recent Post css
/*----------------------------------------*/
.tp-rc__post:not(:last-of-type) {
    margin-bottom: 30px;
}

.tp-rc__post:hover .tp-rc__post-thumb img {
    transform: scale(1.08);
}

.tp-rc__post-title {
    font-size: 16px;
    font-weight: 600;
    line-height: 22px;
    margin-bottom: 15px;
}

.tp-rc__post-title a:hover {
    color: var(--tp-theme-primary);
}

.tp-rc__post-thumb {
    overflow: hidden;
    flex: 0 0 auto;
}

.tp-rc__post-thumb img {
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-rc__post-meta span {
    font-size: 16px;
    font-weight: 400;
    color: #565969;
    display: inline-block;
    margin-bottom: 10px;
}

.tp-rc__post-meta span i {
    color: var(--tp-theme-primary);
    margin-right: 5px;
}

/*----------------------------------------*/
/*  5.3 Sidebar css
/*----------------------------------------*/
.tp-sidebar-wrapper {
    position: sticky;
    top: 180px;
}

.tp-sidebar-widget-title {
    font-size: 30px;
    font-weight: 700;
    margin-bottom: 35px;
    letter-spacing: -0.2px;
    line-height: 1;
}

.tp-sidebar-widget-title-2 {
    font-size: 22px;
    font-weight: 700;
    display: inline-block;
    margin-bottom: 0;
    margin-right: 10px;
}

.tp-sidebar-widget ul li {
    list-style: none;
}

.tp-sidebar-widget ul li:not(:last-of-type) {
    margin-bottom: 20px;
}

.tp-sidebar-widget ul li a {
    position: relative;
    font-size: 16px;
    color: #565969;
    transition: all 0.3s 0s ease-out;
    line-height: 1;
    font-weight: 400;
}

.tp-sidebar-widget ul li a.active {
    color: var(--tp-theme-primary);
}

.tp-sidebar-widget ul li a::after {
    position: absolute;
    content: "";
    bottom: 0;
    left: 0;
    height: 1px;
    width: 100%;
    opacity: 0;
    transform: translateY(1px);
    transition: 0.15s all cubic-bezier(0.39, 0.575, 0.565, 1);
    background: var(--tp-theme-primary);
}

.tp-sidebar-widget ul li a:hover {
    color: var(--tp-theme-primary);
}

.tp-sidebar-widget ul li a:hover::after {
    opacity: 1;
    transform: translateY(0px) translateZ(5px);
}

.tp-sidebar-widget-gellary {
    display: grid;
    grid: auto auto/auto auto auto;
    column-gap: 10px;
    row-gap: 10px;
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-sidebar-widget-gellary {
        width: 330px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px), (max-width: 575px) {
    .tp-sidebar-widget-gellary {
        width: 300px;
    }
}

.tp-sidebar-widget-gellary-item {
    position: relative;
}

.tp-sidebar-widget-gellary-thumb {
    position: relative;
    display: inline-block;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), (max-width: 575px) {
    .tp-sidebar-widget-gellary-thumb img {
        max-width: 100%;
    }
}

.tp-sidebar-widget-gellary-thumb::after {
    position: absolute;
    content: "";
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    transform: scale(0);
    transition: all 0.3s 0s ease-out;
    background: rgba(0, 35, 90, 0.8);
}

.tp-sidebar-widget-gellary-thumb:hover::after {
    transform: scale(1);
}

.tp-sidebar-widget-gellary-thumb:hover .tp-sidebar-widget-gellary-icon {
    opacity: 1;
}

.tp-sidebar-widget-gellary-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: 1;
    opacity: 0;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    transform: translate(-50%, -50%);
}

.tp-sidebar-widget-gellary-icon i {
    color: var(--tp-common-white);
}

.tp-sidebar-widget-about-thumb {
    margin-bottom: 15px;
}

.tp-sidebar-widget-about-title {
    font-size: 22px;
    font-weight: 700;
    letter-spacing: -0.2px;
    margin-bottom: 15px;
    color: var(--tp-theme-secondary);
}

.tp-sidebar-search-input {
    position: relative;
}

.tp-sidebar-search-input input {
    font-size: 16px;
    color: #565969;
    padding-right: 60px;
    height: 42px;
    border: 1px solid rgb(223, 223, 223);
}

.tp-sidebar-search-input input::placeholder {
    font-size: 16px;
    color: #565969;
}

.tp-sidebar-search-input input:focus {
    border: 1px solid var(--tp-theme-primary);
}

.tp-sidebar-search-input button {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    font-weight: 900;
    color: var(--tp-theme-secondary);
    right: 30px;
}

.tp-sidebar-gellary-thumb {
    margin-bottom: 20px;
}

.tp-sidebar-gellary-name {
    font-size: 22px;
    font-weight: 700;
    margin-bottom: 15px;
    color: var(--tp-theme-secondary);
}

.tp-sidebar-work {
    background: var(--tp-theme-primary);
    padding: 40px 60px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-sidebar-work {
        padding: 40px 50px;
    }
}

.tp-sidebar-work-title {
    font-size: 28px;
    font-weight: 500;
    margin-bottom: 15px;
}

.tp-sidebar-work-content p {
    margin-bottom: 30px;
}

.tp-sidebar-work-call-icon i {
    font-size: 24px;
    font-weight: 900;
    transform: translateY(12px);
}

.tp-sidebar-work-call-content span {
    display: block;
    text-align: start;
}

.tagcloud a {
    font-size: 16px;
    color: #565969;
    padding: 10px 21px;
    display: inline-block;
    background: #f5f5f8;
    margin-bottom: 12px;
    margin-right: 10px;
    line-height: 1;
}

.tagcloud a:hover {
    background-color: var(--tp-theme-primary);
    border-color: var(--tp-theme-primary);
    color: var(--tp-common-white);
}

/*----------------------------------------*/
/*  6.1 Footer Style 1
/*----------------------------------------*/
.tp-footer-fixed {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: -1;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-footer-fixed {
        position: relative;
        z-index: inherit;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-footer-shape {
        display: none;
    }
}

.tp-footer-shape .shape-1 {
    position: absolute;
    top: -120px;
    left: 0;
    z-index: 0;
}

.tp-footer-shape .shape-2 {
    position: absolute;
    bottom: 0px;
    right: 0;
    z-index: 0;
}

.tp-footer-bg::after {
    position: absolute;
    content: "";
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    background-color: rgb(10, 18, 41);
}

.tp-footer-main {
    position: relative;
    padding-bottom: 70px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-footer-main {
        padding-bottom: 0;
    }
}

.tp-footer-main.tp-footer-6 .tp-footer-widget-social a:hover {
    background: var(--tp-theme-4);
}

.tp-footer-main.tp-footer-6 .tp-footer-widget-content ul li a:hover {
    color: var(--tp-theme-4);
}

.tp-footer-border {
    border-bottom: 1px solid rgb(54, 66, 103);
}

.tp-footer-widget-content p {
    font-size: 16px;
    color: #9ca5af;
    font-weight: 500;
    margin-bottom: 25px;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-footer-widget-content p br {
        display: none;
    }
}

.tp-footer-widget-content ul li {
    list-style: none;
}

.tp-footer-widget-content ul li:not(:last-of-type) {
    margin-bottom: 10px;
}

.tp-footer-widget-content ul li a {
    position: relative;
    font-size: 16px;
    font-weight: 500;
    color: #9ca5af;
}

.tp-footer-widget-content ul li a::before {
    position: absolute;
    content: "";
    height: 6px;
    width: 6px;
    left: -15px;
    top: 50%;
    opacity: 0;
    transform: translateY(-50%);
    border: 1px solid var(--tp-common-white);
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-footer-widget-content ul li a:hover {
    color: var(--tp-theme-primary);
    margin-left: 15px;
}

.tp-footer-widget-content ul li a:hover::before {
    opacity: 1;
}

.tp-footer-widget-content-input form {
    position: relative;
}

.tp-footer-widget-content-input form input {
    color: var(--tp-common-white);
    background: transparent;
    border-color: rgb(30, 43, 82);
    height: 52px;
    line-height: 52px;
    padding-right: 60px;
}

.tp-footer-widget-content-input form input::placeholder {
    color: #9ca5af;
}

.tp-footer-widget-content-input form input:focus {
    border-color: var(--tp-theme-primary);
}

.tp-footer-widget-content-input form button {
    position: absolute;
    top: 0;
    right: 0;
    height: 52px;
    width: 52px;
    line-height: 52px;
    color: var(--tp-common-white);
    background: var(--tp-theme-primary);
}

.tp-footer-widget-content-input form button:hover {
    color: var(--tp-common-white);
    background-color: var(--tp-theme-primary);
}

.tp-footer-widget-gallery-all {
    display: grid;
    grid: auto auto/auto auto auto;
    column-gap: 10px;
    row-gap: 10px;
    width: 230px;
    height: auto;
}

.tp-footer-widget-gallery-item {
    position: relative;
    display: inline-block;
}

.tp-footer-widget-gallery-item:hover .tp-footer-widget-gallery-thumb::after {
    transform: scale(1);
}

.tp-footer-widget-gallery-item:hover .tp-footer-widget-gallery-social i {
    opacity: 1;
    visibility: visible;
}

.tp-footer-widget-gallery-thumb {
    position: relative;
    display: inline-block;
}

.tp-footer-widget-gallery-thumb::after {
    position: absolute;
    content: "";
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    transform: scale(0);
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    background-color: rgb(255, 94, 20);
}

.tp-footer-widget-gallery-thumb img {
    max-width: 100%;
}

.tp-footer-widget-gallery-social {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
}

.tp-footer-widget-gallery-social i {
    opacity: 0;
    color: var(--tp-common-white);
    visibility: hidden;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-footer-widget-social a {
    position: relative;
    display: inline-block;
    width: 38px;
    height: 38px;
    line-height: 38px;
    text-align: center;
    color: var(--tp-common-white);
    z-index: 1;
    background-color: rgb(23, 33, 64);
}

.tp-footer-widget-social a i {
    position: relative;
    z-index: 2;
}

.tp-footer-widget-social a:not(:last-of-type) {
    margin-right: 15px;
}

.tp-footer-widget-social a::after {
    position: absolute;
    content: "";
    height: 0;
    width: 100%;
    top: 0;
    left: 0;
    z-index: 0;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    background-color: var(--tp-theme-primary);
}

.tp-footer-widget-social a:hover::after {
    height: 100%;
    top: auto;
    bottom: 0;
}

.tp-footer-widget-title {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 30px;
    color: var(--tp-common-white);
}

.tp-footer-copyright {
    position: relative;
    padding-top: 35px;
    padding-bottom: 35px;
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-footer-copyright-inner {
        margin-bottom: 10px;
    }
}

.tp-footer-copyright-inner p {
    color: #9ca5af;
}

.tp-footer-copyright-inner a {
    color: #9ca5af;
}

.tp-footer-copyright-inner a:hover {
    color: var(--tp-theme-primary);
}

.tp-footer-copyright-inner .dvd {
    display: inline-block;
    padding: 0 4px;
    color: #9ca5af;
}

.tp-footer-comming-inner {
    position: relative;
    z-index: 1;
}

.tp-footer-comming-inner p {
    color: #9ca5af;
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-footer-comming-inner p {
        margin-bottom: 20px;
    }
}

.tp-footer-6-blog {
    margin-bottom: 20px;
}

.tp-footer-6-blog-content span {
    font-size: 16px;
    font-weight: 400;
    color: #9ca5af;
    display: inline-block;
}

.tp-footer-6-blog-content span i {
    color: var(--tp-theme-4);
    margin-right: 5px;
    transform: translateY(2px);
}

.tp-footer-6-blog-thumb {
    margin-right: 20px;
}

.tp-footer-6-blog-title {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 15px;
    color: var(--tp-common-white);
}

.tp-footer-6-blog-title a:hover {
    color: var(--tp-theme-4);
}

/* footer col design for home 1 */
.tp-footer-col-1 {
    margin-right: -30px;
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-footer-col-1 {
        margin-right: 0;
    }
}

.tp-footer-col-2 {
    padding-left: 70px;
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-footer-col-2 {
        padding-left: 0;
    }
}

.tp-footer-col-4 {
    margin-left: -30px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-footer-col-4 {
        margin-left: 0;
    }
}

/*----------------------------------------*/
/*  6.2 Footer Style 2
/*----------------------------------------*/
/* footer col design for home 1 */
/*----------------------------------------*/
/*  6.3 Footer Style 3
/*----------------------------------------*/
.tp-footer-6-bg {
    position: absolute;
    content: "";
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    background-color: rgb(1, 13, 20);
}

.tp-footer-6-post:not(:last-of-type) {
    margin-bottom: 10px;
}

.tp-footer-6-post-title {
    font-size: 18px;
    font-weight: 700;
    letter-spacing: 0.16px;
    line-height: 24px;
    margin-bottom: 10px;
    color: var(--tp-common-white);
}

.tp-footer-6-post-title a:hover {
    color: var(--tp-theme-4);
}

.tp-footer-6-post-meta span {
    font-size: 16px;
    line-height: 32px;
    color: #9ca5af;
}

.tp-footer-6-post-meta span img {
    margin-right: 5px;
    transform: translateY(-1px);
}

/* footer col design for home 1 */
.tp-footer-6-col-1 {
    margin-right: -30px;
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-footer-6-col-1 {
        margin-right: 0;
    }
}

.tp-footer-6-col-1 .tp-footer-widget-social a::after {
    background: var(--tp-theme-4);
}

.tp-footer-6-col-1 .tp-footer-widget-content p {
    line-height: 36px;
}

.tp-footer-6-col-2 {
    padding-left: 70px;
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-footer-6-col-2 {
        padding-left: 0;
    }
}

.tp-footer-6-col-2 .tp-footer-widget-content ul li a:hover {
    color: var(--tp-theme-4);
}

.tp-footer-6-col-3 .tp-footer-widget-content ul li a:hover {
    color: var(--tp-theme-4);
}

.tp-footer-6-col-4 {
    margin-left: -30px;
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-footer-6-col-4 {
        margin-left: 0;
    }
}

.tp-footer-7-col-3 {
    margin-left: -30px;
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-footer-7-col-3 {
        margin-left: 0;
    }
}

/*----------------------------------------*/
/*  7.1 Hero Css
/*----------------------------------------*/
.tp-hero-bg::after {
    position: absolute;
    content: "";
    height: 100%;
    width: 100%;
    top: 0;
    left: 0;
    z-index: 0;
    background: url(../img/hero/hero-bg-shape.png) no-repeat;
}

.tp-hero-content {
    padding-top: 250px;
    z-index: 8;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-hero-content {
        padding-top: 200px;
    }
}

@media (max-width: 575px) {
    .tp-hero-content {
        padding-top: 150px;
    }
}

.tp-hero-title {
    /*font-size: 70px;*/
    font-size: 55px;
    font-weight: 800;
    margin-bottom: 45px;
    line-height: 1.26;
    letter-spacing: -2px;
    color: var(--tp-common-white);
}

.tp-hero-title.home-3 {
    font-size: 110px;
    line-height: 1;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-hero-title.home-3 {
        font-size: 80px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
    .tp-hero-title.home-3 {
        font-size: 66px;
        line-height: 1;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-hero-title.home-3 {
        font-size: 65px;
        line-height: 1;
        margin-bottom: 40px;
    }
}

@media (max-width: 575px) {
    .tp-hero-title.home-3 {
        font-size: 55px;
        line-height: 1.2;
        margin-bottom: 40px;
    }
}

.tp-hero-title div {
    display: block;
    overflow: hidden;
}

.tp-hero-title div span {
    position: relative;
    display: block;
    opacity: 0;
    transform: translateY(200px);
    -webkit-transform: translateY(200px);
    transition: 1s;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-hero-title {
        font-size: 46px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
    .tp-hero-title {
        font-size: 44px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-hero-title {
        font-size: 38px;
    }
}

@media (max-width: 575px) {
    .tp-hero-title {
        font-size: 32px;
        /*font-size: 47px;*/
        /*font-size: 50px;*/
        margin-bottom: 30px;
    }
}

.tp-hero-title-pre {
    font-size: 14px;
    font-weight: 400;
    text-transform: uppercase;
    color: var(--tp-common-white);
    display: inline-block;
    margin-bottom: 6px;
    letter-spacing: 0.3px;
}

.tp-hero-title-pre div {
    display: block;
    overflow: hidden;
}

.tp-hero-title-pre div span {
    position: relative;
    display: block;
    opacity: 0;
    transform: translateY(200px);
    -webkit-transform: translateY(200px);
    transition: 1s;
}

.tp-hero-title-pre img {
    margin-right: 10px;
    margin-top: -5px;
}

.tp-hero-btn:not(:last-of-type) {
    margin-right: 30px;
}

.tp-hero-button-wrapper .padding-hero-btn {
    padding-left: 13%;
}

@media (max-width: 575px) {
    /*.tp-hero-btn:not(:last-of-type) {*/
    .tp-hero-btn {
        margin-bottom: 20px;
    }

    .tp-hero-button-wrapper .padding-hero-btn {
        padding-left: 5%;
    }

    .canvas-slider .slide-img {
        transform: translateX(-28%);
    }
}

.tp-hero-btn.style-2 .tp-btn {
    background: var(--tp-common-white);
    color: var(--tp-theme-secondary);
}

.tp-hero-button-wrapper {
    opacity: 0;
    overflow: hidden;
    transform: translateY(200px);
    -webkit-transform: translateY(200px);
    transition: 1.5s;
}

@media (max-width: 575px) {
    .tp-hero-button-wrapper {
        flex-wrap: wrap;
    }
}

@media (max-width: 575px) {
    .tp-hero-button-2-wrapper .tp-hero-2-btn {
        margin-bottom: 20px;
    }
}

.tp-hero-active .swiper-wrapper .swiper-slide.swiper-slide-active .tp-hero-title div span {
    opacity: 1;
    transform: translateY(0px);
    -webkit-transform: translateY(0px);
}

.slide-wrap.overlay {
    position: absolute;
    height: 100%;
    width: 100%;
    top: 0;
    left: 0;
    z-index: 0;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    background-color: rgba(1, 9, 21, 0.4);
}

.swiper-slide-active .tp-hero-button-wrapper {
    opacity: 1;
    transform: translateY(0px);
    -webkit-transform: translateY(0px);
}

.swiper-slide-active .tp-hero-title-pre div span {
    opacity: 1;
    transform: translateY(0px);
    -webkit-transform: translateY(0px);
}

.swiper-slide-active .tp-hero-7-title div span {
    opacity: 1;
}

.tp-hero-2-bg {
    position: absolute;
    top: 0px;
    left: 0;
    height: 100%;
    width: 100%;
    background-repeat: no-repeat;
    background-size: cover;
    z-index: -1;
}

.tp-hero-2-shape {
    position: absolute;
    bottom: 0;
    left: 0;
}

.tp-hero-2-content {
    padding-top: 110px;
    padding-left: 375px;
}

@media only screen and (min-width: 1701px) and (max-width: 1800px) {
    .tp-hero-2-content {
        padding-top: 110px;
        padding-left: 260px;
    }
}

@media only screen and (min-width: 1600px) and (max-width: 1700px) {
    .tp-hero-2-content {
        padding-left: 230px;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .tp-hero-2-content {
        padding-left: 135px;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-hero-2-content {
        padding-left: 100px;
        padding-top: 100px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-hero-2-content {
        padding-bottom: 40px;
        padding-left: 30px;
        padding-top: 0;
    }
}

.tp-hero-2-stock-color {
    position: absolute;
    top: 0px;
    left: 170px;
    z-index: 0;
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .tp-hero-2-stock-color {
        left: 40px;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-hero-2-stock-color {
        left: 30px;
    }
}

.tp-hero-2-curve {
    position: absolute;
    top: 15%;
    right: -50px;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-hero-2-curve {
        top: 0;
        right: 0;
    }
}

@media (max-width: 575px) {
    .tp-hero-2-curve {
        display: none;
    }
}

.tp-hero-2-text {
    font-size: 180px;
    font-weight: 700;
    margin: 0;
    color: transparent;
    -webkit-text-stroke: 3px white;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-hero-2-text {
        font-size: 170px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-hero-2-text {
        font-size: 145px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-hero-2-text {
        font-size: 155px;
    }
}

@media (max-width: 575px) {
    .tp-hero-2-text {
        display: none;
    }
}

.tp-hero-2-title {
    font-size: 90px;
    font-weight: 700;
    line-height: 90px;
    letter-spacing: -4px;
    margin-bottom: 60px;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-hero-2-title {
        font-size: 80px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-hero-2-title {
        font-size: 66px;
        line-height: 1;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-hero-2-title {
        font-size: 65px;
        line-height: 1;
        margin-bottom: 40px;
    }
}

@media (max-width: 575px) {
    .tp-hero-2-title {
        font-size: 55px;
        line-height: 1.2;
        margin-bottom: 40px;
    }
}

.tp-hero-2-btn {
    margin-right: 30px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-hero-2-btn {
        margin-right: 15px;
    }
}

.tp-hero-2-btn .tp-btn {
    padding: 18px 44px;
}

.tp-hero-2-wrapper-slider {
    padding-left: 70px;
    margin-right: -700px;
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .tp-hero-2-wrapper-slider {
        margin-right: -560px;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-hero-2-wrapper-slider {
        margin-right: -500px;
        padding-left: 30px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-hero-2-wrapper-slider {
        padding-left: 30px;
        margin-right: 0;
        padding-right: 30px;
    }
}

.tp-hero-2-wrapper-slider .swiper-slide {
    background: #f8f9fd;
}

.tp-hero-2-nav {
    position: absolute;
    bottom: 0;
    left: 70px;
    z-index: 1;
}

.tp-hero-2-nav button {
    width: 82px;
    font-size: 20px;
    height: 60px;
    line-height: 60px;
    background: var(--tp-common-white);
}

.tp-hero-2-nav button.hero-2-next {
    margin-left: -20px;
}

.tp-hero-2-nav button:hover {
    color: var(--tp-common-white);
    background: var(--tp-theme-primary);
}

.tp-hero-3-stock-color {
    position: absolute;
    top: 125px;
    left: -102px;
}

@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-hero-3-stock-color {
        left: 0;
    }
}

@media (max-width: 575px) {
    .tp-hero-3-stock-color {
        display: none;
    }
}

.tp-hero-3-text {
    font-size: 190px;
    font-weight: 700;
    color: transparent;
    margin: 0;
    -webkit-text-stroke: 1px rgba(244, 245, 247, 0.3);
}

@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
    .tp-hero-3-text {
        font-size: 150px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-hero-3-text {
        font-size: 100px;
    }
}

.tp-hero-3-btn .tp-btn {
    margin-right: 30px;
    background: var(--tp-theme-secondary);
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-hero-3-btn .tp-btn {
        padding: 18px 30px;
    }
}

@media (max-width: 575px) {
    .tp-hero-3-btn .tp-btn {
        margin-bottom: 20px;
    }
}

.tp-hero-3-content {
    padding-top: 240px;
    opacity: 0;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-hero-3-content {
        padding-top: 200px;
    }
}

@media (max-width: 575px) {
    .tp-hero-3-content {
        padding-top: 150px;
    }
}

.tp-hero-3-title {
    font-size: 100px;
    font-weight: 800;
    margin-bottom: 35px;
    color: var(--tp-common-white);
}

.tp-hero-4-hight {
    height: 100vh;
}

.tp-hero-4-shape {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 0;
}

.tp-hero-4-sidebar {
    position: absolute;
    top: 0px;
    left: 60px;
    z-index: 2;
}

.tp-hero-4-sidebar-box {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    width: 190px;
    height: 100vh;
}

.tp-hero-4-menu-btn a span {
    cursor: pointer;
}

.tp-hero-4-menu-btn a svg .cls-1, .tp-hero-4-menu-btn a svg .cls-2, .tp-hero-4-menu-btn a svg .cls-3 {
    fill: var(--tp-theme-secondary);
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-hero-4-menu-btn a:hover svg .cls-1, .tp-hero-4-menu-btn a:hover svg .cls-2, .tp-hero-4-menu-btn a:hover svg .cls-3 {
    fill: var(--tp-theme-primary);
}

.tp-hero-4-menu-btn a:hover svg .cls-2 {
    height: 16px;
}

.tp-hero-4-menu-btn a:hover svg .cls-1, .tp-hero-4-menu-btn a:hover svg .cls-3 {
    height: 26px;
}

.tp-hero-4-wrapper-box {
    padding-top: 100px;
}

.tp-hero-4-wrapper-box::after {
    position: absolute;
    content: "";
    height: 0px;
    width: 0px;
    border-radius: 50%;
    right: -100px;
    bottom: -100px;
    z-index: -1;
    transition: all 0.5s ease-in;
    background: var(--tp-theme-primary);
}

.tp-hero-4-wrap {
    background-position: top right;
    background-repeat: no-repeat;
    min-height: 765px;
}

.tp-hero-4-title {
    font-size: 140px;
    font-weight: 800;
    letter-spacing: -2px;
    line-height: 0.93;
    margin-bottom: 48px;
    color: var(--tp-theme-secondary);
}

@media only screen and (min-width: 1701px) and (max-width: 1800px) {
    .tp-hero-4-title {
        font-size: 120px;
    }
}

.tp-hero-4-title div {
    display: block;
    overflow: hidden;
}

.tp-hero-4-title div span {
    position: relative;
    display: block;
    opacity: 0;
    transform: translateY(160px);
    -webkit-transform: translateY(160px);
    transition: 0.7s;
}

.tp-hero-4-title.orange {
    color: var(--tp-theme-primary);
}

.tp-hero-4-title.black {
    color: var(--tp-common-black);
}

.tp-hero-4-white-bg {
    overflow: hidden;
}

.tp-hero-4-style-2 {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.tp-hero-4-text-white {
    font-size: 140px;
    font-weight: 800;
    letter-spacing: -2px;
    line-height: 0.93;
    margin-bottom: 48px;
    color: var(--tp-common-white);
}

@media only screen and (min-width: 1701px) and (max-width: 1800px) {
    .tp-hero-4-text-white {
        font-size: 120px;
    }
}

.tp-hero-4-text-white div {
    display: block;
    overflow: hidden;
}

.tp-hero-4-text-white div span {
    position: relative;
    display: block;
    opacity: 0;
    transform: translateY(160px);
    -webkit-transform: translateY(160px);
    transition: 0.7s;
}

.tp-hero-4-social a {
    display: table;
}

.tp-hero-4-social a i {
    color: var(--tp-theme-secondary);
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-hero-4-social a:not(:last-of-type) {
    margin-bottom: 20px;
}

.tp-hero-4-social a:hover i {
    color: var(--tp-theme-primary);
}

.tp-hero-4-btn {
    /*animation-delay: 0.4s;*/
    /*animation-duration: 1s;*/
}

.tp-hero-4-btn.blue .tp-btn {
    background: var(--tp-theme-secondary);
}

.tp-hero-4-btn.black .tp-btn {
    background: var(--tp-common-black);
}

.tp-hero-4-active .swiper-wrapper .swiper-slide.swiper-slide-active .tp-hero-4-title div span, .tp-hero-4-active .swiper-wrapper .swiper-slide.swiper-slide-active .tp-hero-4-text-white div span {
    opacity: 1;
    transform: translateY(0px);
    -webkit-transform: translateY(0px);
}

.tp-hero-4-active .swiper-wrapper .swiper-slide.swiper-slide-active .tp-hero-4-btn {
    animation-fill-mode: both;
    animation-name: fadeInUp;
}

.tp-hero-4-active .swiper-wrapper .swiper-slide.swiper-slide-active .tp-hero-4-wrapper-box::after {
    width: 544px;
    height: 544px;
}

.tp-hero-4-active .swiper-wrapper .swiper-slide.swiper-slide-active .tp-hero-4-wrapper-box.blue::after {
    background: var(--tp-theme-secondary);
}

.tp-hero-4-active .swiper-wrapper .swiper-slide.swiper-slide-active .tp-hero-4-wrapper-box.black::after {
    background: var(--tp-common-black);
}

.tp-hero-4-pagination {
    position: absolute;
    left: 375px !important;
    z-index: 1;
    bottom: 100px !important;
    width: auto !important;
}

.tp-hero-4-pagination .swiper-pagination-bullet {
    height: 20px;
    width: 20px;
    opacity: 1;
    display: inline-block;
    margin-bottom: 10px;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    background: var(--tp-theme-secondary);
}

@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-hero-4-pagination .swiper-pagination-bullet {
        display: inline-block;
        margin-right: 10px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-hero-4-pagination .swiper-pagination-bullet {
        height: 10px;
        width: 10px;
    }
}

.tp-hero-4-pagination .swiper-pagination-bullet-active {
    background: var(--tp-theme-primary);
}

.tp-hero-5-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 60%;
    height: 100%;
}

.tp-hero-5-bg span {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    opacity: 0.2;
    background: var(--tp-common-black);
    clip-path: polygon(0 0, 100% 0, 65% 100%, 0% 100%);
}

@media (max-width: 575px) {
    .tp-hero-5-bg span {
        display: none;
    }
}

.tp-hero-5-bg::after {
    position: absolute;
    content: "";
    top: 0;
    right: 0;
    height: 433px;
    width: 232px;
    clip-path: polygon(87.14% -2px, 100.42% -1px, 13px 103.92%, 8.62% 100.69%);
    background: var(--tp-theme-primary);
}

@media only screen and (min-width: 1701px) and (max-width: 1800px) {
    .tp-hero-5-bg::after {
        clip-path: polygon(87.14% -2px, 100.42% -1px, 31px 106.46%, 17.67% 101.62%);
    }
}

@media only screen and (min-width: 1600px) and (max-width: 1700px) {
    .tp-hero-5-bg::after {
        clip-path: polygon(87.14% -2px, 100.42% -1px, 38px 109.23%, 21.98% 101.84%);
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .tp-hero-5-bg::after {
        clip-path: polygon(87.14% -2px, 100.42% -1px, 69px 105.31%);
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-hero-5-bg::after {
        clip-path: polygon(87.14% -2px, 100.42% -1px, 50px 109.23%, 23.27% 107.62%);
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-hero-5-bg::after {
        clip-path: polygon(87.14% -2px, 100.42% -1px, 79px 110.39%, 37.93% 105.54%);
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-hero-5-bg::after {
        display: none;
    }
}

.tp-slider-dot.hero-5 {
    left: 60px;
    right: auto;
    top: 46%;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
    .tp-slider-dot.hero-5 {
        left: auto;
        right: 60px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-slider-dot.hero-5 {
        display: none;
    }
}

.tp-slider-dot.hero-5 .swiper-pagination-bullet {
    width: 15px;
    height: 15px;
    display: block;
}

.tp-slider-dot.hero-5 .swiper-pagination-bullet:not(:last-of-type) {
    margin-bottom: 10px;
}

.tp-slider-dot.hero-5 .swiper-pagination-bullet.swiper-pagination-bullet-active {
    background: var(--tp-theme-primary);
    transform: scale(1);
}

.tp-hero-6-bg {
    background-repeat: no-repeat;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-hero-6-bg {
        padding-top: 160px;
        padding-bottom: 250px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-hero-6-bg {
        padding-top: 130px;
        padding-bottom: 200px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-hero-6-bg {
        background-size: cover;
        padding-top: 100px;
        padding-bottom: 0;
    }
}

@media (max-width: 575px) {
    .tp-hero-6-bg {
        padding-top: 100px;
        padding-bottom: 130px;
    }
}

.tp-hero-6-overlay {
    position: absolute;
    content: "";
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    background-color: rgb(17, 0, 4);
    opacity: 0.702;
}

.tp-hero-6-shape {
    position: absolute;
    bottom: -1px;
    left: 0;
    z-index: 2;
}

.tp-hero-6-shape img {
    width: 100%;
}

.tp-hero-6-content {
    z-index: 2;
}

.tp-hero-6-title {
    font-size: 90px;
    font-weight: 700;
    margin-bottom: 50px;
    letter-spacing: -0.8px;
    color: var(--tp-common-white);
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-hero-6-title {
        font-size: 70px;
        margin-bottom: 40px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-hero-6-title {
        font-size: 55px;
        margin-bottom: 40px;
    }
}

@media (max-width: 575px) {
    .tp-hero-6-title {
        font-size: 50px;
        margin-bottom: 40px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-hero-6-thumb {
        position: relative;
    }
}

.tp-hero-6-thumb .main {
    position: absolute;
    top: 60px;
    right: 170px;
    z-index: 1;
}

@media only screen and (min-width: 1600px) and (max-width: 1700px) {
    .tp-hero-6-thumb .main {
        right: 90px;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .tp-hero-6-thumb .main {
        right: 35px;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-hero-6-thumb .main {
        right: 30px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-hero-6-thumb .main {
        width: 50%;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-hero-6-thumb .main {
        position: static;
        margin-top: 50px;
    }
}

@media (max-width: 575px) {
    .tp-hero-6-thumb .main {
        display: none;
    }
}

.tp-hero-6-thumb .shape-1 {
    position: absolute;
    top: 0;
    right: 60px;
    z-index: 0;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-hero-6-thumb .shape-1 {
        right: 30px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-hero-6-thumb .shape-1 {
        display: none;
    }
}

.tp-hero-6-thumb .shape-2 {
    position: absolute;
    top: 40px;
    right: 250px;
    z-index: 0;
}

@media only screen and (min-width: 1600px) and (max-width: 1700px) {
    .tp-hero-6-thumb .shape-2 {
        right: 170px;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .tp-hero-6-thumb .shape-2 {
        right: 120px;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-hero-6-thumb .shape-2 {
        right: 100px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
    .tp-hero-6-thumb .shape-2 {
        right: 0;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-hero-6-thumb .shape-2 {
        display: none;
    }
}

.tp-hero-7-line {
    position: absolute;
    bottom: 0;
    left: 0;
}

.tp-hero-7-bg {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    background-repeat: no-repeat;
    background-position: top right;
}

.tp-hero-7-shape {
    position: absolute;
    right: 0;
    bottom: 0;
    z-index: 0;
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .tp-hero-7-shape {
        right: -140px;
    }
}

.tp-hero-7-title {
    font-size: 80px;
    font-weight: 700;
    margin-bottom: 60px;
    line-height: 1.16;
    letter-spacing: -2px;
    color: var(--tp-theme-secondary);
    transform: translateY(-100px);
    -webkit-transform: translateY(-100px);
    -webkit-transition-delay: 1000ms;
    transition-delay: 1000ms;
    -webkit-transition: opacity 2000ms ease, -webkit-transform 2000ms ease;
    transition: opacity 2000ms ease, -webkit-transform 2000ms ease;
    transition: transform 2000ms ease, opacity 2000ms ease;
    transition: transform 2000ms ease, opacity 2000ms ease, -webkit-transform 2000ms ease;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-hero-7-title {
        font-size: 72px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-hero-7-title {
        font-size: 65px;
        margin-bottom: 40px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .tp-hero-7-title {
        font-size: 70px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-hero-7-title {
        font-size: 50px;
        margin-bottom: 30px;
    }
}

@media (max-width: 575px) {
    .tp-hero-7-title {
        font-size: 45px;
        margin-bottom: 40px;
    }
}

.tp-hero-7-content {
    opacity: 0;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-hero-7-content {
        padding-top: 120px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-hero-7-content {
        padding-top: 110px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-hero-7-content {
        padding-top: 0;
        padding-bottom: 50px;
    }
}

.tp-hero-7-thumb {
    opacity: 0;
    transform: translateX(500px);
    -webkit-transform: translateX(500px);
    -webkit-transition-delay: 1000ms;
    transition-delay: 1000ms;
    -webkit-transition: opacity 2000ms ease, -webkit-transform 2000ms ease;
    transition: opacity 2000ms ease, -webkit-transform 2000ms ease;
    transition: transform 2000ms ease, opacity 2000ms ease;
    transition: transform 2000ms ease, opacity 2000ms ease, -webkit-transform 2000ms ease;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-hero-7-thumb {
        margin-left: -100px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-hero-7-thumb {
        margin-left: -160px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-hero-7-thumb img {
        width: 100%;
    }
}

.tp-hero-7-wrapper {
    transform: translateY(80px);
    -webkit-transform: translateY(80px);
    -webkit-transition-delay: 1000ms;
    transition-delay: 1000ms;
    -webkit-transition: opacity 2000ms ease, -webkit-transform 2000ms ease;
    transition: opacity 2000ms ease, -webkit-transform 2000ms ease;
    transition: transform 2000ms ease, opacity 2000ms ease;
    transition: transform 2000ms ease, opacity 2000ms ease, -webkit-transform 2000ms ease;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), (max-width: 575px) {
    .tp-hero-7-wrapper .tp-hero-2-btn {
        margin-bottom: 20px;
    }
}

.tp-hero-7-arrow-box {
    position: absolute;
    top: 54%;
    left: 60px;
    z-index: 5;
    transform: translateY(-50%);
}

@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-hero-7-arrow-box {
        display: none;
    }
}

.tp-hero-7-arrow-box button {
    display: block;
    margin-bottom: 10px;
    height: 60px;
    width: 60px;
    font-size: 20px;
    transform: translateY(-50%);
    color: var(--tp-common-white);
    background: var(--tp-theme-secondary);
    z-index: 2;
}

.tp-hero-7-arrow-box button:hover {
    color: var(--tp-theme-secondary);
    background: var(--tp-common-white);
}

.tp-hero-7-active .swiper-wrapper .swiper-slide.swiper-slide-active .tp-hero-7-thumb {
    opacity: 1;
    transform: translateX(0px);
    -webkit-transform: translateX(0px);
}

.tp-hero-7-active .swiper-wrapper .swiper-slide.swiper-slide-active .tp-hero-7-title {
    transform: translateY(0px);
    -webkit-transform: translateY(0px);
}

.tp-hero-7-active .swiper-wrapper .swiper-slide.swiper-slide-active .tp-hero-7-wrapper {
    transform: translateY(0px);
    -webkit-transform: translateY(0px);
}

.tp-hero-7-active .swiper-wrapper .swiper-slide.swiper-slide-active .tp-hero-7-content {
    opacity: 1;
}

.tp-line-wrapper {
    position: absolute;
    top: 0;
    left: 40%;
    z-index: 0;
    bottom: 0px;
    margin: auto;
    width: calc(100% - 1000px);
    transform: translateX(-45%);
    border-right: 1px solid rgba(255, 255, 255, 0.2);
}

@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-line-wrapper {
        width: calc(100% - 200px);
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .tp-line-wrapper {
        width: calc(100% - 80px);
    }
}

.tp-line-wrapper::before {
    width: 1px;
    right: -1px;
    content: "";
    height: 100px;
    position: absolute;
    background-image: linear-gradient(0deg, rgb(255, 255, 255), transparent);
    /*animation: line_anim_2 15s ease-out infinite alternate;*/
    z-index: 1;
}

.tp-line-item {
    width: 30.5%;
    float: left;
    height: 100%;
    position: relative;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .tp-line-item {
        width: 20.5%;
    }
}

.tp-line-item::before {
    width: 1px;
    left: 100%;
    content: "";
    height: 100px;
    position: absolute;
    z-index: 2;
    /*animation: line_anim 15s ease-out infinite alternate;*/
    background-image: linear-gradient(0deg, rgb(255, 255, 255), transparent);
}

.tp-line-item:nth-child(even)::before {
    /*animation: line_anim 15s ease-out infinite alternate;*/
    background-image: linear-gradient(0deg, rgba(0, 0, 0, 0.3), transparent);
}

.tp-sidebar-8-left {
    position: fixed;
    width: 100px;
    height: 100%;
    left: 0;
    top: 0;
    z-index: 98;
    text-align: center;
    background-color: rgb(248, 248, 248);
}

.tp-sidebar-8-content {
    height: 100%;
    padding-top: 40px;
}

.tp-sidebar-8-menu {
    cursor: pointer;
    padding: 40px 0;
    border-top: 1px solid rgb(232, 232, 232);
}

.tp-sidebar-8-menu:hover img {
    filter: brightness(150) invert(2);
}

.tp-sidebar-8-menu .hamburger-btn {
    transform: translateX(-2px);
}

.tp-sidebar-8-social a {
    display: flex;
    justify-content: center;
    color: var(--tp-theme-secondary);
}

.tp-sidebar-8-social a:hover {
    color: var(--tp-theme-primary);
}

.tp-sidebar-8-social a:not(:last-of-type) {
    margin-bottom: 20px;
}

.tp-hero-8-search-box {
    position: fixed;
    top: 0;
    right: 0;
    width: 240px;
    padding: 33px 70px;
    background-color: rgb(248, 248, 248);
}

.tp-hero-8-search span {
    cursor: pointer;
    display: inline-block;
    font-size: 16px;
    font-weight: 400;
    color: #565969;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-hero-8-search span:hover {
    color: var(--tp-theme-primary);
}

.tp-hero-8-search span:hover i {
    color: var(--tp-theme-primary);
}

.tp-hero-8-search span i {
    font-size: 20px;
    margin-right: 5px;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    color: var(--tp-theme-secondary);
}

.tp-hero-8-right {
    overflow: hidden;
}

@media (max-width: 575px) {
    .tp-hero-8-right {
        padding-top: 50px;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-hero-8-content {
        padding-left: 100px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-hero-8-content {
        margin-bottom: 50px;
        padding-left: 100px;
    }
}

@media (max-width: 575px) {
    .tp-hero-8-content {
        margin-bottom: 40px;
    }
}

.tp-hero-8-content p {
    margin-bottom: 70px;
}

@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-hero-8-content p br {
        display: none;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-hero-8-content p {
        margin-bottom: 30px;
    }
}

.tp-hero-8-title {
    font-size: 100px;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 42px;
}

@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-hero-8-title {
        font-size: 70px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-hero-8-title {
        font-size: 60px;
        margin-bottom: 30px;
    }
}

@media (max-width: 575px) {
    .tp-hero-8-title {
        font-size: 52px;
        margin-bottom: 25px;
    }
}

.tp-hero-8-info {
    position: absolute;
    bottom: 0px;
    left: 40px;
    z-index: 1;
    opacity: 0;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

@media (max-width: 575px) {
    .tp-hero-8-info {
        left: 30px;
    }
}

.tp-hero-8-info p {
    margin-bottom: 6px;
    color: var(--tp-common-white);
}

.tp-hero-8-wrapper {
    margin-right: -500px;
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-hero-8-wrapper {
        margin-right: 0;
        padding-left: 100px;
    }
}

@media (max-width: 575px) {
    .tp-hero-8-wrapper {
        margin-right: 0;
    }
}

.tp-hero-8-thumb img {
    width: 100%;
}

.tp-hero-8-item:hover .tp-hero-8-info {
    opacity: 1;
    bottom: 30px;
}

.tp-hero-8-item:hover::after {
    height: 100%;
}

.tp-hero-8-item-title {
    font-size: 24px;
    font-weight: 700;
    color: var(--tp-common-white);
}

.tp-hero-8-item-title:hover {
    color: var(--tp-theme-primary);
}

.tp-hero-8-item::after {
    position: absolute;
    content: "";
    bottom: 0;
    left: 0;
    height: 0%;
    width: 100%;
    background-image: -moz-linear-gradient(90deg, rgb(0, 35, 90) 0%, rgba(0, 35, 90, 0.5) 32%, rgba(0, 35, 90, 0.04) 95%, rgba(0, 35, 90, 0) 100%);
    background-image: -webkit-linear-gradient(90deg, rgb(0, 35, 90) 0%, rgba(0, 35, 90, 0.5) 32%, rgba(0, 35, 90, 0.04) 95%, rgba(0, 35, 90, 0) 100%);
    background-image: -ms-linear-gradient(90deg, rgb(0, 35, 90) 0%, rgba(0, 35, 90, 0.5) 32%, rgba(0, 35, 90, 0.04) 95%, rgba(0, 35, 90, 0) 100%);
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-hero-8-slider {
        padding-bottom: 30px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-hero-8-slider-btn {
        margin-bottom: 30px;
    }
}

.tp-hero-8-slider-btn a {
    font-size: 16px;
    font-weight: 700;
    color: var(--tp-theme-secondary);
    text-decoration: underline;
    margin-right: 160px;
}

.tp-hero-8-slider-btn a:hover {
    color: var(--tp-theme-primary);
}

@media (max-width: 575px) {
    .tp-hero-8-slider-btn a {
        margin-bottom: 20px;
        display: block;
    }
}

.tp-hero-8-slider-btn button {
    font-size: 24px;
    font-weight: 700;
    color: var(--tp-theme-secondary);
}

.tp-hero-8-slider-btn button:hover {
    color: var(--tp-theme-primary);
}

.tp-hero-8-slider-btn button:not(:last-of-type) {
    margin-right: 35px;
}

.tp-hero-9-slider {
    position: absolute;
    bottom: 0;
    right: 0;
    z-index: 2;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-hero-9-slider {
        display: none;
    }
}

.tp-hero-9-wrapper {
    height: 100vh;
}

.tp-hero-9-wrapper.blue {
    background: var(--tp-theme-secondary);
}

.tp-hero-9-box {
    display: flex;
    align-items: center;
    height: 100vh;
    z-index: 1;
}

@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-hero-9-box {
        padding-top: 150px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-hero-9-box {
        height: auto;
    }
}

.tp-hero-9-box::after {
    position: absolute;
    content: "";
    top: 31%;
    left: -60px;
    height: 515px;
    width: 970px;
    z-index: -1;
    background: var(--tp-theme-primary);
}

@media only screen and (min-width: 1600px) and (max-width: 1700px) {
    .tp-hero-9-box::after {
        top: 30%;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-hero-9-box::after {
        top: 23%;
        left: 130px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-hero-9-box::after {
        width: 945px;
        left: 0;
        top: 20%;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-hero-9-box::after {
        content: none;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-hero-9-content {
        margin-bottom: 50px;
    }
}

.tp-hero-9-content-box {
    margin-bottom: 60px;
}

@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
    .tp-hero-9-content-box {
        margin-bottom: 40px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-hero-9-content-box {
        margin-bottom: 30px;
    }
}

@media (max-width: 575px) {
    .tp-hero-9-content-box {
        flex-wrap: wrap;
    }
}

.tp-hero-9-content p {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 95px;
    color: var(--tp-common-white);
}

@media only screen and (min-width: 1600px) and (max-width: 1700px) {
    .tp-hero-9-content p {
        margin-bottom: 60px;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
    .tp-hero-9-content p {
        margin-bottom: 40px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-hero-9-content p {
        font-size: 22px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-hero-9-content p {
        margin-bottom: 30px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-hero-9-thumb img {
        width: 100%;
    }
}

.tp-hero-9-point:not(:last-of-type) {
    margin-right: 60px;
}

@media (max-width: 575px) {
    .tp-hero-9-point:not(:last-of-type) {
        margin-right: 30px;
        margin-bottom: 20px;
    }
}

.tp-hero-9-point span {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 5px;
    color: var(--tp-common-white);
}

.tp-hero-9-point p {
    font-size: 14px;
    font-weight: 400;
    margin: 0;
}

.tp-hero-9-title {
    font-size: 90px;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 80px;
    color: var(--tp-common-white);
}

@media only screen and (min-width: 1600px) and (max-width: 1700px) {
    .tp-hero-9-title {
        margin-bottom: 50px;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
    .tp-hero-9-title {
        margin-bottom: 25px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-hero-9-title {
        font-size: 80px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-hero-9-title {
        font-size: 70px;
        margin-bottom: 30px;
    }
}

@media (max-width: 575px) {
    .tp-hero-9-title {
        font-size: 55px;
        margin-bottom: 30px;
    }
}

.tp-hero-9-title span {
    color: var(--tp-theme-secondary);
}

.tp-hero-9-btn .tp-btn {
    color: var(--tp-theme-secondary);
    background: var(--tp-common-white);
}

.tp-hero-9-active .swiper-slide-active .blue.tp-hero-9-box::after {
    background: var(--tp-theme-secondary);
}

.tp-comming-bg-color::after {
    position: absolute;
    content: "";
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    z-index: 0;
    background-color: rgba(0, 0, 44, 0.8);
}

.tp-comming-hight {
    height: 100vh;
    padding-top: 200px;
    background-repeat: no-repeat;
    background-size: cover;
}

@media only screen and (min-width: 1600px) and (max-width: 1700px), only screen and (min-width: 1200px) and (max-width: 1399px), (max-width: 575px) {
    .tp-comming-hight {
        padding-top: 150px;
    }
}

.tp-comming-title {
    font-size: 90px;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 30px;
    color: var(--tp-common-white);
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .tp-comming-title {
        font-size: 80px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-comming-title {
        font-size: 62px;
    }
}

.tp-comming-content p {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 80px;
    color: var(--tp-common-white);
}

@media only screen and (min-width: 1600px) and (max-width: 1700px), only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-comming-content p {
        margin-bottom: 30px;
    }
}

.tp-comming-countdown {
    margin-bottom: 100px;
}

@media only screen and (min-width: 1600px) and (max-width: 1700px), only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-comming-countdown {
        margin-bottom: 70px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-comming-countdown {
        margin-bottom: 50px;
    }
}

.tp-comming-countdown-inner ul li {
    list-style: none;
    display: inline-block;
    font-size: 16px;
    font-weight: 500;
    line-height: 1;
    width: 150px;
    color: var(--tp-common-white);
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-comming-countdown-inner ul li {
        margin-bottom: 30px;
    }
}

.tp-comming-countdown-inner ul li span {
    display: block;
    font-size: 90px;
    font-weight: 500;
    margin-bottom: 15px;
    color: var(--tp-theme-primary);
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-comming-countdown-inner ul li span {
        font-size: 70px;
    }
}

@media (max-width: 575px) {
    .tp-comming-countdown-inner ul li span {
        font-size: 65px;
    }
}

.tp-comming-countdown-inner ul li:not(:last-of-type) {
    margin-right: 80px;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .tp-comming-countdown-inner ul li:not(:last-of-type) {
        margin-right: 20px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-comming-countdown-inner ul li:not(:last-of-type) {
        margin-right: 30px;
    }
}

@media (max-width: 575px) {
    .tp-comming-countdown-inner ul li:not(:last-of-type) {
        margin-right: 0;
        margin-bottom: 30px;
    }
}

.tp-comming-input {
    margin-bottom: 180px;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-comming-input {
        margin-bottom: 150px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-comming-input {
        margin-bottom: 100px;
    }
}

.tp-comming-input input {
    padding-left: 56px;
    padding-right: 170px;
    font-size: 16px;
    font-weight: 500;
    color: #565969;
    font-family: var(--tp-ff-p);
}

.tp-comming-input input::placeholder {
    font-size: 16px;
    font-weight: 500;
    color: #565969;
}

.tp-comming-input input:focus {
    border: 1px solid var(--tp-theme-primary);
}

.tp-comming-input .icon-1 {
    position: absolute;
    top: 50%;
    left: 35px;
    transform: translateY(-50%);
    font-size: 16px;
    color: #565969;
}

.tp-comming-input button {
    position: absolute;
    top: 50%;
    right: 0px;
    transform: translateY(-50%);
    font-size: 16px;
    font-weight: 500;
    padding: 18px 41px;
    color: var(--tp-common-white);
    background: var(--tp-theme-primary);
}

.tp-swiper-fraction-divide {
    position: relative;
    display: inline-block;
    width: 15px;
    height: 45px;
}

.tp-swiper-fraction-divide::after {
    position: absolute;
    content: "/";
    top: -9px;
    left: 0;
    font-size: 55px;
    color: var(--tp-theme-primary);
}

.tp-swiper-fraction span {
    font-size: 55px;
    font-weight: 400;
    line-height: 1;
    color: var(--tp-common-white);
}

.tp-slider-pagination {
    position: absolute;
    bottom: 65px;
    left: 90px;
    z-index: 9;
    width: auto;
    right: auto;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-slider-pagination {
        bottom: 20px;
    }
}

.tp-slider-pagination .swiper-pagination-total {
    color: var(--tp-theme-primary);
}

/*----------------------------------------*/
/*  7.2 About Css
/*----------------------------------------*/
.tp-about-shape {
    position: absolute;
    top: 65px;
    right: 0;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-about-shape {
        display: none;
    }
}

.tp-about-thumb {
    text-align: end;
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-about-thumb {
        text-align: start;
        margin-bottom: 30px;
    }
}

.tp-about-thumb p {
    font-size: 220px;
    font-weight: 700;
    color: var(--tp-common-white);
    position: absolute;
    top: 30%;
    left: 34%;
    z-index: 2;
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .tp-about-thumb p {
        left: 28%;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-about-thumb p {
        left: 25%;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-about-thumb p {
        display: none;
    }
}

/*.tp-about-thumb::after {*/
/*  position: absolute;*/
/*  content: "";*/
/*  width: 92px;*/
/*  height: 92px;*/
/*  top: 10%;*/
/*  left: 32%;*/
/*  z-index: -2;*/
/*  background: var(--tp-theme-secondary);*/
/*}*/
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-about-thumb::after {
        display: none;
    }
}

.tp-about-thumb::before {
    position: absolute;
    content: "";
    width: 59px;
    height: 68px;
    top: 35%;
    left: 566px;
    z-index: 0;
    background: var(--tp-theme-primary);
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .tp-about-thumb::before {
        top: 17%;
        left: 496px;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-about-thumb::before {
        display: none;
    }
}

.tp-about-thumb-wrapper {
    padding-top: 120px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-about-thumb-wrapper {
        padding-top: 0;
    }
}

.tp-about-thumb-wrapper.home-2 .tp-about-thumb::after {
    content: none;
}

.tp-about-thumb-wrapper.home-2 .tp-about-thumb::before {
    width: 33px;
    height: 86px;
    top: 10%;
    left: 690px;
}

@media only screen and (min-width: 1600px) and (max-width: 1700px) {
    .tp-about-thumb-wrapper.home-2 .tp-about-thumb::before {
        left: 645px;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .tp-about-thumb-wrapper.home-2 .tp-about-thumb::before {
        left: 564px;
    }
}

.tp-about-thumb-wrapper.home-2 .tp-about-thumb.home-2 .grid__item-img.left-top {
    height: 664px;
    width: 470px;
    position: absolute;
    top: 0;
    left: 155px;
}

.tp-about-thumb-wrapper.home-2 .tp-about-thumb.home-2 .grid__item-img.right-top {
    height: 518px;
    width: 420px;
    position: absolute;
    z-index: 1;
}

.tp-about-thumb .main {
    position: relative;
    z-index: 1;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-about-thumb .main {
        max-width: 100%;
    }
}

.tp-about-thumb .top {
    position: absolute;
    top: 74px;
    left: 155px;
    z-index: 1;
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .tp-about-thumb .top {
        left: 85px;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-about-thumb .top {
        left: 30px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-about-thumb .top {
        display: none;
    }
}

.tp-about-thumb .shape-1 {
    position: absolute;
    bottom: 17%;
    left: 17%;
    border-radius: 50%;
    z-index: 2;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-about-thumb .shape-1 {
        left: 2%;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-about-thumb .shape-1 {
        left: 5%;
        bottom: 26%;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-about-thumb .shape-1 {
        top: 30%;
        left: 5%;
    }
}

@media (max-width: 575px) {
    .tp-about-thumb .shape-1 {
        display: none;
    }
}

.tp-about-thumb .shape-2 {
    position: absolute;
    bottom: 165px;
    left: 340px;
    z-index: 2;
    /*animation: circle-animations 15s forwards infinite alternate;*/
}

@media only screen and (min-width: 1701px) and (max-width: 1800px) {
    .tp-about-thumb .shape-2 {
        left: 320px;
    }
}

@media only screen and (min-width: 1600px) and (max-width: 1700px) {
    .tp-about-thumb .shape-2 {
        left: 285px;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .tp-about-thumb .shape-2 {
        left: 265px;
        bottom: 170px;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-about-thumb .shape-2 {
        left: 40px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-about-thumb .shape-2 {
        left: 62px;
        bottom: 28%;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-about-thumb .shape-2 {
        top: 31%;
        left: 7%;
    }
}

@media (max-width: 575px) {
    .tp-about-thumb .shape-2 {
        display: none;
    }
}

.tp-about-wrapper {
    z-index: 2;
    padding-left: 25px;
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-about-wrapper {
        padding-left: 0;
    }
}

.tp-about-wrapper p {
    margin-bottom: 50px;
}

.tp-about-icon {
    margin-right: 25px;
}

.tp-about-icon span {
    font-size: 50px;
    color: var(--tp-theme-primary);
}

.tp-about-icon-content:not(:last-of-type) {
    margin-right: 70px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-about-icon-content:not(:last-of-type) {
        margin-right: 25px;
    }
}

@media (max-width: 575px) {
    .tp-about-icon-content:not(:last-of-type) {
        margin-bottom: 30px;
    }
}

.tp-about-icon-content h5 {
    font-size: 22px;
    font-weight: 700;
}

.tp-about-icon-box {
    padding-bottom: 30px;
    margin-bottom: 50px;
    border-bottom: 1px solid rgb(226, 226, 226);
}

@media (max-width: 575px) {
    .tp-about-icon-box {
        flex-wrap: wrap;
    }
}

.tp-about-list {
    display: inline-block;
    margin-bottom: 25px;
}

.tp-about-list ul li {
    font-size: 18px;
    font-weight: 600;
    color: var(--tp-theme-secondary);
    list-style: none;
    float: left;
    width: 50%;
    margin-bottom: 10px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-about-list ul li {
        width: 100%;
    }
}

.tp-about-list ul li i {
    color: var(--tp-theme-primary);
    margin-right: 10px;
}

.tp-about-btn .tp-btn {
    margin-right: 85px;
}

@media (max-width: 575px) {
    .tp-about-btn .tp-btn {
        margin-bottom: 20px;
    }
}

.tp-about-2-text {
    position: absolute;
    top: 10px;
    left: 60%;
    transform: translateX(-60%);
    z-index: -1;
}

@media (max-width: 575px) {
    .tp-about-2-text {
        left: 28%;
    }
}

.tp-about-2-shape {
    position: absolute;
    top: 65px;
    right: -20px;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-about-2-shape {
        display: none;
    }
}

.tp-about-2-wrapper {
    padding-left: 30px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-about-2-wrapper {
        padding-left: 0;
    }
}

.tp-about-2-wrapper p {
    margin-bottom: 45px;
}

.tp-about-2-list {
    display: inline-block;
    margin-bottom: 50px;
}

.tp-about-2-list ul li {
    list-style: none;
    font-size: 18px;
    font-weight: 600;
    color: var(--tp-theme-secondary);
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-about-2-list ul li {
        font-size: 16px;
    }
}

.tp-about-2-list ul li i {
    font-size: 14px;
    margin-right: 10px;
    color: var(--tp-theme-primary);
}

.tp-about-2-list ul li:not(:last-of-type) {
    margin-bottom: 15px;
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-about-2-btn {
        flex-wrap: wrap;
    }
}

.tp-about-2-btn .tp-btn {
    margin-right: 60px;
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-about-2-btn .tp-btn {
        margin-bottom: 30px;
    }
}

.tp-about-2-user img {
    margin-right: 25px;
}

.tp-about-2-user span {
    position: absolute;
    bottom: 0;
    right: 0;
}

.tp-about-3-shape .shape-1 {
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-about-3-shape .shape-1 {
        display: none;
    }
}

.tp-about-3-shape .shape-2 {
    position: absolute;
    right: 130px;
    bottom: 0;
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .tp-about-3-shape .shape-2 {
        right: 40px;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-about-3-shape .shape-2 {
        right: 0;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-about-3-wrapper {
        padding-top: 30px;
    }
}

.tp-about-3-thumb {
    margin-left: -20px;
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .tp-about-3-thumb {
        margin-left: 40px;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-about-3-thumb {
        margin-left: 30px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-about-3-thumb {
        margin-left: 0;
    }
}

.tp-about-3-thumb .shape-1 {
    position: absolute;
    top: 210px;
    left: -170px;
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .tp-about-3-thumb .shape-1 {
        left: -125px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-about-3-thumb .shape-1 {
        display: none;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-about-3-thumb img {
        width: 100%;
    }
}

.tp-about-3-title-wrapper p {
    margin-bottom: 40px;
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-about-3-btn {
        flex-wrap: wrap;
    }
}

.tp-about-3-btn .tp-btn {
    margin-right: 60px;
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-about-3-btn .tp-btn {
        margin-bottom: 20px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
    .tp-about-3-btn .tp-btn {
        margin-right: 40px;
    }
}

.tp-about-7-shape {
    position: absolute;
    top: 0;
    left: 145px;
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-about-7-shape {
        left: 0;
    }
}

@media (max-width: 575px) {
    .tp-about-7-shape {
        display: none;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-about-7-thumb {
        margin-bottom: 50px;
    }
}

@media (max-width: 575px) {
    .tp-about-7-thumb .main {
        width: 100%;
    }
}

.tp-about-7-thumb .shape-1 {
    position: absolute;
    bottom: -125px;
    left: 0;
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-about-7-thumb .shape-1 {
        display: none;
    }
}

.tp-about-7-ex {
    position: absolute;
    bottom: -105px;
    right: 40px;
    padding: 12px 45px 12px 100px;
    background-repeat: no-repeat;
    background-position: left;
    background-color: var(--tp-common-white);
    box-shadow: 0px 8px 40px 0px rgba(18, 14, 14, 0.06);
    /*animation: updown-two 2.6s linear 0s infinite alternate;*/
    z-index: 1;
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-about-7-ex {
        bottom: 50px;
        right: 0;
    }
}

.tp-about-7-ex-title {
    font-size: 48px;
    font-weight: 700;
    margin: 0;
}

.tp-about-7-ex span {
    font-size: 16px;
    color: #565969;
}

.tp-about-7-wrapper {
    padding-left: 70px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-about-7-wrapper {
        padding-left: 0;
    }
}

.tp-about-7-wrapper p {
    margin-bottom: 50px;
}

.tp-hover-distort-wrapper {
    position: relative;
}

.tp-hover-distort-wrapper .canvas {
    max-width: 400px;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.tp-hover-distort-wrapper .canvas canvas {
    max-width: 400px;
}

.tp-hover-distort-img {
    max-width: 400px;
    opacity: 0;
    object-fit: cover;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .tp-hover-distort-img {
        width: 100%;
    }
}

.tp-hover-distort-img.back {
    position: absolute;
    content: "";
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

/*----------------------------------------*/
/*  8.3 Offer Css
/*----------------------------------------*/
.tp-offer-slider-btn {
    z-index: 100;
}

.tp-offer-shape {
    position: absolute;
    left: 0;
    bottom: 90px;
    z-index: -1;
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-offer-shape {
        display: none;
    }
}

.tp-offer-item {
    position: relative;
    padding: 20px;
    background-color: rgb(255, 255, 255);
    box-shadow: 0px 0px 80px 0px rgba(0, 8, 21, 0.04);
}

.tp-offer-item:hover {
    background: var(--tp-theme-secondary);
}

.tp-offer-item:hover .tp-offer-title {
    color: var(--tp-common-white);
}

.tp-offer-item:hover .tp-offer-btn {
    color: var(--tp-common-white);
}

.tp-offer-item:hover .tp-offer-icon span {
    color: var(--tp-common-white);
}

.tp-offer-item:hover .tp-offer-item-thumb img {
    transform: scale(1.1);
}

.tp-offer-item-thumb {
    position: relative;
    overflow: hidden;
}

.tp-offer-item-thumb img {
    -webkit-transition: 1s;
    -moz-transition: 1s;
    -ms-transition: 1s;
    -o-transition: 1s;
    transition: 1s;
    width: 100%;
}

.tp-offer-content {
    padding: 30px 20px 20px;
}

.tp-offer-icon {
    margin-bottom: 20px;
}

.tp-offer-icon span {
    font-size: 55px;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    color: var(--tp-theme-primary);
}

.tp-offer-title {
    transition: none;
}

.tp-offer-btn {
    color: var(--tp-theme-secondary);
}

.tp-offer-btn svg {
    height: 16px;
}

.tp-offer-arrow-box {
    position: absolute;
    top: 155px;
    right: 0;
}

.tp-offer-arrow-box button {
    display: inline-block;
    height: 60px;
    width: 60px;
    font-size: 20px;
    line-height: 60px;
    border-radius: 50%;
    background-color: rgb(255, 255, 255);
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-offer-arrow-box button:not(:last-of-type) {
    margin-right: 20px;
}

.tp-offer-arrow-box button {
    color: var(--tp-common-white);
    background-color: var(--tp-theme-primary);
}

.tp-offer-arrow-box button:hover {
    color: var(--tp-common-white);
    background-color: rgb(0, 25, 64);
}

.tp-offer-arrow-box .tp-offer-prev {
    left: 0;
    transform: translateX(-125%);
}

.tp-offer-arrow-box .tp-offer-next {
    right: 0;
    transform: translateX(125%);
}

.tp-portfolio-slider .tp-offer-arrow-box .tp-offer-prev {
    left: 0;
    transform: translateX(30px);
}

.tp-portfolio-slider .tp-offer-arrow-box .tp-offer-next {
    right: 0;
    transform: translateX(-30px);
}
.tp-portfolio-slider .tp-offer-arrow-box {
    position: absolute;
    top: 250px;
    right: 0;
}

.tp-testimonial-slider .tp-offer-arrow-box .tp-offer-prev {
    left: 0;
    transform: translateX(-80px);
}

.tp-testimonial-slider .tp-offer-arrow-box .tp-offer-next {
    right: 0;
    transform: translateX(80px);
}
.tp-testimonial-slider .tp-offer-arrow-box {
    position: absolute;
    top: 100px;
    right: 0;
}
.tp-offer-slider-btn-mobile .tp-offer-arrow-box .tp-offer-prev {
    left: 28%;
    transform: translateX(0);
}

.tp-offer-slider-btn-mobile .tp-offer-arrow-box .tp-offer-next {
    right: 28%;
    transform: translateX(-0);
}
.tp-offer-slider-btn-mobile .tp-offer-arrow-box {
    position: absolute;
    top: -70px;
    right: 0;
}

.tp-offer-2-bg {
    background-color: rgb(248, 248, 248);
}

.tp-offer-2-text {
    position: absolute;
    top: 22%;
    left: 55px;
}

.tp-offer-2-shape {
    position: absolute;
    bottom: -50px;
    right: -50px;
}

.tp-offer-2-item {
    padding: 40px;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    background-color: rgb(255, 255, 255);
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-offer-2-item {
        padding: 30px;
    }
}

.tp-offer-2-item:hover .tp-offer-2-item-bg {
    height: 100%;
    top: auto;
    bottom: 0;
}

.tp-offer-2-item:hover .tp-offer-2-title {
    color: var(--tp-common-white);
}

.tp-offer-2-item:hover .tp-offer-2-btn span {
    color: var(--tp-common-white);
    background: var(--tp-theme-primary);
}

.tp-offer-2-item:hover p {
    color: var(--tp-common-white);
}

.tp-offer-2-item-bg {
    position: absolute;
    width: 100%;
    height: 0;
    bottom: auto;
    left: 0;
    top: 0;
    z-index: -1;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    background-repeat: no-repeat;
}

.tp-offer-2-item-bg::before {
    position: absolute;
    content: "";
    height: 100%;
    width: 100%;
    top: 0;
    left: 0;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    background-color: rgba(0, 35, 90, 0.9);
}

.tp-offer-2-item-bg img {
    width: 100%;
    height: 100%;
}

.tp-offer-2-item p {
    margin-bottom: 30px;
}

.tp-offer-2-icon span {
    display: block;
    margin-bottom: 25px;
    font-size: 42px;
    color: var(--tp-theme-primary);
}

.tp-offer-2-title {
    font-size: 22px;
    font-weight: 700;
    margin-bottom: 20px;
    color: var(--tp-theme-secondary);
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-offer-2-btn span {
    display: inline-block;
    width: 48px;
    height: 48px;
    line-height: 46px;
    text-align: center;
    font-size: 20px;
    color: var(--tp-theme-primary);
    background: var(--tp-common-white);
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-error-area {
        padding-top: 90px;
        padding-bottom: 100px;
    }
}

.tp-error-shape {
    position: absolute;
    top: 58px;
    right: -390px;
    height: 727px;
    width: 727px;
    border-radius: 50%;
    display: inline-block;
    background: var(--tp-theme-primary);
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-error-shape {
        right: -560px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-error-shape {
        display: none;
    }
}

.tp-error-text-transparent {
    position: absolute;
    top: 165px;
    left: 190px;
}

.tp-error-text-transparent h2 {
    font-size: 160px;
    font-weight: 700;
    color: #f1f1f1;
}

@media only screen and (min-width: 1600px) and (max-width: 1700px), only screen and (min-width: 1400px) and (max-width: 1599px) {
    .tp-error-text-transparent {
        left: 100px;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-error-text-transparent {
        left: 50px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-error-text-transparent {
        left: 30px;
    }

    .tp-error-text-transparent h2 {
        font-size: 100px;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-error-content {
        padding-left: 30px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-error-content {
        padding-left: 0px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-error-thumb img {
        width: 100%;
    }
}

.tp-error-title {
    font-size: 55px;
    font-weight: 700;
    margin-bottom: 28px;
    letter-spacing: -2px;
    color: var(--tp-theme-secondary);
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-error-wrap {
        padding-top: 0;
        padding-bottom: 30px;
    }
}

.tp-error-wrap p {
    margin-bottom: 35px;
}

/*----------------------------------------*/
/*  8.4 Portfolio Css
/*----------------------------------------*/
.tp-portfolio-thumb {
    position: relative;
}

.tp-portfolio-thumb img {
    width: auto;
    height: 583px;
}

.tp-portfolio-thumb::after, .tp-portfolio-thumb::before {
    position: absolute;
    content: "";
    bottom: 0;
    left: 0;
    height: 50%;
    width: 100%;
    background-image: -moz-linear-gradient(90deg, rgb(0, 35, 90) 0%, rgba(0, 35, 90, 0.5) 32%, rgba(0, 35, 90, 0.04) 95%, rgba(0, 35, 90, 0) 100%);
    background-image: -webkit-linear-gradient(90deg, rgb(0, 35, 90) 0%, rgba(0, 35, 90, 0.5) 32%, rgba(0, 35, 90, 0.04) 95%, rgba(0, 35, 90, 0) 100%);
    background-image: -ms-linear-gradient(90deg, rgb(0, 35, 90) 0%, rgba(0, 35, 90, 0.5) 32%, rgba(0, 35, 90, 0.04) 95%, rgba(0, 35, 90, 0) 100%);
    z-index: 0;
}

.tp-portfolio-thumb::before {
    height: 0;
    bottom: 0;
    opacity: 0.8;
    background-color: rgb(0, 35, 90);
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-portfolio-thumb:hover::before {
    height: 100%;
}

.tp-portfolio-thumb:hover .tp-portfolio-thumb-info {
    bottom: 115px;
}

.tp-portfolio-thumb:hover .tp-portfolio-btn {
    bottom: 40px;
    opacity: 1;
    visibility: visible;
}

.tp-portfolio-thumb-info {
    position: absolute;
    bottom: 30px;
    left: 40px;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    z-index: 1;
}

.tp-portfolio-thumb-info p {
    font-weight: 600;
    font-family: var(--tp-ff-heading);
    color: var(--tp-common-white);
    margin-bottom: 8px;
}

.tp-portfolio-btn {
    position: absolute;
    bottom: 0;
    left: 40px;
    opacity: 0;
    visibility: hidden;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    z-index: 1;
}

.tp-portfolio-btn a {
    display: inline-block;
    width: 60px;
    height: 60px;
    line-height: 60px;
    border-radius: 50%;
    font-size: 18px;
    text-align: center;
    color: var(--tp-common-white);
    background: var(--tp-theme-primary);
}

.tp-portfolio-btn a:hover {
    color: var(--tp-theme-secondary);
    background: var(--tp-common-white);
}

.tp-portfolio-title {
    font-size: 28px;
    font-weight: 700;
    color: var(--tp-common-white);
}

.tp-portfolio-title:hover {
    color: var(--tp-theme-primary);
}

.tp-portfolio-3-color::after {
    position: absolute;
    content: "";
    height: 240px;
    width: 100%;
    bottom: 0;
    left: 0;
    background: #f8f8f8;
    z-index: -1;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-portfolio-3-thumb img {
        width: 100%;
    }
}

.tp-portfolio-3-thumb::after {
    position: absolute;
    content: "";
    left: 0px;
    top: 0px;
    width: 100%;
    height: 100%;
    transition: all 500ms ease;
    -webkit-transform: translateY(-100%);
    -ms-transform: translateY(-100%);
    -o-transform: translateY(-100%);
    -moz-transform: translateY(-100%);
    transform: translateY(-100%);
    opacity: 0;
    background-color: var(--tp-theme-secondary);
}

.tp-portfolio-3-thumb:hover .tp-portfolio-3-info {
    opacity: 1;
    transform: translateY(0);
}

.tp-portfolio-3-thumb:hover .tp-portfolio-3-btn {
    top: 40px;
    opacity: 1;
}

.tp-portfolio-3-thumb:hover::after {
    -webkit-transform: translateY(0%);
    -ms-transform: translateY(0%);
    -o-transform: translateY(0%);
    -moz-transform: translateY(0%);
    transform: translateY(0%);
    opacity: 0.9;
}

.tp-portfolio-3-info {
    position: absolute;
    left: 40px;
    bottom: 30px;
    transition: 0.5s;
    transform: translateY(10px);
    opacity: 0;
    z-index: 1;
}

.tp-portfolio-3-info p {
    font-size: 16px;
    font-weight: 600;
    color: var(--tp-common-white);
}

.tp-portfolio-3-title {
    font-size: 28px;
    font-weight: 700;
    margin: 0;
    color: var(--tp-common-white);
}

.tp-portfolio-3-title:hover {
    color: var(--tp-theme-primary);
}

.tp-portfolio-3-btn {
    position: absolute;
    top: 60px;
    right: 40px;
    opacity: 0;
    transition: all 900ms ease 500ms;
    -webkit-transition: all 900ms ease 500ms;
    -ms-transition: all 900ms ease 500ms;
    -o-transition: all 900ms ease 500ms;
    transition-delay: 0s;
    z-index: 1;
}

.tp-portfolio-3-btn a svg {
    height: 20px;
    color: var(--tp-common-white);
}

.tp-portfolio-3-slider {
    margin-right: -350px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-portfolio-3-slider {
        margin-right: 0;
    }
}

.tp-portfolio-6-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 62%;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-portfolio-6-bg {
        height: 57%;
    }
}

.tp-portfolio-6-title-wrapper {
    position: relative;
    margin-bottom: 60px;
}

.tp-portfolio-6-title-wrapper .tp-section-title {
    color: var(--tp-common-white);
}

.tp-portfolio-6-title-wrapper .tp-section-title-pre-2 {
    color: var(--tp-common-white);
}

.tp-portfolio-6-item {
    position: relative;
    overflow: hidden;
    background-color: rgb(255, 255, 255);
    box-shadow: 0px 4px 60px 0px rgba(18, 14, 14, 0.08);
}

.tp-portfolio-6-item:hover {
    background-color: none;
}

.tp-portfolio-6-item:hover .tp-portfolio-6-thumb img {
    transform: scale(1.1);
}

.tp-portfolio-6-item:hover .tp-portfolio-6-shape {
    opacity: 1;
    right: 0;
    bottom: 0;
}

.tp-portfolio-6-item:hover .tp-portfolio-6-content::after {
    width: 100%;
    left: 0;
    right: auto;
}

.tp-portfolio-6-item:hover .tp-portfolio-6-icon span::before {
    opacity: 1;
    visibility: visible;
}

.tp-portfolio-6-thumb {
    overflow: hidden;
}

.tp-portfolio-6-thumb img {
    transition: 1s;
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-portfolio-6-thumb img {
        min-width: 100%;
    }
}

.tp-portfolio-6-content {
    position: relative;
    padding: 68px 40px 35px 40px;
}

.tp-portfolio-6-content p {
    color: #727272;
    margin-bottom: 16px;
}

.tp-portfolio-6-content::after {
    position: absolute;
    content: "";
    width: 0%;
    height: 6px;
    top: 0px;
    left: auto;
    right: 0;
    background-color: var(--tp-theme-4);
    transition: all 0.3s ease-out 0s;
}

.tp-portfolio-6-icon {
    position: absolute;
    top: -50px;
    left: 40px;
    z-index: 1;
}

.tp-portfolio-6-icon span {
    display: inline-block;
    height: 100px;
    width: 100px;
    line-height: 100px;
    font-size: 47px;
    text-align: center;
    border-radius: 50%;
    color: var(--tp-theme-4);
    background: var(--tp-common-white);
    box-shadow: 0px 4px 20px 0px rgba(18, 14, 14, 0.06);
}

.tp-portfolio-6-icon span::before {
    position: absolute;
    content: "";
    border: 4px solid var(--tp-theme-4);
    height: 100%;
    width: 100%;
    border-radius: 50%;
    top: 0px;
    left: 0;
    opacity: 0;
    visibility: hidden;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-portfolio-6-title {
    font-size: 26px;
    font-weight: 700;
    margin-bottom: 15px;
    color: var(--tp-heading-2);
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-portfolio-6-title {
        font-size: 24px;
    }
}

.tp-portfolio-6-title a:hover {
    color: var(--tp-theme-4);
}

.tp-portfolio-6-btn a {
    font-size: 16px;
    font-weight: 700;
    color: var(--tp-heading-2);
}

.tp-portfolio-6-btn a svg {
    height: 13px;
    margin-left: 5px;
}

.tp-portfolio-6-btn a:hover {
    color: var(--tp-theme-4);
}

.tp-portfolio-6-shape {
    position: absolute;
    bottom: -30px;
    right: -30px;
    opacity: 0;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-portfolio-7-shape .shape-1 {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 0;
    width: 100%;
    opacity: 0.7;
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-portfolio-7-shape .shape-1 {
        display: none;
    }
}

.tp-portfolio-7-shape .shape-2 {
    position: absolute;
    top: 0;
    right: 0;
    filter: saturate(0);
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-portfolio-7-shape .shape-2 {
        display: none;
    }
}

.tp-portfolio-7-plr {
    padding-left: 175px;
    padding-right: 175px;
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .tp-portfolio-7-plr {
        padding-left: 90px;
        padding-right: 90px;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-portfolio-7-plr {
        padding-right: 30px;
        padding-left: 30px;
    }
}

.tp-portfolio-7-item:hover .tp-portfolio-7-content {
    bottom: 40px;
    opacity: 1;
}

.tp-portfolio-7-item:hover .tp-portfolio-7-thumb::before {
    opacity: 0.851;
}

.tp-portfolio-7-content {
    position: absolute;
    bottom: 20px;
    left: 0;
    opacity: 0;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-portfolio-7-box {
    padding: 20px 40px;
    background: var(--tp-common-white);
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-portfolio-7-box {
        padding: 20px 20px;
    }
}

.tp-portfolio-7-box span {
    font-size: 18px;
    font-weight: 400;
    color: #727272;
    margin-bottom: 10px;
    display: block;
}

.tp-portfolio-7-thumb img {
    width: 100%;
}

.tp-portfolio-7-thumb::before {
    position: absolute;
    content: "";
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    background-color: rgb(0, 35, 90);
    opacity: 0;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-portfolio-7-title {
    font-size: 22px;
    font-weight: 600;
    margin-bottom: 0;
    color: var(--tp-heading-2);
}

.tp-portfolio-7-title:hover {
    color: var(--tp-theme-primary);
}

.tp-portfolio-7-btn {
    position: absolute;
    top: -24px;
    right: 20px;
}

.tp-portfolio-7-btn:hover a i {
    /*animation: tfLeftToRight 0.5s forwards;*/
}

.tp-portfolio-7-btn a {
    font-size: 20px;
    color: var(--tp-common-white);
    display: inline-block;
    text-align: center;
    border-radius: 50%;
    height: 48px;
    width: 48px;
    line-height: 45px;
    background: var(--tp-theme-primary);
}

.tp-portfolio-7-btn a i {
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    transform: translateY(0px);
}

.tp-portfolio-details-wrapper .text-1 {
    margin-bottom: 40px;
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-portfolio-details-wrapper .text-1 br {
        display: none;
    }
}

.tp-portfolio-details-wrapper .text-2 {
    margin-bottom: 40px;
}

.tp-portfolio-details-wrapper .text-3 {
    margin-bottom: 40px;
}

.tp-portfolio-details-title {
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 20px;
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-portfolio-details-title {
        font-size: 36px;
    }
}

.tp-portfolio-details-title-2 {
    font-size: 45px;
    font-weight: 700;
    margin-bottom: 20px;
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-portfolio-details-title-2 {
        font-size: 36px;
    }
}

.tp-portfolio-details-wrap {
    margin-bottom: 35px;
}

.tp-portfolio-details-wrap ul {
    display: flex;
    flex-wrap: wrap;
}

.tp-portfolio-details-wrap ul li {
    font-size: 16px;
    color: #565969;
    list-style: none;
}

.tp-portfolio-details-wrap ul li:not(:last-of-type) {
    margin-right: 80px;
}

.tp-portfolio-details-wrap ul li span {
    display: block;
    font-size: 18px;
    font-weight: 700;
    color: var(--tp-theme-secondary);
}

.tp-portfolio-details-wrap-box {
    margin-bottom: 100px;
}

.tp-portfolio-details-box-3 {
    padding-bottom: 80px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-portfolio-details-box-3 {
        padding-bottom: 40px;
    }
}

.tp-portfolio-details-list ul li {
    position: relative;
    list-style: none;
    margin-left: 30px;
    margin-bottom: 30px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-portfolio-details-list ul li {
        margin-left: 60px;
    }
}

@media (max-width: 575px) {
    .tp-portfolio-details-list ul li {
        margin-left: 55px;
    }
}

.tp-portfolio-details-list ul li span {
    position: absolute;
    top: 0;
    left: -60px;
    display: inline-block;
    flex: 0 0 auto;
    font-size: 20px;
    height: 48px;
    width: 48px;
    line-height: 48px;
    text-align: center;
    border-radius: 50%;
    color: var(--tp-theme-primary);
    background: var(--tp-text-2);
}

.tp-portfolio-details-btn a {
    font-size: 16px;
    font-weight: 400;
    color: #565969;
    padding: 6px 22px;
    display: inline-block;
    background: var(--tp-text-2);
}

.tp-portfolio-details-btn a:not(:last-of-type) {
    margin-right: 20px;
}

@media (max-width: 575px) {
    .tp-portfolio-details-btn a:not(:last-of-type) {
        margin-bottom: 20px;
    }
}

.tp-portfolio-details-btn a:hover {
    color: var(--tp-common-white);
    background: var(--tp-theme-secondary);
}

.tp-portfolio-details-share-area {
    padding-bottom: 30px;
    border-bottom: 1px solid rgba(225, 225, 225, 0.6);
    margin-bottom: 40px;
}

.tp-portfolio-details-share span {
    display: inline-block;
    height: 30px;
    width: 30px;
    border-radius: 50%;
    line-height: 30px;
    text-align: center;
    font-size: 14px;
    color: var(--tp-common-white);
    background: var(--tp-theme-primary);
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-portfolio-details-share span:hover {
    background: var(--tp-theme-secondary);
}

.tp-portfolio-details-prev a:hover {
    color: var(--tp-theme-primary);
}

.tp-portfolio-details-thumb {
    margin-bottom: 45px;
}

.tp-portfolio-details-thumb img {
    width: 100%;
}

.tp-portfolio-details-thumb-2 {
    margin-bottom: 30px;
}

@media (max-width: 575px) {
    .tp-portfolio-details-thumb-2 img {
        width: 100%;
    }
}

/*----------------------------------------*/
/*  8.1 Feature Css
/*----------------------------------------*/
.tp-feature-text-title {
    top: 188px;
    left: 100px;
    position: absolute;
    transform: rotate(-90deg) translateY(-102px);
    font-size: 100px;
    text-transform: uppercase;
    color: rgba(0, 35, 90, 0.039);
    margin: 0;
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-feature-text-title {
        left: -30px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-feature-text-title {
        left: -90px;
    }
}

.tp-feature-text-title.onepage {
    top: 305px;
    left: 100px;
    position: absolute;
    transform: rotate(-90deg) translateY(-102px);
    font-size: 100px;
    text-transform: uppercase;
    color: rgba(0, 35, 90, 0.039);
    margin: 0;
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-feature-text-title.onepage {
        left: -30px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-feature-text-title.onepage {
        left: -90px;
    }
}

.tp-feature-shape-1 {
    position: absolute;
    top: -68px;
    right: 16%;
}

@media only screen and (min-width: 1600px) and (max-width: 1700px) {
    .tp-feature-shape-1 {
        right: 9%;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .tp-feature-shape-1 {
        right: 4%;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
    .tp-feature-shape-1 {
        right: 2%;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-feature-shape-1 {
        right: 10px;
    }
}

@media (max-width: 575px) {
    .tp-feature-shape-1 {
        display: none;
    }
}

.tp-feature-tab-title {
    font-size: 26px;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    color: var(--tp-theme-secondary);
}

.tp-feature-tab-btn {
    padding-right: 25px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-feature-tab-btn {
        padding-right: 0;
    }
}

.tp-feature-tab-btn .nav-pills .nav-item {
    position: relative;
    width: 346px;
    overflow: hidden;
}

.tp-feature-tab-btn .nav-pills .nav-item:not(:last-of-type) {
    margin-bottom: 12px;
}

.tp-feature-tab-btn .nav-pills .nav-item .nav-link {
    border-radius: 0;
    padding: 16px 30px;
    border: 1px solid rgb(228, 228, 228);
    background-color: white;
}

.tp-feature-tab-btn .nav-pills .nav-item .nav-link::after {
    position: absolute;
    content: "";
    height: 100%;
    width: 0px;
    top: 0;
    left: 0;
    background: var(--tp-theme-primary);
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-feature-tab-btn .nav-pills .nav-item .nav-link::before {
    position: absolute;
    content: "";
    top: 50%;
    height: 15px;
    width: 15px;
    left: -5px;
    opacity: 0;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    transform: translateY(-50%);
    background: var(--tp-theme-primary);
    clip-path: polygon(-20% 0%, 0% 100%, 106.67% 50%);
}

.tp-feature-tab-btn .nav-pills .nav-item .nav-link p {
    font-size: 14px;
    font-weight: 500;
    color: #565969;
    margin-bottom: 5px;
}

.tp-feature-tab-btn .nav-pills .nav-item .nav-link.active {
    background-color: white;
}

.tp-feature-tab-btn .nav-pills .nav-item .nav-link.active .tp-feature-tab-title {
    color: var(--tp-theme-primary);
}

.tp-feature-tab-btn .nav-pills .nav-item .nav-link.active::after {
    width: 4px;
}

.tp-feature-tab-btn .nav-pills .nav-item .nav-link.active::before {
    opacity: 1;
    left: 0;
}

.tp-feature-tab-btn .nav-pills .nav-item .nav-link:hover {
    cursor: pointer;
}

.tp-feature-tab-thumb {
    position: relative;
}

.tp-feature-tab-thumb img {
    max-width: 100%;
}

.tp-feature-tab-box {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 220px;
    padding: 40px 40px 30px 40px;
    background: var(--tp-common-white);
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-feature-tab-box {
        right: 210px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-feature-tab-box {
        right: 0;
        bottom: -115px;
        padding: 20px 30px;
    }
}

.tp-feature-tab-box a {
    position: relative;
    display: inline-block;
    font-size: 16px;
    font-weight: 700;
}

.tp-feature-tab-box a span {
    position: relative;
    display: inline-block;
    padding: 0;
    vertical-align: middle;
}

.tp-feature-tab-title {
    font-size: 26px;
    font-weight: 700;
    margin-bottom: 20px;
    color: var(--tp-theme-secondary);
}

@media (max-width: 575px) {
    .tp-feature-tab-title {
        font-size: 18px;
    }
}

.tp-feature-6-card {
    position: relative;
    -webkit-backface-visibility: hidden;
}

.tp-feature-6-box {
    -ms-transform-style: preserve-3d;
    transform-style: preserve-3d;
    -webkit-transform-style: preserve-3d;
    perspective: 1000px;
    -webkit-perspective: 1000px;
}

.tp-feature-6-box:hover .tp-feature-6-item {
    -ms-transform: rotateY(-180deg);
    -webkit-transform: rotateY(-180deg);
    transform: rotateY(-180deg);
    -webkit-transform-style: preserve-3d;
    -ms-transform-style: preserve-3d;
    transform-style: preserve-3d;
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-feature-6-box:hover .tp-feature-6-item {
        transform: rotateY(0deg);
    }
}

.tp-feature-6-box:hover .tp-feature-6-back {
    -ms-transform: rotateY(0deg);
    -webkit-transform: rotateY(0deg);
    transform: rotateY(0deg);
    -webkit-transform-style: preserve-3d;
    -ms-transform-style: preserve-3d;
    transform-style: preserve-3d;
}

.tp-feature-6-back, .tp-feature-6-item {
    background-size: cover;
    background-position: center;
    -ms-transition: transform 0.7s cubic-bezier(0.4, 0.2, 0.2, 1);
    transition: transform 0.7s cubic-bezier(0.4, 0.2, 0.2, 1);
    -webkit-transition: transform 0.7s cubic-bezier(0.4, 0.2, 0.2, 1);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
}

.tp-feature-6-item {
    -ms-transform: rotateY(0deg);
    -webkit-transform: rotateY(0deg);
    transform: rotateY(0deg);
    -webkit-transform-style: preserve-3d;
    -ms-transform-style: preserve-3d;
    transform-style: preserve-3d;
    background-color: rgb(255, 255, 255);
    box-shadow: 0px 0px 80px 0px rgba(30, 22, 22, 0.08);
    border-top: 2px solid var(--tp-theme-4);
    padding: 45px 40px 40px 40px;
}

.tp-feature-6-item p {
    font-size: 16px;
    font-weight: 400;
    color: #767676;
    margin-bottom: 25px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-feature-6-item p {
        margin-bottom: 15px;
    }
}

.tp-feature-6-back {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    -ms-transform: rotateY(180deg);
    -webkit-transform: rotateY(180deg);
    transform: rotateY(180deg);
    -webkit-transform-style: preserve-3d;
    -ms-transform-style: preserve-3d;
    transform-style: preserve-3d;
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-feature-6-back {
        display: none;
    }
}

.tp-feature-6-icon {
    position: absolute;
    top: 0;
    left: 40px;
}

.tp-feature-6-icon i {
    font-size: 60px;
    height: 90px;
    width: 90px;
    display: inline-block;
    text-align: center;
    line-height: 90px;
    color: var(--tp-common-white);
    background: var(--tp-theme-4);
}

.tp-feature-6-title {
    font-size: 22px;
    font-weight: 700;
    margin-bottom: 25px;
    padding-left: 125px;
    color: var(--tp-heading-2);
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-feature-6-title {
        font-size: 18px;
        margin-bottom: 15px;
        padding-left: 105px;
    }
}

.tp-feature-6-btn a {
    font-weight: 700;
    color: var(--tp-heading-2);
}

.tp-feature-6-btn a:hover {
    color: var(--tp-theme-4);
}

.tp-feature-6-shape {
    position: absolute;
    bottom: -5px;
    right: -5px;
    z-index: 1;
}

.tp-feature-6-shape::after {
    position: absolute;
    content: "";
    bottom: 5px;
    right: 5px;
    height: 85px;
    width: 88px;
    background-color: rgb(0, 0, 0);
    opacity: 0.6;
    z-index: -1;
}

.tp-feature-6-thumb span {
    position: absolute;
    bottom: 15px;
    right: 20px;
    font-size: 22px;
    z-index: 1;
    color: var(--tp-common-white);
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-feature-6-thumb span:hover {
    color: var(--tp-theme-4);
}

.tp-feature-6-thumb img {
    width: 100%;
}

.tp-success-title-wrap {
    margin-bottom: 80px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-success-item {
        padding-bottom: 30px;
    }
}

.tp-success-item.line::after {
    position: absolute;
    content: "";
    top: 20px;
    right: -72px;
    height: 1px;
    width: 120px;
    background-color: rgb(215, 215, 215);
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-success-item.line::after {
        content: none;
    }
}

.tp-success-item:hover .tp-success-point span {
    background: var(--tp-theme-primary);
}

.tp-success-item:hover .tp-success-point span::before {
    bottom: -10px;
}

.tp-success-point {
    margin-bottom: 30px;
}

.tp-success-point span {
    position: relative;
    height: 50px;
    width: 50px;
    line-height: 50px;
    border-radius: 50%;
    display: inline-block;
    text-align: center;
    font-size: 20px;
    font-weight: 700;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    color: var(--tp-common-white);
    background: var(--tp-theme-secondary);
}

.tp-success-point span::before {
    position: absolute;
    content: "";
    bottom: -10px;
    left: -10px;
    height: 70px;
    width: 70px;
    line-height: 70px;
    display: inline-block;
    border-radius: 50%;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    border: 1px solid rgb(215, 215, 215);
}

.tp-success-content p {
    font-family: var(--tp-ff-heading);
    font-size: 18px;
    font-weight: 600;
    color: var(--tp-theme-secondary);
}

/*----------------------------------------*/
/*  7.4 Counter Css
/*----------------------------------------*/
.tp-counter-shape .shape-1 {
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 0;
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-counter-shape .shape-1 {
        max-width: 100%;
    }
}

.tp-counter-shape .shape-2 {
    position: absolute;
    top: 0;
    right: 100px;
    z-index: 0;
}

@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-counter-shape .shape-2 {
        right: 15px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-counter-shape .shape-2 {
        display: none;
    }
}

.tp-counter-title-wrapper .tp-section-title {
    letter-spacing: -0.8px;
}

@media (max-width: 575px) {
    .tp-counter-call {
        margin-bottom: 20px;
    }
}

.tp-counter-call-icon span {
    height: 60px;
    width: 60px;
    line-height: 60px;
    display: inline-block;
    text-align: center;
    border-radius: 50%;
    font-size: 20px;
    color: var(--tp-common-white);
    background: var(--tp-theme-secondary);
    margin-right: 15px;
}

.tp-counter-call-icon.call span {
    color: var(--tp-theme-primary);
    background: var(--tp-common-white);
}

.tp-counter-call-info p {
    font-family: var(--tp-ff-heading);
    color: #565969;
}

.tp-counter-call-info span {
    font-size: 18px;
    font-weight: 700;
    color: var(--tp-theme-secondary);
}

.tp-counter-call-info span:hover {
    color: var(--tp-theme-primary);
}

.tp-counter-call.call {
    padding: 30px;
    background: var(--tp-text-2);
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-counter-call.call {
        margin-bottom: 50px;
    }
}

.tp-counter-wrapper {
    margin-left: 50px;
    margin-right: -40px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-counter-wrapper {
        margin-left: 0;
        margin-right: 0;
    }
}

.tp-counter-btn-wrapper {
    position: relative;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-counter-btn-wrapper {
        flex-wrap: wrap;
    }
}

.tp-counter-btn-wrapper .tp-btn {
    margin-right: 40px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-counter-btn-wrapper .tp-btn {
        margin-bottom: 20px;
    }
}

.tp-counter-item {
    position: relative;
    background-color: rgb(252, 252, 252);
    padding: 40px;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-counter-item.active {
    background-color: rgb(255, 255, 255);
    box-shadow: 0px 0px 60px 0px rgba(0, 8, 21, 0.1);
}

.tp-counter-item-icon {
    margin-bottom: 15px;
}

.tp-counter-item-icon span {
    font-size: 50px;
    color: var(--tp-theme-primary);
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-counter-item-content p {
        font-size: 15px;
    }
}

.tp-counter-title {
    font-size: 48px;
    font-weight: 700;
    color: var(--tp-theme-secondary);
}

.tp-counter-2-bg {
    background: var(--tp-theme-secondary);
}

.tp-counter-2-shape {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 0;
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-counter-2-wrapper {
        margin-bottom: 30px;
    }
}

.tp-counter-2-title {
    font-size: 55px;
    font-weight: 700;
    margin: 0;
    letter-spacing: 0.02px;
    color: var(--tp-common-white);
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-counter-2-title {
        font-size: 50px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-counter-2-title {
        font-size: 45px;
    }
}

@media (max-width: 575px) {
    .tp-counter-2-title {
        font-size: 35px;
    }
}

.tp-counter-2-item.color span {
    color: var(--tp-theme-secondary);
}

.tp-counter-2-item-title {
    font-size: 135px;
    font-weight: 700;
    color: #103774;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-counter-2-item-title {
        font-size: 100px;
    }
}

.tp-counter-2-item-title .purecounter {
    font-style: normal;
}

.tp-counter-2-item-title.color {
    color: #f7faff;
}

.tp-counter-2-item span {
    position: absolute;
    top: 50%;
    left: 0;
    font-size: 18px;
    font-weight: 700;
    display: block;
    right: 0;
    text-align: center;
    transform: translateY(-50%);
    color: var(--tp-common-white);
}

.tp-counter-2-item.two {
    margin-left: 50px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-counter-2-item.two {
        margin-left: 20px;
    }
}

@media (max-width: 575px) {
    .tp-counter-2-item.two {
        margin-left: 0;
    }
}

.tp-counter-2-item.two span {
    margin-left: 40px;
}

.tp-counter-2-item.three {
    margin-left: 92px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-counter-2-item.three {
        margin-left: 20px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-counter-2-item.three {
        margin-left: 0;
    }
}

.tp-counter-2-item.three span {
    margin-left: 44px;
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-counter-2-btn {
        margin-bottom: 30px;
    }
}

.tp-counter-2-btn .tp-btn:hover {
    color: var(--tp-theme-secondary);
    background: var(--tp-common-white);
}

.tp-counter-2-btn .tp-btn svg {
    height: 13px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-counter-6-area {
        padding-bottom: 30px;
    }
}

.tp-counter-6-title {
    font-size: 55px;
    font-weight: 700;
    color: var(--tp-common-black);
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-counter-6-title {
        font-size: 45px;
    }
}

@media (max-width: 575px) {
    .tp-counter-6-title {
        font-size: 36px;
    }
}

.tp-counter-6-item-title {
    font-size: 135px;
    font-weight: 700;
    color: #F5F5F8;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), (max-width: 575px) {
    .tp-counter-6-item-title {
        font-size: 105px;
    }
}

.tp-counter-6-item span {
    position: absolute;
    top: 50%;
    left: 0;
    font-size: 18px;
    font-weight: 700;
    display: block;
    right: 0;
    text-align: center;
    transform: translateY(-50%);
    color: var(--tp-common-black);
}

.tp-counter-6-item.two {
    margin-left: 50px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-counter-6-item.two {
        margin-left: 20px;
    }
}

@media (max-width: 575px) {
    .tp-counter-6-item.two {
        margin-left: 0;
    }
}

.tp-counter-6-item.two span {
    margin-left: 40px;
}

.tp-counter-6-item.three {
    margin-left: 92px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-counter-6-item.three {
        margin-left: 20px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-counter-6-item.three {
        margin-left: 0;
    }
}

.tp-counter-6-item.three span {
    margin-left: 44px;
}

.tp-counter-6-btn .tp-btn:hover {
    background: var(--tp-heading-2);
}

.tp-counter-7-bg {
    background-repeat: no-repeat;
    background-size: cover;
    padding: 40px 100px;
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-counter-7-bg {
        padding: 40px 50px;
    }
}

.tp-counter-7-bg-color::before {
    position: absolute;
    content: "";
    height: 50%;
    width: 100%;
    bottom: 0;
    left: 0;
    background: rgb(248, 248, 248);
    z-index: -1;
}

.tp-counter-7-border {
    border-right: 1px solid rgb(21, 55, 109);
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-counter-7-border {
        border: none;
    }
}

.tp-counter-7-item {
    padding: 18px 0;
}

.tp-counter-7-item:hover .tp-counter-7-title {
    color: var(--tp-theme-primary);
}

.tp-counter-7-item p {
    font-size: 18px;
    font-weight: 600;
    color: #9ca5af;
}

.tp-counter-7-title {
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 0;
    color: var(--tp-common-white);
}

/*----------------------------------------*/
/*  7.5 Video Css
/*----------------------------------------*/
.tp-video-shape {
    position: absolute;
    top: 40px;
    right: 50px;
    z-index: -1;
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-video-shape {
        display: none;
    }
}

.tp-video-thumb {
    margin-left: -230px;
}

@media only screen and (min-width: 1600px) and (max-width: 1700px) {
    .tp-video-thumb {
        margin-left: -170px;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-video-thumb {
        margin-left: 0;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-video-thumb img {
        max-width: 100%;
    }
}

.tp-video-list {
    display: inline-block;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-video-list {
        margin-bottom: 20px;
    }
}

.tp-video-list ul li {
    list-style: none;
    font-size: 18px;
    font-weight: 600;
    color: var(--tp-theme-secondary);
    margin-bottom: 20px;
}

.tp-video-list ul li i {
    font-size: 14px;
    margin-right: 5px;
    color: var(--tp-theme-primary);
}

.tp-video-play {
    background-repeat: no-repeat;
    background-size: cover;
    padding: 30px 52px;
    flex: 0 0 auto;
    height: 100px;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .tp-video-play {
        padding: 30px 23px;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-video-play {
        padding: 32px 24px;
    }
}

@media (max-width: 575px) {
    .tp-video-play {
        padding: 30px 60px;
        margin-top: 20px;
    }
}

.tp-video-play::before {
    position: absolute;
    content: "";
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    opacity: 0.851;
    background-color: rgb(0, 35, 90);
}

.tp-video-popup a {
    position: relative;
    display: flex;
    align-items: center;
}

.tp-video-popup a p {
    font-weight: 500;
    text-transform: uppercase;
    color: var(--tp-common-white);
}

.tp-video-popup a:hover p {
    color: var(--tp-theme-primary);
}

.tp-video-popup span {
    position: relative;
    height: 46px;
    width: 46px;
    line-height: 46px;
    text-align: center;
    border-radius: 50%;
    font-size: 16px;
    margin-right: 15px;
    /*animation: tp-pulse-2 2s infinite;*/
    color: var(--tp-theme-secondary);
    background: var(--tp-common-white);
}

.tp-video-box {
    margin-bottom: 65px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-video-box {
        margin-bottom: 30px;
        flex-wrap: wrap;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-video-icon-content {
        margin-bottom: 20px;
    }
}

.tp-video-icon-content span {
    font-size: 50px;
    margin-right: 20px;
    margin-left: 10px;
    color: var(--tp-theme-primary);
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-video-icon-box {
        flex-wrap: wrap;
    }
}

.tp-video-title {
    font-size: 22px;
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-video-title-wrapper {
        margin-bottom: 30px;
    }
}

.tp-text-sliding {
    overflow: hidden;
}

.tp-text-scroll-hr {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    white-space: nowrap;
}

.tp-text-scroll-wrap {
    display: flex;
    -webkit-animation: scrollText 33s infinite linear;
    animation: scrollText 33s infinite linear;
}

.tp-text-title {
    font-size: 120px;
    font-weight: 700;
    color: transparent;
    -webkit-text-stroke: 1px rgba(0, 35, 90, 0.3);
}

.tp-progress-color {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    background-color: rgb(0, 35, 90);
    z-index: -1;
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-progress-shape {
        display: none;
    }
}

.tp-progress-shape .shape-1 {
    position: absolute;
    top: 0;
    left: 0;
}

.tp-progress-shape .shape-2 {
    position: absolute;
    top: 0;
    right: 0;
}

.tp-progress-title {
    font-size: 22px;
    font-size: 700;
    color: var(--tp-common-white);
}

.tp-progress-item .circular {
    position: relative;
    margin-bottom: 25px;
}

.tp-progress-item .circular::before {
    position: absolute;
    content: "";
    top: 50%;
    left: 50%;
    height: 80px;
    width: 80px;
    border-radius: 50%;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    transform: translate(-50%, -50%);
    background-color: rgb(0, 27, 69);
}

.tp-progress-item .circular .knob {
    font-family: var(--tp-ff-p) !important;
    font-size: 20px !important;
    font-weight: 700 !important;
    margin-top: 60px !important;
}

.tp-progress-item:hover .circular::before {
    background: var(--tp-theme-primary);
}

.tp-progress-wrap {
    margin-top: 60px;
}

.tp-progress-wrap span {
    font-size: 16px;
    font-weight: 500;
    color: #9ca5af;
    padding: 17px 32px;
    background-color: rgb(0, 27, 69);
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-progress-wrap span {
        padding: 0;
        background-color: transparent;
    }
}

.tp-progress-wrap span a {
    color: var(--tp-theme-primary);
}

.tp-progress-wrap span a:hover {
    color: var(--tp-common-white);
}

/*----------------------------------------*/
/*  8.0 Faq Css
/*----------------------------------------*/
.tp-faq-text {
    top: 41%;
    left: 100px;
    position: absolute;
    transform: rotate(-90deg) translateY(-102px);
}

.tp-faq-text-title {
    font-size: 100px;
    text-transform: uppercase;
    color: rgba(0, 35, 90, 0.039);
    margin: 0;
}

@media (max-width: 575px) {
    .tp-faq-text-title {
        display: none;
    }
}

.tp-faq-shape {
    position: absolute;
    bottom: 0;
    right: 0;
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-faq-title-wrapper {
        margin-bottom: 30px;
    }
}

.tp-faq-thumb-wrapper {
    position: relative;
    padding-left: 70px;
}

@media only screen and (min-width: 1600px), only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-faq-thumb-wrapper {
        padding-top: 60px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-faq-thumb-wrapper {
        padding-top: 40px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-faq-thumb-wrapper {
        padding-left: 0;
    }
}

@media only screen and (min-width: 1600px), only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), (max-width: 575px) {
    .tp-faq-thumb-wrapper img {
        max-width: 100%;
    }
}

.tp-faq-thumb-wrapper span {
    position: absolute;
    top: 17%;
    left: 14%;
    display: inline-block;
    width: 6px;
    height: 424px;
    z-index: 0;
    background: var(--tp-theme-primary);
}

@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-faq-thumb-wrapper span {
        left: 0;
    }
}

@media (max-width: 575px) {
    .tp-faq-thumb-wrapper span {
        top: 12%;
    }
}


.tp-video-thumb {
    position: relative;
    padding-right: 70px;
}

@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-video-thumb {
        padding-top: 35px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-video-thumb {
        padding-top: 40px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-video-thumb {
        padding-right: 0;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), (max-width: 575px) {
    .tp-video-thumb img {
        max-width: 100%;
    }
}

@media only screen and (min-width: 1600px) {
    .tp-video-thumb img {
        max-width: 90%;
    }
}

.tp-video-thumb span {
    position: absolute;
    top: 11%;
    right: 14%;
    display: inline-block;
    width: 6px;
    height: 424px;
    z-index: 0;
    background: var(--tp-theme-primary);
}

@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-video-thumb span {
        right: 0;
    }
}

@media (max-width: 575px) {
    .tp-video-thumb span {
        top: 12%;
    }
}

.tp-faq-wrapper {
    padding-right: 110px;
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-faq-wrapper {
        padding-right: 0;
    }
}

.tp-faq-wrapper.home-6 {
    padding-right: 0;
}

.tp-faq-wrapper .accordion .accordion-item {
    border: none;
    border-radius: 0;
    background: transparent;
}

.tp-faq-wrapper .accordion .accordion-item:not(:last-of-type) {
    margin-bottom: 30px;
}

.tp-faq-wrapper .accordion .accordion-item .accordion-header .accordion-button {
    font-size: 18px;
    font-weight: 600;
    border-radius: 0;
    padding: 19px 30px;
    color: var(--tp-theme-secondary);
    background-color: rgb(255, 255, 255);
}

@media (max-width: 575px) {
    .tp-faq-wrapper .accordion .accordion-item .accordion-header .accordion-button {
        padding-right: 50px;
    }
}

.tp-faq-wrapper .accordion .accordion-item .accordion-header .accordion-button::after {
    font-family: "Font Awesome 6 Pro";
    content: "\f107";
    position: absolute;
    top: 32%;
    right: 30px;
    font-weight: 400;
    background-image: none;
}

.tp-faq-wrapper .accordion .accordion-item .accordion-header .accordion-button:not(.collapsed) {
    border: none;
    color: var(--tp-common-white);
    background: var(--tp-theme-secondary);
}

.tp-faq-wrapper .accordion .accordion-item .accordion-header .accordion-button:not(.collapsed)::after {
    right: 36px;
    color: var(--tp-common-white);
}

.tp-faq-wrapper .accordion .accordion-item .accordion-header .accordion-button:focus {
    box-shadow: none;
}

.tp-faq-wrapper .accordion .accordion-item .accordion-body {
    background: transparent;
    padding: 24px 30px 0 30px;
}

.tp-faq-wrapper .accordion .accordion-item .accordion-body p {
    color: #565969;
}

.tp-faq-5-shape {
    position: absolute;
    bottom: 17%;
    right: 3%;
    z-index: 1;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
    .tp-faq-5-shape img {
        width: 75%;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-faq-5-shape {
        display: none;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-faq-5-hight {
        padding-top: 100px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-faq-5-box {
        padding-top: 0;
    }
}

.tp-faq-6-shape {
    position: absolute;
    bottom: -180px;
    left: 0;
    z-index: 0;
}

.tp-faq-6-title-wrapper {
    padding-right: 72px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-faq-6-title-wrapper {
        padding-right: 0;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-faq-6-title-wrapper {
        margin-bottom: 40px;
    }
}

.tp-faq-6-title-wrapper p {
    font-size: 16px;
    color: #767676;
}

.tp-faq-6-box .tp-faq-wrapper .accordion .accordion-item .accordion-header .accordion-button:not(.collapsed) {
    background: var(--tp-theme-4);
}

.tp-faq-bdc-item::after {
    position: absolute;
    content: "";
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    background: rgba(11, 17, 37, 0.8);
}

.tp-faq-bdc-content {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    text-align: center;
    transform: translateY(-50%);
    z-index: 1;
}

.tp-faq-bdc-content p {
    margin-bottom: 20px;
    color: var(--tp-common-white);
}

.tp-faq-bdc-content.active .tp-faq-bdc-title {
    margin-bottom: 15px;
}

.tp-faq-bdc-content.active .tp-faq-bdc-icon a {
    border: 1px solid var(--tp-theme-primary);
    background: var(--tp-theme-primary);
}

.tp-faq-bdc-content.active .tp-faq-bdc-icon a:hover {
    background: transparent;
    border: 1px solid rgb(247, 247, 249);
}

.tp-faq-bdc-title {
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 30px;
    color: var(--tp-common-white);
}

.tp-faq-bdc-icon a {
    display: inline-block;
    line-height: 65px;
    height: 60px;
    width: 60px;
    border-radius: 50%;
    text-align: center;
    font-size: 20px;
    color: var(--tp-common-white);
    border: 1px solid rgb(247, 247, 249);
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-faq-bdc-icon a:hover {
    border: 1px solid var(--tp-theme-primary);
    background: var(--tp-theme-primary);
}

/*----------------------------------------*/
/*  8.9 Team Css
/*----------------------------------------*/
.tp-team-shape {
    position: absolute;
    top: 70px;
    left: 0;
}

.tp-team-shape img {
    width: 100%;
}

.tp-team-item:hover .tp-team-hover {
    height: 300px;
    opacity: 1;
}

.tp-team-item:hover .tp-team-social {
    transform: translateY(0);
    visibility: visible;
    opacity: 1;
    transition-delay: 0.4s;
}

.tp-team-thumb {
    overflow: hidden;
}

.tp-team-thumb img {
    width: 100%;
}

.tp-team-hover {
    position: absolute;
    left: 0px;
    right: 0;
    margin: 0 auto;
    width: 334px;
    height: 0;
    opacity: 0;
    bottom: -195px;
    border-radius: 50%;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    background: var(--tp-theme-primary);
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 575px) {
    .tp-team-hover {
        width: 100%;
    }
}

.tp-team-social {
    padding: 40px 0px;
    text-align: center;
    transform: translateY(5px);
    transition-delay: 0.1s;
    transition: 0.2s;
    opacity: 0;
    visibility: hidden;
}

.tp-team-social a {
    position: relative;
    font-size: 14px;
    color: var(--tp-common-white);
}

.tp-team-social a:not(:last-of-type) {
    margin-right: 15px;
}

.tp-team-social a:hover {
    color: var(--tp-theme-secondary);
}

.tp-team-title {
    font-size: 24px;
    font-weight: 700;
    color: var(--tp-theme-secondary);
}

.tp-team-title a:hover {
    color: var(--tp-theme-primary);
}

.tp-team-share {
    margin-bottom: 20px;
    margin-top: -25px;
    position: relative;
}

.tp-team-share button {
    width: 38px;
    height: 38px;
    line-height: 38px;
    text-align: center;
    border-radius: 50%;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    color: var(--tp-common-white);
    background: var(--tp-theme-secondary);
}

.tp-team-2-text {
    position: absolute;
    top: 8%;
    left: 20%;
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-team-2-text {
        left: 0;
    }
}

@media (max-width: 575px) {
    .tp-team-2-text {
        top: 3%;
    }
}

.tp-team-2-box {
    padding-left: 40px;
    padding-right: 40px;
}

.tp-team-2-title-wrapper .tp-section-title {
    margin: 0;
}

.tp-team-2-item:hover .tp-team-2-thumb img {
    transform: scale(1.1);
}

.tp-team-2-item:hover .tp-team-2-title {
    color: var(--tp-common-white);
}

.tp-team-2-item:hover .tp-team-2-content::after {
    height: 100%;
    top: auto;
    bottom: 0;
}

.tp-team-2-item:hover .tp-team-2-content span {
    color: var(--tp-common-white);
}

.tp-team-2-item:hover .tp-team-2-shape {
    right: 0;
}

.tp-team-2-item:hover .tp-team-2-social a {
    color: var(--tp-common-white);
}

.tp-team-2-thumb img {
    width: 100%;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-team-2-content {
    padding: 30px 40px 25px;
    -webkit-transition: 1s;
    -moz-transition: 1s;
    -ms-transition: 1s;
    -o-transition: 1s;
    transition: 1s;
    background-color: rgb(255, 255, 255);
    box-shadow: 0px 0px 40px 0px rgba(30, 22, 22, 0.06);
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-team-2-content {
        padding: 30px 20px 25px;
    }
}

.tp-team-2-content::after {
    position: absolute;
    content: "";
    width: 100%;
    height: 0;
    bottom: auto;
    left: 0;
    top: 0;
    background: var(--tp-theme-secondary);
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    z-index: 0;
}

.tp-team-2-content span {
    position: relative;
    z-index: 1;
    display: block;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 5px;
    -webkit-transition: 0.5s;
    -moz-transition: 0.5s;
    -ms-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s;
    color: var(--tp-theme-secondary);
}

.tp-team-2-title {
    position: relative;
    z-index: 2;
    font-size: 24px;
    font-weight: 700;
    transition: none;
    margin-bottom: 15px;
    letter-spacing: 0.02px;
    color: var(--tp-theme-secondary);
}

.tp-team-2-title a:hover {
    color: var(--tp-theme-primary);
}

.tp-team-2-social {
    position: relative;
    z-index: 1;
}

.tp-team-2-social a {
    font-size: 14px;
    white-space: none;
    color: var(--tp-theme-secondary);
    -webkit-transition: 0.5s;
    -moz-transition: 0.5s;
    -ms-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s;
}

.tp-team-2-social a i {
    -webkit-transition: 0.5s;
    -moz-transition: 0.5s;
    -ms-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s;
}

.tp-team-2-social a:hover i {
    color: var(--tp-theme-primary);
}

.tp-team-2-social a:not(:last-of-type) {
    margin-right: 10px;
}

.tp-team-2-shape {
    position: absolute;
    right: -30px;
    top: 0;
    z-index: 1;
    -webkit-transition: 0.5s;
    -moz-transition: 0.5s;
    -ms-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-team-6-area {
        padding-top: 50px;
    }
}

.tp-team-6-bg {
    position: absolute;
    top: -30px;
    left: 0;
    height: 180px;
    width: 100%;
}

.tp-team-6-box {
    margin-top: -165px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-team-6-box {
        margin-top: 0;
    }
}

.tp-team-6-box .row .col-lg-6:nth-child(even) {
    margin-top: 40px;
}

.tp-team-6-title-wrapper {
    position: relative;
    z-index: 1;
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-team-6-title-wrapper {
        margin-bottom: 30px;
    }
}

.tp-team-6-title-wrapper p {
    font-size: 16px;
    color: #727272;
    margin-bottom: 50px;
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-team-6-title-wrapper p {
        margin-bottom: 30px;
    }
}

.tp-team-6-content {
    padding: 25px 40px;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    background: var(--tp-common-white);
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-team-6-content {
        padding: 20px 30px;
    }
}

.tp-team-6-content:after {
    position: absolute;
    content: "";
    width: 100%;
    height: 0;
    bottom: auto;
    left: 0;
    top: 0;
    background: var(--tp-heading-2);
    transition: all 0.3s 0s ease-out;
    z-index: 0;
}

.tp-team-6-content span {
    font-size: 16px;
    color: #767676;
    display: block;
    margin-bottom: 5px;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    position: relative;
    z-index: 1;
}

.tp-team-6-thumb img {
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-team-6-thumb img {
        width: 100%;
    }
}

.tp-team-6-title {
    font-size: 24px;
    font-weight: 700;
    -webkit-transition: 1s 0.3s 0s ease-out;
    -moz-transition: 1s 0.3s 0s ease-out;
    -ms-transition: 1s 0.3s 0s ease-out;
    -o-transition: 1s 0.3s 0s ease-out;
    transition: 1s 0.3s 0s ease-out;
    color: var(--tp-heading-2);
    position: relative;
    z-index: 1;
}

.tp-team-6-item:hover .tp-team-6-content::after {
    height: 100%;
    top: auto;
    bottom: 0;
}

.tp-team-6-item:hover .tp-team-6-content span {
    color: var(--tp-common-white);
}

.tp-team-6-item:hover .tp-team-6-title {
    color: var(--tp-common-white);
}

.tp-team-6-item:hover .tp-team-6-thumb img {
    transform: scale(1.1);
}

.tp-team-7-item {
    padding: 50px;
    border-width: 1px;
    border-color: rgb(235, 235, 235);
    border-style: solid;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-team-7-item {
        padding: 30px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-team-7-item {
        padding: 20px;
    }
}

.tp-team-7-item:hover::after {
    height: 4px;
}

.tp-team-7-item:hover::before {
    height: 100%;
}

.tp-team-7-item:hover .tp-team-7-title {
    color: var(--tp-common-white);
}

.tp-team-7-item:hover .tp-team-7-content span {
    color: #8199bf;
}

.tp-team-7-item:hover .tp-team-7-social a {
    color: var(--tp-common-white);
}

.tp-team-7-item:hover .tp-team-7-social a:hover {
    color: var(--tp-theme-primary);
}

.tp-team-7-item::after {
    position: absolute;
    top: 0;
    left: 0;
    content: "";
    height: 0px;
    width: 100%;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    background: var(--tp-theme-primary);
}

.tp-team-7-item::before {
    position: absolute;
    content: "";
    top: 0px;
    left: 0;
    height: 0;
    width: 100%;
    transition: all 0.5s ease-in-out;
    z-index: -1;
    background: var(--tp-theme-secondary);
}

.tp-team-7-thumb {
    margin-bottom: 70px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-team-7-thumb {
        margin-bottom: 50px;
    }
}

.tp-team-7-thumb img {
    border-radius: 50%;
}

.tp-team-7-content {
    margin-bottom: 25px;
}

.tp-team-7-content span {
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-team-7-title {
    font-size: 20px;
    font-weight: 700;
}

.tp-team-7-title a:hover {
    color: var(--tp-theme-primary);
}

.tp-team-7-social a {
    font-size: 14px;
    color: var(--tp-theme-secondary);
}

.tp-team-7-social a:not(:last-of-type) {
    margin-right: 15px;
}

.tp-team-details-information {
    padding: 85px 5px 85px 108px;
    background-color: rgb(255, 255, 255);
    border: 1px solid rgb(226, 225, 225);
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-team-details-information {
        padding: 70px 20px 70px 30px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-team-details-information {
        padding: 30px;
    }
}

.tp-team-details-wrapper {
    padding-left: 70px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-team-details-wrapper {
        padding-left: 0;
    }
}

.tp-team-details-wrapper .shape {
    position: absolute;
    bottom: 0;
    right: -95px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-team-details-wrapper .shape {
        display: none;
    }
}

.tp-team-details-wrapper p {
    margin-bottom: 30px;
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-team-details-wrapper p br {
        display: none;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-team-details-thumb {
        margin-bottom: 30px;
    }
}

.tp-team-details-thumb img {
    width: 100%;
}

.tp-team-details-title {
    font-size: 40px;
    font-weight: 600;
    color: var(--tp-heading-primary);
}

.tp-team-details-subtitle {
    font-size: 18px;
    font-weight: 400;
    display: inline-block;
    margin-bottom: 35px;
    color: var(--tp-theme-primary);
}

.tp-team-details-info {
    position: relative;
}

.tp-team-details-info:not(:last-of-type) {
    margin-bottom: 20px;
}

.tp-team-details-info span {
    position: absolute;
    left: 0;
    top: 2px;
    font-size: 18px;
    font-weight: 700;
    letter-spacing: 0.16px;
    color: var(--tp-heading-primary);
}

@media (max-width: 575px) {
    .tp-team-details-info span {
        position: static;
    }
}

.tp-team-details-info a {
    margin-left: 155px;
    font-size: 16px;
    font-weight: 400;
    color: var(--tp-text-body);
}

@media (max-width: 575px) {
    .tp-team-details-info a {
        margin-left: 0;
        display: block;
        margin-top: 10px;
    }
}

.tp-team-details-info a:hover {
    color: var(--tp-theme-primary);
}

.tp-team-details-info-title {
    font-size: 36px;
    font-weight: 800;
    margin-bottom: 20px;
    color: var(--tp-heading-primary);
}

.tp-team-details-social {
    margin-top: 40px;
}

.tp-team-details-social a {
    font-size: 14px;
    margin-right: 18px;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    color: var(--tp-heading-primary);
}

.tp-team-details-social a:hover {
    color: var(--tp-theme-primary);
}

.tp-team-details-wrap p {
    margin-bottom: 30px;
}

.tp-team-details-progress-item {
    position: relative;
    margin-bottom: 23px;
}

.tp-team-details-progress-item p {
    display: inline-block;
    font-size: 16px;
    font-weight: 700;
    color: var(--tp-heading-primary);
    margin-bottom: 6px;
}

.tp-team-details-progress-item span {
    position: absolute;
    right: 10px;
    bottom: 6px;
    font-size: 14px;
    color: var(--tp-text-body);
}

.tp-team-details-progress-item .progress {
    height: 4px;
    border-radius: 0;
}

.tp-team-details-progress-item .progress-bar {
    background-color: var(--tp-theme-primary);
}

@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-team-details-elaborate-wrap {
        padding-left: 30px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-team-details-elaborate-wrap {
        padding-left: 0;
    }
}

.tp-team-details-form {
    margin-top: 20px;
    padding: 70px 75px;
    background-color: rgb(244, 244, 244);
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-team-details-form {
        padding: 30px;
    }
}

.tp-team-details-form p {
    font-size: 14px;
    margin-bottom: 32px;
}

.tp-team-details-form-title {
    font-size: 36px;
    font-weight: 800;
    margin-bottom: 2px;
    color: var(--tp-heading-primary);
}

.tp-team-details-form .team-form-input input {
    height: 60px;
    font-size: 16px;
    color: var(--tp-text-body);
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    border: 1px solid var(--tp-common-white);
}

.tp-team-details-form .team-form-input input::placeholder {
    font-size: 16px;
    color: var(--tp-text-body);
}

.tp-team-details-form .team-form-input input:focus {
    border: 1px solid var(--tp-theme-primary);
}

.tp-team-details-form .team-form-input textarea {
    height: 148px;
    resize: none;
    font-size: 16px;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    border: 1px solid var(--tp-common-white);
    color: var(--tp-text-body);
}

.tp-team-details-form .team-form-input textarea::placeholder {
    font-size: 16px;
    color: var(--tp-text-body);
}

.tp-team-details-form .team-form-input textarea:focus {
    border: 1px solid var(--tp-theme-primary);
}

/*----------------------------------------*/
/*  7.8 Contact Css
/*----------------------------------------*/
@media (max-width: 575px) {
    .tp-contact-area {
        padding-top: 130px;
        padding-bottom: 130px;
    }
}

.tp-contact-bg {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-size: cover;
    background-repeat: no-repeat;
}

.tp-contact-bg::before {
    position: absolute;
    content: "";
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    opacity: 0.949;
    background: var(--tp-theme-secondary);
}

.tp-contact-title-wrapper {
    padding-right: 24px;
    z-index: 1;
}

.tp-contact-title-wrapper .tp-section-title {
    color: var(--tp-common-white);
    margin-bottom: 30px;
}

.tp-contact-title-wrapper .tp-section-title-pre {
    color: var(--tp-common-white);
}

.tp-contact-title-wrapper .tp-section-title-pre::after {
    border: 1px solid var(--tp-common-white);
}

.tp-contact-title-wrapper p {
    margin-bottom: 55px;
    color: var(--tp-common-white);
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-contact-title-wrapper p {
        margin-bottom: 40px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-contact-title-wrapper p br {
        display: none;
    }
}

.tp-contact-thumb .shape-main {
    position: absolute;
    bottom: 0;
    right: 17%;
    z-index: 4;
}

@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-contact-thumb .shape-main {
        right: 2%;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-contact-thumb .shape-main {
        right: 0;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-contact-thumb .shape-main {
        display: none;
    }
}

.tp-contact-thumb .shape-1 {
    position: absolute;
    top: 0;
    right: 6%;
}

@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-contact-thumb .shape-1 {
        right: 2%;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-contact-thumb .shape-1 {
        right: 0;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-contact-thumb .shape-1 {
        display: none;
    }
}

.tp-contact-thumb .shape-2 {
    position: absolute;
    bottom: 50px;
    right: 7%;
}

@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-contact-thumb .shape-2 {
        right: 2%;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-contact-thumb .shape-2 {
        right: 0;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-contact-thumb .shape-2 {
        display: none;
    }
}

.tp-contact-thumb .shape-3 {
    position: absolute;
    top: 25px;
    right: 14%;
}

@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-contact-thumb .shape-3 {
        right: 2%;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-contact-thumb .shape-3 {
        right: 0;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-contact-thumb .shape-3 {
        display: none;
    }
}

.tp-contact-btn-box .tp-btn:not(:last-of-type) {
    margin-right: 25px;
}

@media (max-width: 575px) {
    .tp-contact-btn-box .tp-btn:not(:last-of-type) {
        margin-bottom: 30px;
    }
}

.tp-contact-btn-box .tp-btn:hover {
    color: var(--tp-theme-secondary);
    background: var(--tp-common-white);
}

.tp-contact-btn-box .tp-btn.white {
    color: var(--tp-theme-secondary);
    background: var(--tp-common-white);
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .tp-contact-2-p {
        padding-top: 130px;
        padding-bottom: 130px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-contact-2-p {
        padding-top: 100px;
        padding-bottom: 120px;
    }
}

@media (max-width: 575px) {
    .tp-contact-2-p {
        padding-top: 100px;
        padding-bottom: 100px;
    }
}

.tp-contact-2-shape {
    position: absolute;
    top: 40px;
    z-index: 0;
    height: 100%;
    width: 100%;
    background-repeat: no-repeat;
    background-position: top center;
}

@media only screen and (min-width: 1701px) and (max-width: 1800px) {
    .tp-contact-2-shape img {
        width: 100%;
    }
}

.tp-contact-2-title {
    font-size: 55px;
    font-weight: 700;
    letter-spacing: 0.02px;
    margin-bottom: 40px;
    color: var(--tp-common-white);
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-contact-2-title {
        font-size: 50px;
    }
}

@media (max-width: 575px) {
    .tp-contact-2-title {
        font-size: 38px;
    }
}

.tp-contact-2-call {
    margin-bottom: 45px;
}

.tp-contact-2-call span {
    font-size: 18px;
    font-weight: 600;
    color: var(--tp-common-white);
}

.tp-contact-2-call span a {
    color: var(--tp-theme-primary);
}

.tp-contact-2-call span a:hover {
    color: var(--tp-common-white);
}

.tp-contact-2-btn .tp-btn:hover {
    color: var(--tp-theme-primary);
    background: var(--tp-common-white);
}

.tp-contact-6-area {
    background-repeat: no-repeat;
    background-size: cover;
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-contact-6-area {
        padding-bottom: 180px;
    }
}

@media (max-width: 575px) {
    .tp-contact-6-area {
        padding-bottom: 160px;
    }
}

.tp-contact-6-shape {
    position: absolute;
    bottom: 0;
    left: 0;
}

.tp-contact-6-shape img {
    width: 100%;
}

.tp-contact-6-box {
    padding: 65px 80px 50px 80px;
    background: var(--tp-common-white);
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-contact-6-box {
        padding: 40px;
    }
}

@media (max-width: 575px) {
    .tp-contact-6-box {
        padding: 30px;
    }
}

.tp-contact-6-title {
    font-size: 55px;
    font-weight: 700;
    color: var(--tp-heading-2);
    margin-bottom: 60px;
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-contact-6-title {
        font-size: 40px;
        margin-bottom: 30px;
    }
}

.tp-contact-6-form input {
    font-size: 16px;
    margin-bottom: 30px;
}

.tp-contact-6-form input::placeholder {
    font-size: 16px;
}

.tp-contact-6-form input:focus {
    border: 1px solid var(--tp-theme-4);
}

.tp-contact-6-btn .tp-btn {
    background: var(--tp-theme-4);
}

.tp-contact-6-btn .tp-btn span {
    font-size: 18px;
    margin-left: 8px;
}

.tp-contact-7-bg {
    position: absolute;
    top: 0;
    left: 0;
    height: 536px;
    width: 69%;
    background-repeat: no-repeat;
    background-position: top left;
    z-index: -1;
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-contact-7-bg {
        width: 100%;
    }
}

.tp-contact-7-bg::before {
    position: absolute;
    content: "";
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    opacity: 0.9;
    background: var(--tp-theme-secondary);
    z-index: 0;
}

.tp-contact-7-shape {
    position: absolute;
    bottom: 0px;
    right: 0;
    z-index: 2;
}

@media only screen and (min-width: 1701px) and (max-width: 1800px), only screen and (min-width: 1600px) and (max-width: 1700px), only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-contact-7-shape {
        z-index: 1;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-contact-7-shape {
        display: none;
    }
}

.tp-contact-7-title {
    font-size: 26px;
    font-weight: 700;
    margin-bottom: 40px;
}

.tp-contact-7-title-wrapper {
    position: relative;
    z-index: 1;
    margin-bottom: 100px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-contact-7-title-wrapper {
        margin-bottom: 65px;
    }
}

.tp-contact-7-title-wrapper .tp-section-title {
    margin-bottom: 50px;
    color: var(--tp-common-white);
}

.tp-contact-7-angel {
    position: absolute;
    bottom: -30px;
    right: 15px;
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-contact-7-angel {
        display: none;
    }
}

.tp-contact-7-box {
    position: relative;
    padding: 60px 55px;
    z-index: 1;
    background: var(--tp-common-white);
    box-shadow: 0px 0px 60px 0px rgba(30, 22, 22, 0.06);
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
    .tp-contact-7-box {
        padding: 50px 35px;
    }
}

@media (max-width: 575px) {
    .tp-contact-7-box {
        padding: 40px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 575px) {
    .tp-contact-7-project {
        flex-wrap: wrap;
    }
}

.tp-contact-7-form input, .tp-contact-7-form textarea {
    margin-bottom: 20px;
    font-size: 16px;
    font-weight: 400;
    color: #565969;
    height: 60px;
    border: 1px solid rgb(235, 235, 235);
}

.tp-contact-7-form input::placeholder, .tp-contact-7-form textarea::placeholder {
    font-size: 16px;
    font-weight: 400;
    color: #565969;
}

.tp-contact-7-form input:focus, .tp-contact-7-form textarea:focus {
    border: 1px solid var(--tp-theme-primary);
}

.tp-contact-7-form textarea {
    height: 126px;
    resize: none;
}

.tp-contact-7-complete {
    position: relative;
    padding: 50px;
    z-index: 2;
    background: var(--tp-common-white);
    box-shadow: 0px 0px 60px 0px rgba(0, 8, 21, 0.1);
}

.tp-contact-7-complete:not(:last-of-type) {
    margin-right: 20px;
}

@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 575px) {
    .tp-contact-7-complete:not(:last-of-type) {
        margin-right: 0;
        margin-bottom: 30px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-contact-7-complete {
        padding: 40px;
    }
}

@media (max-width: 575px) {
    .tp-contact-7-complete {
        padding: 30px;
        margin-bottom: 30px;
    }
}

.tp-contact-7-count {
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 5px;
}

.tp-contact-7-btn .tp-btn:hover {
    color: var(--tp-theme-secondary);
    background: var(--tp-common-white);
}

.tp-contact-wrapper {
    position: relative;
    z-index: 1;
    padding: 100px;
    /*margin-top: -80px;*/
    margin-top: 50px;
    background-color: rgb(255, 255, 255);
    box-shadow: 0px 0px 80px 0px rgba(30, 22, 22, 0.04);
}

@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-contact-wrapper {
        padding: 70px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-contact-wrapper {
        padding: 50px;
    }
}

@media (max-width: 575px) {
    .tp-contact-wrapper {
        padding: 30px;
    }
}

.tp-contact-title {
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 30px;
    color: var(--tp-theme-secondary);
}

.tp-contact-input {
    margin-bottom: 30px;
}

.tp-contact-input input, .tp-contact-input textarea {
    font-size: 16px;
    font-weight: 400;
    height: 60px;
    color: #565969;
    background-color: rgb(248, 248, 248);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.tp-contact-input input::placeholder, .tp-contact-input textarea::placeholder {
    font-size: 16px;
    font-weight: 400;
    color: #565969;
}

.tp-contact-input input:focus, .tp-contact-input textarea:focus {
    border: 1px solid var(--tp-theme-primary);
}

.tp-contact-input textarea {
    height: 166px;
    resize: none;
}

.tp-contact-item {
    position: relative;
    z-index: 1;
    padding: 30px;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    background-color: rgb(255, 255, 255);
    box-shadow: 0px 4px 60px 0px rgba(22, 23, 26, 0.06);
}

.tp-contact-item:hover {
    background: var(--tp-theme-secondary);
}

.tp-contact-item:hover .tp-contact-item-icon span {
    color: var(--tp-common-white);
    background: var(--tp-theme-primary);
}

.tp-contact-item:hover .tp-contact-item-title {
    color: var(--tp-common-white);
}

.tp-contact-item:hover .tp-contact-item-content a {
    color: var(--tp-common-white);
}

.tp-contact-item-icon {
    margin-bottom: 22px;
}

.tp-contact-item-icon span {
    font-size: 26px;
    height: 80px;
    width: 80px;
    line-height: 85px;
    display: inline-block;
    border-radius: 50%;
    text-align: center;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    color: var(--tp-theme-primary);
    background: rgb(245, 245, 248);
}

.tp-contact-item-content p {
    font-size: 16px;
    font-weight: 400;
    color: #565969;
}

.tp-contact-item-title {
    font-size: 26px;
    font-weight: 700;
    margin-bottom: 10px;
    color: var(--tp-theme-secondary);
}

.tp-contact-box {
    margin-bottom: -80px;
}

/*----------------------------------------*/
/*  8.5 Pricing Css
/*----------------------------------------*/
.tp-pricing-item {
    padding: 55px;
    background-color: rgb(255, 255, 255);
    box-shadow: 0px 6px 60px 0px rgba(30, 22, 22, 0.06);
    z-index: 1;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-pricing-item {
        padding: 40px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-pricing-item {
        padding: 30px;
    }
}

.tp-pricing-item .shape-bg {
    position: absolute;
    right: 0;
    top: 0;
    z-index: -1;
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-pricing-item .shape-bg img {
        width: 91%;
    }
}

.tp-pricing-item.active {
    background: var(--tp-theme-secondary);
}

.tp-pricing-item.active .tp-pricing-head span {
    color: var(--tp-common-white);
}

.tp-pricing-item.active .tp-pricing-tag {
    color: var(--tp-common-white);
}

.tp-pricing-item.active .tp-pricing-list ul li {
    color: var(--tp-common-white);
}

.tp-pricing-item.active .tp-pricing-price {
    color: var(--tp-common-white);
}

.tp-pricing-item.active .tp-pricing-price span {
    color: var(--tp-common-white);
}

.tp-pricing-item.active .tp-pricing-btn .tp-btn {
    background: var(--tp-theme-primary);
}

.tp-pricing-item.active .tp-pricing-btn .tp-btn:hover {
    color: var(--tp-theme-secondary);
    background: var(--tp-common-white);
}

.tp-pricing-head {
    margin-bottom: 35px;
}

.tp-pricing-head span {
    display: block;
    font-size: 16px;
    color: #565969;
}

.tp-pricing-tag {
    font-size: 26px;
    font-weight: 700;
    letter-spacing: 0.4px;
}

.tp-pricing-list {
    display: block;
    border-bottom: 1px dashed rgb(234, 234, 234);
    margin-bottom: 30px;
    padding-bottom: 30px;
}

.tp-pricing-list ul li {
    list-style: none;
    font-size: 16px;
    font-weight: 600;
    color: var(--tp-theme-secondary);
}

.tp-pricing-list ul li.disabled {
    color: #565969;
}

.tp-pricing-list ul li:not(:last-of-type) {
    margin-bottom: 12px;
}

.tp-pricing-list ul li i {
    font-size: 14px;
    font-weight: 400;
    margin-right: 10px;
    color: var(--tp-theme-primary);
}

.tp-pricing-price {
    font-size: 48px;
    font-weight: 700;
    letter-spacing: 0.2px;
    color: var(--tp-theme-primary);
    margin-bottom: 45px;
}

.tp-pricing-price span {
    display: inline-block;
    font-size: 18px;
    font-weight: 700;
    letter-spacing: 0.2px;
    color: var(--tp-theme-secondary);
}

.tp-pricing-btn .tp-btn {
    background: var(--tp-theme-secondary);
}

.tp-pricing-btn .tp-btn:hover {
    background: var(--tp-theme-primary);
}

.tp-pricing-7-box {
    box-shadow: 0px 6px 60px 0px rgba(30, 22, 22, 0.06);
}

.tp-pricing-7-item {
    padding: 50px;
    background-color: rgb(255, 255, 255);
    z-index: 1;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-pricing-7-item {
        padding: 40px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 575px) {
    .tp-pricing-7-item {
        padding: 30px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-pricing-7-item {
        margin-bottom: 30px;
    }
}

.tp-pricing-7-item .shape-bg {
    position: absolute;
    right: 0;
    top: 0;
    z-index: -1;
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-pricing-7-item .shape-bg img {
        width: 91%;
    }
}

.tp-pricing-7-item.active {
    background: var(--tp-theme-secondary);
    transform: translateY(-40px);
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-pricing-7-item.active {
        transform: translateY(0);
    }
}

.tp-pricing-7-item.active .tp-pricing-7-tag-title {
    color: var(--tp-common-white);
}

.tp-pricing-7-item.active .tp-pricing-7-price {
    color: var(--tp-common-white);
}

.tp-pricing-7-item.active .tp-pricing-7-head span {
    color: var(--tp-common-white);
}

.tp-pricing-7-item.active .tp-pricing-7-list ul li {
    color: var(--tp-common-white);
}

.tp-pricing-7-item.active .tp-pricing-7-btn .tp-btn {
    color: var(--tp-common-white);
    background: var(--tp-theme-primary);
}

.tp-pricing-7-item.active .tp-pricing-7-btn .tp-btn:hover {
    background: var(--tp-common-white);
    color: var(--tp-common-black);
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-pricing-7-title-wrapper {
        margin-bottom: 30px;
    }
}

.tp-pricing-7-head {
    position: relative;
    margin-bottom: 40px;
    padding-bottom: 40px;
}

.tp-pricing-7-head::after {
    position: absolute;
    content: "";
    left: 0;
    bottom: 0;
    width: 150px;
    height: 2px;
    background: rgb(248, 248, 248);
}

.tp-pricing-7-head span {
    font-size: 16px;
    font-weight: 700;
    color: var(--tp-theme-secondary);
}

.tp-pricing-7-list {
    display: block;
    margin-bottom: 35px;
}

.tp-pricing-7-list ul li {
    list-style: none;
    font-size: 16px;
    font-weight: 400;
    color: var(--tp-theme-secondary);
}

.tp-pricing-7-list ul li:not(:last-of-type) {
    margin-bottom: 15px;
}

.tp-pricing-7-list ul li i {
    margin-right: 15px;
    color: var(--tp-theme-primary);
}

.tp-pricing-7-list ul li.disabled {
    color: #727272;
}

.tp-pricing-7-tag {
    position: absolute;
    right: -70px;
    top: 72px;
    transform: rotate(-90deg) translateY(-102px);
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-pricing-7-tag {
        right: -100px;
    }
}

@media (max-width: 575px) {
    .tp-pricing-7-tag {
        right: -100px;
        top: 75px;
        transform: rotate(-90deg) translateY(-100px);
    }
}

.tp-pricing-7-tag-title {
    font-size: 24px;
    font-weight: 700;
    margin: 0;
}

.tp-pricing-7-icon {
    margin-bottom: 30px;
}

.tp-pricing-7-btn .tp-btn {
    color: var(--tp-theme-secondary);
    background: #f8f8f8;
}

.tp-pricing-7-btn .tp-btn:hover {
    color: var(--tp-common-white);
    background: var(--tp-theme-primary);
}

.tp-pricing-7-price {
    font-size: 50px;
    font-weight: 700;
    margin-bottom: 10px;
    color: var(--tp-theme-primary);
}

/*----------------------------------------*/
/*  7.6 Brand Css
/*----------------------------------------*/
.tp-brand-area .container .row [class*=col-] {
    text-align: center;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-brand-area .container .row [class*=col-] {
        text-align: inherit !important;
        margin-bottom: 30px;
    }
}

@media (max-width: 575px) {
    .tp-brand-area .container .row [class*=col-] {
        text-align: center !important;
        margin-bottom: 30px;
    }
}

.tp-brand-area .container .row [class*=col-]:first-child {
    text-align: start;
}

.tp-brand-area .container .row [class*=col-]:last-child {
    text-align: end;
}

.tp-brand-bg {
    background: var(--tp-theme-secondary);
}

.tp-brand-item {
    position: relative;
    display: inline-block;
    overflow: hidden;
    vertical-align: top;
}

.tp-brand-item-img {
    position: relative;
    display: block;
    width: auto;
    margin: 0 auto;
    opacity: 0.18;
    transition: transform 0.4s ease;
}

.tp-brand-item-img.home-3 {
    opacity: 1;
}

.tp-brand-item-hover {
    position: absolute;
    top: 0;
    left: 50%;
    width: auto;
    transform: translate(-50%, -130%);
    transition: transform 0.5s ease;
}

.tp-brand-item:hover .tp-brand-item-img {
    transform: translateY(100%);
}

.tp-brand-item:hover .tp-brand-item-hover {
    transform: translate(-50%, 0);
}

.tp-brand-6-area .tp-brand-item-img {
    opacity: 1;
}

.tp-brand-3-bg {
    background: var(--tp-theme-primary);
}

.tp-brand-3-shape {
    position: absolute;
    top: 0;
    left: 0;
}

.tp-market-bg {
    background: var(--tp-theme-secondary);
}

.tp-market-shape {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 0;
}

@media (max-width: 575px) {
    .tp-market-shape {
        left: 0;
    }
}

@media (max-width: 575px) {
    .tp-market-shape img {
        width: 100%;
    }
}

.tp-market-shape-2 {
    position: absolute;
    bottom: 0;
    right: 0;
}

.tp-market-wrap {
    padding-bottom: 100px;
}

.tp-market-wrapper {
    padding-left: 15px;
}

.tp-market-thumb {
    margin-left: -230px;
    position: relative;
}

@media only screen and (min-width: 1600px) and (max-width: 1700px) {
    .tp-market-thumb {
        margin-left: -170px;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-market-thumb {
        margin-left: 0;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-market-thumb {
        margin-bottom: 30px;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-market-thumb img {
        width: 100%;
    }
}

.tp-market-title-wrapper {
    margin-bottom: 50px;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-market-title-wrapper {
        margin-bottom: 30px;
    }
}

.tp-market-title-wrapper .tp-section-title {
    color: var(--tp-common-white);
    letter-spacing: -2px;
}

.tp-market-title-wrapper .tp-section-title-pre {
    color: var(--tp-common-white);
}

.tp-market-title-wrapper .tp-section-title-pre::after {
    border: 1px solid var(--tp-common-white);
}

.tp-market-list {
    display: block;
    margin-bottom: 60px;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-market-list {
        margin-bottom: 30px;
    }
}

.tp-market-list ul li {
    list-style: none;
    font-size: 18px;
    font-weight: 600;
    color: var(--tp-common-white);
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-market-list ul li {
        font-size: 16px;
    }
}

.tp-market-list ul li:not(:last-of-type) {
    margin-bottom: 20px;
}

.tp-market-list ul li i {
    font-size: 14px;
    margin-right: 5px;
    color: var(--tp-theme-primary);
}

.tp-market-counter .tp-comming-countdown {
    margin-bottom: 0;
}

.tp-market-counter .tp-comming-countdown-inner ul li {
    position: relative;
    width: 270px;
    padding: 45px 0;
    text-align: center;
    background-color: rgb(7, 43, 100);
}

.tp-market-counter .tp-comming-countdown-inner ul li span {
    font-size: 80px;
    font-weight: 700;
    color: var(--tp-common-white);
}

.tp-market-counter .tp-comming-countdown-inner ul li:not(:last-of-type) {
    margin-right: 25px;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
    .tp-market-counter .tp-comming-countdown-inner ul li:not(:last-of-type) {
        margin-bottom: 20px;
    }
}

.tp-market-counter .tp-comming-countdown-inner ul li .top {
    position: absolute;
    top: 10px;
    right: 10px;
    opacity: 0;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-market-counter .tp-comming-countdown-inner ul li .bottom {
    position: absolute;
    bottom: 10px;
    left: 10px;
    opacity: 0;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-market-counter .tp-comming-countdown-inner ul li:hover .top {
    top: 20px;
    right: 20px;
    opacity: 1;
}

.tp-market-counter .tp-comming-countdown-inner ul li:hover .bottom {
    bottom: 20px;
    left: 20px;
    opacity: 1;
}

.tp-market-btn .tp-btn:hover {
    color: var(--tp-theme-primary);
    background: var(--tp-common-white);
}

/*----------------------------------------*/
/*  8.6 Project Css
/*----------------------------------------*/
.tp-project-text {
    position: absolute;
    top: 70px;
    left: 30px;
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-project-text {
        left: 0;
    }
}

.tp-project-bottom-color::before {
    position: absolute;
    content: "";
    height: 240px;
    width: 100%;
    bottom: 0;
    left: 0;
    background: #fff;
    z-index: -1;
}

.tp-project-item {
    z-index: 1;
}

.tp-project-item:hover .tp-project-item-content {
    bottom: 0;
    opacity: 1;
    visibility: visible;
}

.tp-project-item:hover .tp-project-thumb img {
    transform: scale(1.1);
}

.tp-project-item-content {
    position: absolute;
    bottom: -30px;
    left: 0;
    right: 0;
    opacity: 0;
    visibility: hidden;
    padding: 30px 40px;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    background: var(--tp-theme-secondary);
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-project-item-content {
        padding: 30px;
    }
}

.tp-project-item-content span {
    display: inline-block;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 7px;
    color: var(--tp-common-white);
}

.tp-project-item-title {
    font-size: 22px;
    font-weight: 700;
    color: var(--tp-common-white);
    letter-spacing: 0.02px;
    margin: 0;
}

.tp-project-item-title:hover {
    color: var(--tp-theme-primary);
}

.tp-project-item-btn {
    position: absolute;
    right: 40px;
    top: -30px;
}

.tp-project-item-btn:hover span {
    color: var(--tp-theme-primary);
    background: var(--tp-common-white);
}

.tp-project-item-btn span {
    display: inline-block;
    font-size: 20px;
    width: 48px;
    height: 48px;
    line-height: 46px;
    text-align: center;
    color: var(--tp-common-white);
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    background: var(--tp-theme-primary);
}

.tp-project-thumb img {
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-project-thumb img {
        width: 100%;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-project-title-wrapper {
        margin-bottom: 30px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-project-tab-button {
        margin-bottom: 20px;
    }
}

.tp-project-tab-button button {
    position: relative;
    border-radius: 0;
    font-size: 16px;
    font-weight: 600;
    padding: 5px 14px;
    margin-bottom: 10px;
    z-index: 1;
    color: var(--tp-theme-secondary);
    background-color: rgb(248, 248, 248);
}

.tp-project-tab-button button:not(:last-of-type) {
    margin-right: 10px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-project-tab-button button {
        margin-bottom: 20px;
    }
}

.tp-project-tab-button button::after {
    position: absolute;
    content: "";
    width: 100%;
    height: 0%;
    background-color: var(--tp-theme-primary);
    left: 0;
    bottom: 0;
}

.tp-project-tab-button button span {
    position: relative;
    z-index: 1;
}

.tp-project-tab-button button.active {
    color: var(--tp-common-white);
}

.tp-project-tab-button button.active::after {
    height: 100%;
    z-index: 0;
}

.tp-project-2-shape {
    position: absolute;
    bottom: 240px;
    left: 0;
    z-index: 2;
}

.tp-project-6-plr {
    padding-left: 170px;
    padding-right: 170px;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-project-6-plr {
        padding-right: 120px;
        padding-left: 120px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-project-6-plr {
        padding-left: 0;
        padding-right: 0;
    }
}

.tp-project-6-item:hover .tp-project-6-thumb::after {
    height: 100%;
}

.tp-project-6-item:hover .tp-project-6-content {
    bottom: 20px;
    opacity: 1;
    visibility: visible;
}

.tp-project-6-thumb {
    position: relative;
}

.tp-project-6-thumb img {
    width: 100%;
}

.tp-project-6-thumb::after {
    position: absolute;
    content: "";
    bottom: 0;
    left: 0;
    height: 0;
    width: 100%;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    background-image: -moz-linear-gradient(90deg, rgb(1, 13, 20) 0%, rgba(1, 13, 20, 0.5) 32%, rgba(1, 13, 20, 0.04) 95%, rgba(1, 13, 20, 0) 100%);
    background-image: -webkit-linear-gradient(90deg, rgb(1, 13, 20) 0%, rgba(1, 13, 20, 0.5) 32%, rgba(1, 13, 20, 0.04) 95%, rgba(1, 13, 20, 0) 100%);
    background-image: -ms-linear-gradient(90deg, rgb(1, 13, 20) 0%, rgba(1, 13, 20, 0.5) 32%, rgba(1, 13, 20, 0.04) 95%, rgba(1, 13, 20, 0) 100%);
}

.tp-project-6-content {
    position: absolute;
    bottom: 0;
    left: 40px;
    right: 40px;
    opacity: 0;
    visibility: hidden;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-project-6-title {
    font-size: 22px;
    font-weight: 700;
    color: var(--tp-common-white);
}

.tp-project-6-title a:hover {
    color: var(--tp-theme-4);
}

.tp-project-6-title-wrap span {
    font-size: 16px;
    font-weight: 600;
    color: var(--tp-common-white);
}

.tp-project-6-btn {
    font-size: 30px;
    height: 48px;
    width: 48px;
    line-height: 38px;
    display: block;
    text-align: center;
    color: var(--tp-common-white);
    background: var(--tp-theme-4);
}

.tp-project-6-btn:hover {
    color: var(--tp-theme-4);
    background: var(--tp-common-white);
}

/*----------------------------------------*/
/*  9.0 Testimonial Css
/*----------------------------------------*/
.tp-testimonial-bg {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    z-index: -1;
}

.tp-testimonial-bg::before {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    content: "";
    opacity: 0.95;
    background: #f8f8f8;
}

.tp-testimonial-item {
    padding: 0px 30px 40px;
    border: 1px solid rgb(230, 230, 230);
    background-color: rgb(255, 255, 255);
    box-shadow: 0px 4px 60px 0px rgba(35, 35, 49, 0.08);
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-testimonial-item {
        padding: 0px 15px 40px;
    }
}

.tp-testimonial-item:hover {
    border: 1px solid var(--tp-theme-primary);
}

.tp-testimonial-item::before {
    position: absolute;
    bottom: -20px;
    left: 50px;
    content: "";
    height: 30px;
    width: 50px;
    background: rgb(255, 255, 255);
    clip-path: polygon(100% 0, 0 0, 0 100%);
    box-shadow: 0px 4px 60px 0px rgba(35, 35, 49, 0.08);
    z-index: 2;
}

.tp-testimonial-shape {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 0;
}

.tp-testimonial-shape img {
    filter: saturate(0);
}

.tp-testimonial-thumb {
    margin-top: -50px;
    margin-bottom: 40px;
}

.tp-testimonial-thumb img {
    border-radius: 50%;
}

.tp-testimonial-content span {
    display: inline-block;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 30px;
    color: var(--tp-theme-primary);
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-testimonial-content span {
        margin-bottom: 15px;
    }
}

.tp-testimonial-content p {
    color: #565969;
    margin-bottom: 22px;
}

.tp-testimonial-title {
    font-size: 20px;
    font-weight: 700;
}

.tp-testimonial-rating i {
    color: var(--tp-theme-primary);
}

.tp-testimonial-rating.pink {
    margin-bottom: 40px;
}

.tp-testimonial-rating.pink i {
    color: var(--tp-theme-4);
}

.tp-testimonial-wrapper.contact {
    position: relative;
}

.tp-testimonial-wrapper.contact .tp-section-title {
    color: var(--tp-common-white);
}

.tp-testimonial-wrapper.contact .tp-section-title-pre-2 {
    color: var(--tp-common-white);
}

.tp-testimonial-active .swiper-slide {
    padding-top: 48px;
    padding-bottom: 50px;
}

.tp-testimonial-3-bg {
    background: #f8f8f8;
}

.tp-testimonial-3-content {
    padding: 40px;
    margin-bottom: 30px;
    background-color: rgb(255, 255, 255);
    box-shadow: 0px 6px 60px 0px rgba(30, 22, 22, 0.06);
}

.tp-testimonial-3-content::before {
    position: absolute;
    content: "";
    bottom: -34px;
    left: 25px;
    height: 35px;
    width: 35px;
    background: var(--tp-common-white);
    clip-path: polygon(100% 0, 0 0, 100% 100%);
}

.tp-testimonial-3-thumb {
    margin-right: 20px;
}

.tp-testimonial-3-thumb img {
    border-radius: 50%;
    width: 75px;
    height: 75px;
    object-fit: cover;
}

.tp-testimonial-3-rating {
    margin-bottom: 25px;
}

.tp-testimonial-3-rating i {
    color: var(--tp-theme-primary);
}

.tp-testimonial-3-user span {
    display: inline-block;
    font-size: 16px;
    font-weight: 600;
    color: #565969;
}

.tp-testimonial-3-title {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 4px;
}

.tp-testimonial-5-color {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    background-color: rgb(10, 18, 41);
    z-index: -1;
}

.tp-testimonial-5-shape .shape-1 {
    position: absolute;
    top: 0;
    left: 0;
}

.tp-testimonial-5-shape .shape-2 {
    position: absolute;
    bottom: 0;
    right: 0;
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-testimonial-5-shape .shape-2 {
        width: 100%;
    }
}

.tp-testimonial-5-thumb {
    margin-bottom: 30px;
}

.tp-testimonial-5-thumb img {
    border-radius: 50%;
    border: 1px solid var(--tp-theme-primary);
}

.tp-testimonial-5-content p {
    font-size: 28px;
    font-weight: 600;
    line-height: 38px;
    margin-bottom: 40px;
    letter-spacing: -1.6px;
    color: var(--tp-common-white);
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-testimonial-5-content p br {
        display: none;
    }
}

.tp-testimonial-5-dec span {
    font-size: 24px;
    font-weight: 700;
    color: var(--tp-theme-primary);
}

.tp-testimonial-5-dec p {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 0;
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-testimonial-5-nav {
        display: none;
    }
}

.tp-testimonial-5-nav button {
    position: absolute;
    top: 50%;
    left: 0px;
    border-radius: 50%;
    font-size: 24px;
    transform: translateY(-50%);
    color: #3f4964;
    z-index: 2;
}

.tp-testimonial-5-nav button:hover {
    color: var(--tp-theme-primary);
}

.tp-testimonial-5-nav button.tp-testimonial-5-next-1 {
    right: 0px;
    left: auto;
}

.tp-testimonial-6-border {
    border-bottom: 1px solid rgb(232, 232, 232);
}

.tp-testimonial-6-item {
    padding: 40px;
    background-color: rgb(255, 255, 255);
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-testimonial-6-item:hover {
    background-color: rgb(255, 255, 255);
    box-shadow: 0px 0px 60px 0px rgba(30, 22, 22, 0.06);
}

.tp-testimonial-6-item p {
    font-size: 16px;
    color: #565969;
    line-height: 24px;
    margin-bottom: 40px;
}

.tp-testimonial-6-thumb {
    margin-right: 20px;
}

.tp-testimonial-6-thumb img {
    height: 80px;
    width: 80px;
}

.tp-testimonial-6-decs span {
    font-size: 16px;
    color: #767676;
    font-weight: 500;
}

.tp-testimonial-6-active .swiper-wrapper .swiper-slide.swiper-slide-active .tp-testimonial-6-item {
    background-color: rgb(255, 255, 255);
    box-shadow: 0px 0px 60px 0px rgba(30, 22, 22, 0.06);
}

/*----------------------------------------*/
/*  8.7 Service Css
/*----------------------------------------*/
.tp-service-3-item {
    padding: 30px;
    background-color: rgb(248, 248, 248);
}

.tp-service-3-item:hover .tp-service-3-btn a {
    color: var(--tp-common-white);
    background: var(--tp-theme-primary);
}

.tp-service-3-item:hover::after {
    opacity: 1;
    width: 135px;
}

.tp-service-3-item::after {
    position: absolute;
    content: "";
    height: 215px;
    width: 100px;
    top: 0;
    right: 0px;
    opacity: 0;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    clip-path: polygon(50% 0%, 100% 100%, 100% 0);
    background: rgba(0, 35, 90, 0.1);
}

.tp-service-3-icon {
    margin-bottom: 25px;
}

.tp-service-3-icon span {
    font-size: 55px;
    line-height: normal;
    color: var(--tp-theme-primary);
}
.flex-grow-0-5 {
    flex-grow: 0.5;
}

.tp-service-3-content p {
    color: #565969;
    margin-bottom: 30px;
}

.tp-service-3-title {
    font-size: 22px;
    font-weight: 700;
    margin-bottom: 15px;
    color: var(--tp-theme-secondary);
}

.tp-service-3-title:hover {
    color: var(--tp-theme-primary);
}

.tp-service-3-btn {
    position: absolute;
    bottom: -20px;
    left: 30px;
}

.tp-service-3-btn a {
    display: inline-block;
    height: 48px;
    width: 48px;
    line-height: 48px;
    text-align: center;
    font-size: 20px;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    color: var(--tp-theme-primary);
    background: var(--tp-common-white);
}

.tp-service-3-btn a i {
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    transform: translateY(0px);
}

.tp-service-3-btn a:hover i {
    animation: tfLeftToRight 0.5s forwards;
}

.tp-service-5-box {
    padding-left: 160px;
    padding-right: 160px;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-service-5-box {
        padding-right: 120px;
        padding-left: 120px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-service-5-box {
        padding-left: 0;
        padding-right: 0;
    }
}

.tp-service-5-shape-bg {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 0;
}

.tp-service-5-item:hover .tp-service-5-thumb img {
    transform: scale(1.2);
}

.tp-service-5-item:hover .tp-service-5-content {
    padding: 40px 40px 35px 40px;
    bottom: 0;
    background: var(--tp-theme-primary);
}

.tp-service-5-item:hover .tp-service-5-shape {
    opacity: 1;
}

.tp-service-5-item:hover .tp-service-5-icon span {
    color: var(--tp-common-white);
}

.tp-service-5-item:hover .tp-service-5-title {
    color: var(--tp-common-white);
}

.tp-service-5-item:hover .tp-service-5-btn {
    opacity: 1;
}

.tp-service-5-icon span {
    font-size: 50px;
    margin-bottom: 15px;
    display: block;
    color: var(--tp-theme-primary);
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-service-5-thumb img {
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

@media only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-service-5-thumb img {
        width: 100%;
    }
}

.tp-service-5-content {
    position: absolute;
    left: 0;
    right: 40px;
    bottom: -15px;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    padding: 40px 40px 0px 40px;
    background-color: rgb(255, 255, 255);
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-service-5-content {
        bottom: -30px;
    }
}

.tp-service-5-shape {
    position: absolute;
    top: 0;
    right: 0;
    opacity: 0;
    z-index: 0;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-service-5-title {
    font-size: 22px;
    font-weight: 700;
    margin-bottom: 30px;
    transition: 0.2s ease-in;
}

.tp-service-5-btn {
    opacity: 0;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    transform: translateY(0px);
}

.tp-service-5-btn .tp-icon-style {
    color: var(--tp-common-white);
}

.tp-service-details-box ul li {
    list-style: none;
}

.tp-service-details-box ul li:not(:last-of-type) {
    margin-bottom: 10px;
}

.tp-service-details-box ul li a {
    font-size: 16px;
    font-weight: 500;
    padding: 18px 30px;
    display: block;
    color: var(--tp-theme-secondary);
    background: var(--tp-text-2);
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-service-details-box ul li a:hover {
    color: var(--tp-theme-primary);
    background-color: rgb(255, 255, 255);
    box-shadow: 0px 6px 20px 0px rgba(35, 35, 49, 0.08);
}

.tp-service-details-box ul li a span {
    float: right;
}

.tp-service-details-box ul li a.active {
    color: var(--tp-theme-primary);
    background-color: rgb(255, 255, 255);
    box-shadow: 0px 6px 20px 0px rgba(35, 35, 49, 0.08);
}

.tp-service-details-box-title {
    font-size: 30px;
    font-weight: 700;
    margin-bottom: 20px;
    color: var(--tp-theme-secondary);
}

.tp-service-details-price {
    padding: 30px 0px 0px 30px;
    background: var(--tp-text-2);
}

.tp-service-details-month {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 30px;
    color: var(--tp-theme-primary);
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-service-details-thumb img {
        width: 100%;
    }
}

.tp-service-details-wrapper p {
    margin-bottom: 50px;
}

.tp-service-details-title {
    font-size: 45px;
    font-weight: 600;
    margin-bottom: 20px;
    color: var(--tp-theme-secondary);
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-service-details-title {
        font-size: 36px;
    }
}

.tp-service-details-title-2 {
    font-size: 24px;
    font-weight: 700;
}

.tp-service-details-point span {
    display: inline-block;
    flex: 0 0 auto;
    font-size: 20px;
    height: 48px;
    width: 48px;
    line-height: 48px;
    text-align: center;
    border-radius: 50%;
    color: var(--tp-theme-primary);
    background: var(--tp-text-2);
    margin-right: 30px;
}

.tp-service-details-point-title {
    font-size: 16px;
    font-weight: 400;
    margin-bottom: 0;
    color: var(--tp-theme-secondary);
}

.tp-service-details-point-2 {
    padding: 40px;
    background: var(--tp-theme-secondary);
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-service-details-point-2 {
        padding: 30px;
    }
}

.tp-service-details-point-2 span {
    display: inline-block;
    flex: 0 0 auto;
    font-size: 45px;
    height: 80px;
    width: 80px;
    line-height: 85px;
    text-align: center;
    border-radius: 50%;
    color: var(--tp-theme-primary);
    background: var(--tp-common-white);
    margin-right: 30px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-service-details-point-2 span {
        margin-right: 15px;
    }
}

.tp-service-details-point-2-content h4 {
    font-size: 18px;
    font-weight: 600;
    color: var(--tp-common-white);
}

.tp-service-details-point-2-content p {
    font-size: 16px;
    font-weight: 400;
    margin: 0;
    color: var(--tp-common-white);
}

/*----------------------------------------*/
/*  7.7 Company Css
/*----------------------------------------*/
.tp-company-thumb {
    padding-top: 120px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-company-thumb {
        padding-top: 0;
        padding-bottom: 30px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-company-thumb img {
        width: 100%;
    }
}

.tp-company-thumb .shape-1 {
    position: absolute;
    top: 0;
    left: 150px;
    z-index: 0;
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .tp-company-thumb .shape-1 {
        left: 40px;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-company-thumb .shape-1 {
        left: 20px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-company-thumb .shape-1 {
        display: none;
    }
}

.tp-company-thumb .shape-2 {
    position: absolute;
    top: 100px;
    left: 31%;
    z-index: -2;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-company-thumb .shape-2 {
        display: none;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-company-title {
        margin-bottom: 30px;
    }
}

.tp-company-progress-item {
    margin-bottom: 35px;
}

.tp-company-progress-item span {
    position: absolute;
    top: 0;
    right: 20%;
    color: #565969;
}

.tp-company-progress-item span.pursent-2 {
    right: 30%;
}

.tp-company-progress-item span.pursent-3 {
    right: 10%;
}

.tp-company-progress-item .progress {
    height: 6px;
    border-radius: 0;
    background: #f2f3f5;
}

.tp-company-progress-item .progress-bar {
    background: var(--tp-theme-primary);
}

.tp-company-progress-item .progress.progress-5 {
    height: 2px;
}

.tp-company-progress-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--tp-theme-secondary);
}

.tp-company-wrapper {
    padding-left: 60px;
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .tp-company-wrapper {
        padding-left: 40px;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-company-wrapper {
        padding-left: 0;
    }
}

.tp-company-5-shape {
    position: absolute;
    top: 0;
    left: 5%;
}

@media only screen and (min-width: 1701px) and (max-width: 1800px), only screen and (min-width: 1600px) and (max-width: 1700px), only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-company-5-shape {
        display: none;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-company-5-thumb {
        padding-left: 50px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-company-5-thumb {
        padding-left: 0;
    }
}

.tp-company-5-thumb .shape-2 {
    position: absolute;
    bottom: -32%;
    left: 50px;
    z-index: 2;
    animation: updown-two 2.6s linear 0s infinite alternate;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-company-5-thumb .shape-2 {
        left: 0;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-company-5-thumb .shape-2 {
        position: static;
    }
}

@media (max-width: 575px) {
    .tp-company-5-thumb .shape-2 {
        width: 100%;
    }
}

.tp-company-5-thumb-sm.shape-1 {
    position: absolute;
    top: 170px;
    left: 0;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-company-5-thumb-sm.shape-1 {
        top: 110px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-company-5-thumb-sm.shape-1 {
        display: none;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-company-5-thumb-box {
        margin-bottom: 30px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-company-5-wrapper {
        padding-left: 0;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-company-6-wrapper {
        padding-right: 0;
    }
}

.tp-company-6-wrapper b {
    font-size: 20px;
    font-weight: 600;
    color: var(--tp-heading-2);
    margin-bottom: 45px;
    display: inline-block;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-company-6-title {
        margin-bottom: 30px;
    }
}

.tp-company-6-title p {
    color: #767676;
}

.tp-company-6-icon {
    padding: 30px 40px 18px 28px;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-company-6-icon {
        padding: 20px;
    }
}

.tp-company-6-icon:not(:last-of-type) {
    margin-right: 30px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-company-6-icon:not(:last-of-type) {
        margin-right: 10px;
    }
}

.tp-company-6-icon:hover {
    background-color: rgb(255, 255, 255);
    box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.04);
}

.tp-company-6-icon i {
    font-size: 60px;
    color: var(--tp-theme-4);
    margin-right: 25px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-company-6-icon i {
        margin-right: 10px;
    }
}

.tp-company-6-icon-box {
    margin-bottom: 60px;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 768px) and (max-width: 991px) {
    .tp-company-6-icon-box {
        margin-bottom: 30px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-company-6-icon-box {
        flex-wrap: wrap;
        margin-bottom: 30px;
    }
}

.tp-company-6-icon-title {
    font-size: 22px;
    font-weight: 700;
    margin: 0;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-company-6-icon-title {
        font-size: 20px;
    }
}

.tp-company-6-thumb {
    padding-top: 65px;
    margin-right: -100px;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px), lg, md, only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-company-6-thumb {
        margin-right: 0;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-company-6-thumb {
        padding-top: 30px;
    }
}

.tp-company-6-thumb .shape-1 {
    position: absolute;
    top: 10px;
    left: 0;
    z-index: 2;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .tp-company-6-thumb .shape-1 {
        left: -30px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .tp-company-6-thumb .shape-1 {
        left: 205px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-company-6-thumb .shape-1 {
        display: none;
    }
}

.tp-company-6-thumb .shape-2 {
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 2;
}

@media (max-width: 575px) {
    .tp-company-6-thumb .shape-2 {
        width: 100%;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-company-6-btn {
        margin-bottom: 20px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-company-6-btn-wrapper {
        margin-bottom: 30px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-company-6-btn-wrapper {
        flex-wrap: wrap;
    }
}

.tp-map-content {
    width: 100%;
    height: 540px;
}

.tp-map-content iframe {
    width: 100%;
    height: 100%;
    filter: saturate(0);
}

/*----------------------------------------*/
/*  7.9 Cta Css
/*----------------------------------------*/
.tp-cta-area {
    overflow: hidden;
}

.tp-cta-shape {
    position: absolute;
    top: 0;
    left: 40%;
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-cta-shape {
        display: none;
    }
}

.tp-cta-title {
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 0;
    color: var(--tp-common-white);
}

@media only screen and (min-width: 992px) and (max-width: 1199px), (max-width: 575px) {
    .tp-cta-title {
        font-size: 32px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-cta-title {
        margin-bottom: 20px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-cta-title br {
        display: none;
    }
}

.tp-cta-box {
    padding-left: 70px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-cta-box {
        padding-left: 0;
    }
}

.tp-cta-form {
    position: relative;
}

.tp-cta-form input {
    font-size: 16px;
    font-weight: 700;
    height: 78px;
    padding-right: 250px;
    background: transparent;
    color: var(--tp-common-white);
}

.tp-cta-form input::placeholder {
    font-size: 16px;
    font-weight: 700;
    color: var(--tp-common-white);
}

.tp-cta-form input:focus {
    border: 1px solid var(--tp-common-white);
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-cta-form input {
        padding-right: 210px;
    }
}

@media (max-width: 575px) {
    .tp-cta-form input {
        padding-right: 30px;
        height: 70px;
    }
}

.tp-cta-form button {
    position: absolute;
    top: 50%;
    right: 10px;
    font-size: 14px;
    font-weight: 700;
    padding: 18px 45px;
    transform: translateY(-50%);
    color: var(--tp-common-black);
    background: var(--tp-common-white);
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-cta-form button {
        padding: 18px 25px;
    }
}

@media (max-width: 575px) {
    .tp-cta-form button {
        position: static;
        transform: translateY(20px);
    }
}

/*----------------------------------------*/
/*  8.2 History Css
/*----------------------------------------*/
.tp-history-title {
    font-size: 30px;
    font-weight: 700;
    color: var(--tp-theme-secondary);
}

.tp-history-title.orange {
    color: var(--tp-theme-primary);
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-history-thumb {
        margin-bottom: 30px;
    }
}

.tp-history-thumb img {
    border-radius: 50%;
    border: 2px solid rgb(236, 240, 245);
    padding: 12px;
}

@media (max-width: 575px) {
    .tp-history-text {
        margin-bottom: 20px;
    }
}

.tp-history-year {
    font-size: 120px;
    font-weight: 700;
    color: #ecf0f5;
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-history-item-box {
        padding-bottom: 40px;
    }
}

.tp-history-bottom-right::before {
    position: absolute;
    content: "";
    bottom: -70px;
    right: 55px;
    width: 2px;
    height: 150px;
    background: var(--tp-theme-secondary);
    transform: rotate(-35deg);
    z-index: -1;
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-history-bottom-right::before {
        content: none;
    }
}

.tp-history-bottom-left::after {
    position: absolute;
    content: "";
    bottom: -70px;
    left: 55px;
    width: 2px;
    height: 150px;
    background: var(--tp-theme-secondary);
    transform: rotate(35deg);
    z-index: -1;
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-history-bottom-left::after {
        content: none;
    }
}

.tp-history-top-right::before {
    position: absolute;
    content: "";
    top: -78px;
    right: 55px;
    width: 2px;
    height: 150px;
    background: var(--tp-theme-secondary);
    transform: rotate(35deg);
    z-index: -1;
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-history-top-right::before {
        content: none;
    }
}

.tp-history-top-left::after {
    position: absolute;
    content: "";
    top: -78px;
    left: 55px;
    width: 2px;
    height: 150px;
    background: var(--tp-theme-secondary);
    transform: rotate(-35deg);
    z-index: -1;
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-history-top-left::after {
        content: none;
    }
}

/*----------------------------------------*/
/*  8.8 Shop Css
/*----------------------------------------*/
.tp-shop-item:hover .tp-shop-btn {
    opacity: 1;
    visibility: visible;
    bottom: 0;
}

.tp-shop-thumb {
    overflow: hidden;
}

.tp-shop-thumb span {
    position: absolute;
    top: 20px;
    right: 20px;
}

.tp-shop-thumb span:hover i {
    color: var(--tp-theme-primary);
}

.tp-shop-thumb span i {
    font-size: 18px;
    font-weight: 600;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    color: var(--tp-theme-secondary);
}

@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 575px) {
    .tp-shop-thumb img {
        width: 100%;
    }
}

.tp-shop-title {
    font-size: 20px;
    font-weight: 600;
    color: var(--tp-theme-secondary);
}

.tp-shop-btn {
    position: absolute;
    bottom: -40px;
    left: 0;
    right: 0;
    opacity: 0;
    visibility: hidden;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-shop-btn a {
    position: relative;
    display: block;
    font-size: 16px;
    font-weight: 700;
    padding: 12px 0;
    color: var(--tp-common-white);
    background: var(--tp-theme-primary);
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.tp-shop-btn a:hover {
    text-decoration: underline;
}

.tp-shop-content {
    padding: 18px 0;
}

.tp-shop-name {
    font-size: 20px;
    font-weight: 600;
}

.tp-shop-name a:hover {
    color: var(--tp-theme-primary);
}

.tp-shop-new-price {
    font-size: 16px;
    font-weight: 500;
    color: var(--tp-theme-primary);
}

.tp-shop-new-price.old {
    text-decoration-line: line-through;
    color: #565969;
    margin-right: 5px;
}

.tp-shop-pagination ul li {
    list-style: none;
    display: inline-block;
    margin-right: 15px;
}

.tp-shop-pagination ul li a {
    position: relative;
    font-size: 14px;
    font-weight: 700;
    display: inline-block;
    width: 38px;
    height: 38px;
    line-height: 37px;
    text-align: center;
    border-radius: 50%;
    color: var(--tp-theme-secondary);
    background: var(--tp-text-2);
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    z-index: 1;
}

.tp-shop-pagination ul li a.active {
    color: var(--tp-common-white);
    background: var(--tp-theme-secondary);
}

.tp-shop-pagination ul li a:hover {
    color: var(--tp-common-white);
    background: var(--tp-theme-secondary);
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .tp-shop-show {
        margin-bottom: 30px;
    }
}

@media (max-width: 575px) {
    .tp-shop-show {
        flex-wrap: wrap;
    }
}

.tp-shop-show-title {
    font-size: 16px;
    font-weight: 400;
    color: #565969;
}

@media (max-width: 575px) {
    .tp-shop-from {
        margin-bottom: 30px;
    }
}

.tp-shop-from .nice-select {
    font-size: 16px;
    font-weight: 400;
    color: #565969;
    border-radius: 0;
    padding-left: 40px;
    padding-right: 40px;
    border: 1px solid #565969;
    background: var(--tp-common-white);
}

.tp-shop-from .nice-select.open .list {
    width: 100%;
}

.tp-shop-from .nice-select::after {
    height: 9px;
    width: 9px;
    right: 15px;
    color: #565969;
    margin-top: -5px;
}

.product__item:hover .product__thumb img {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    -o-transform: scale(1.1);
    transform: scale(1.1);
}

.product__item:hover .product__add {
    opacity: 1;
    visibility: visible;
    bottom: 0;
}

.product__item:hover .product__action a, .product__item:hover .product__action button {
    transform: translateX(0);
    visibility: visible;
    opacity: 1;
}

.product__item:hover .product__action a:nth-child(1), .product__item:hover .product__action button:nth-child(1) {
    transition-delay: 0s;
}

.product__item:hover .product__action a:nth-child(2), .product__item:hover .product__action button:nth-child(2) {
    transition-delay: 0.1s;
}

.product__item:hover .product__action a:nth-child(3), .product__item:hover .product__action button:nth-child(3) {
    transition-delay: 0.12s;
}

.product__item:hover .product__action a:nth-child(4), .product__item:hover .product__action button:nth-child(4) {
    transition-delay: 0.14s;
}

.product-related-title {
    font-size: 40px;
    font-weight: 600;
    margin-bottom: 30px;
}

.product__content {
    padding-top: 14px;
}

.product__rating span {
    font-size: 11px;
    display: inline-block;
}

.product__rating span:not(:last-child) {
    margin-right: 3px;
}

.product__rating-2 {
    margin-bottom: 2px;
}

.product__rating-2 span {
    font-size: 12px;
}

.product__rating-2 span i {
    color: var(--tp-theme-primary);
}

.product__title {
    font-weight: 400;
    font-size: 14px;
}

.product__title a:hover {
    color: var(--tp-theme-primary);
}

.product__ammount {
    font-weight: 500;
    font-size: 15px;
    color: #565969;
}

.product__badge {
    position: absolute;
    left: 0;
    top: 20px;
}

.product__badge-item {
    display: inline-block;
    font-size: 13px;
    line-height: 1;
    color: var(--tp-common-white);
    background-color: var(--tp-common-black);
    padding: 4px 10px 4px;
    margin-bottom: 5px;
    text-transform: capitalize;
}

.product__badge-item.has-new {
    background-color: var(--tp-theme-primary);
}

.product__action {
    position: absolute;
    top: 40px;
    right: 10px;
}

.product__action a, .product__action button {
    display: inline-block;
    width: 38px;
    height: 38px;
    line-height: 38px;
    background-color: #FFFFFF;
    box-shadow: 0px 1px 3px rgba(3, 4, 28, 0.12);
    text-align: center;
    position: relative;
    margin-bottom: 6px;
    transform: translateX(100%);
    opacity: 0;
    visibility: hidden;
}

.product__action a svg, .product__action button svg {
    -webkit-transform: translateY(-2px);
    -moz-transform: translateY(-2px);
    -ms-transform: translateY(-2px);
    -o-transform: translateY(-2px);
    transform: translateY(-2px);
}

.product__action a:nth-child(1), .product__action button:nth-child(1) {
    transition-delay: 0.14s;
}

.product__action a:nth-child(2), .product__action button:nth-child(2) {
    transition-delay: 0.12s;
}

.product__action a:nth-child(3), .product__action button:nth-child(3) {
    transition-delay: 0.1s;
}

.product__action a:nth-child(4), .product__action button:nth-child(4) {
    transition-delay: 0s;
}

.product__action a:hover, .product__action button:hover {
    background-color: var(--tp-theme-primary);
    color: var(--tp-common-white);
}

.product__add {
    position: absolute;
    bottom: -60px;
    left: 0;
    right: 0;
    visibility: hidden;
    opacity: 0;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .product__details-area {
        padding-bottom: 60px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .product__details-thumb-tab {
        margin-right: 0;
    }
}

.product__details-thumb-content {
    margin-bottom: 14px;
}

.product__details-thumb-nav .nav-link {
    width: 110px;
    height: 110px;
    position: relative;
    margin-bottom: 10px;
}

@media (max-width: 575px) {
    .product__details-thumb-nav .nav-link {
        margin-right: 12px;
    }
}

.product__details-thumb-nav .nav-link::after {
    position: absolute;
    content: "";
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: transparent;
    border: 1px solid var(--tp-theme-primary);
    opacity: 0;
    visibility: hidden;
}

.product__details-thumb-nav .nav-link img {
    width: 110px;
    height: 110px;
    position: relative;
}

.product__details-thumb-nav .nav-link.active::after {
    visibility: visible;
    opacity: 1;
}

.product__details-stock {
    margin-bottom: 13px;
}

.product__details-stock span {
    font-weight: 500;
    font-size: 14px;
    line-height: 1;
    color: var(--tp-theme-secondary);
    border: 1px solid rgba(33, 68, 216, 0.1);
    padding: 5px 18px;
}

.product__details-title {
    font-weight: 800;
    font-size: 36px;
    margin-bottom: 5px;
}

.product__details-rating {
    margin-bottom: 13px;
}

.product__details-rating .product__rating {
    margin-right: 10px;
}

.product__details-rating-count {
    position: relative;
    margin-bottom: 2px;
    padding-left: 9px;
}

.product__details-rating-count::after {
    position: absolute;
    content: "";
    left: 0;
    top: 3px;
    width: 1px;
    height: 20px;
    background-color: #DADCE0;
}

.product__details-rating-count span {
    font-size: 14px;
}

@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .product__details-wrapper {
        margin-top: 40px;
    }
}

.product__details-wrapper p {
    font-size: 16px;
    line-height: 1.6;
    margin-bottom: 23px;
}

.product__details-price {
    margin-bottom: 35px;
}

.product__details-price span {
    line-height: 1;
}

.product__details-ammount {
    font-weight: 600;
    font-size: 24px;
    color: var(--tp-common-black);
}

.product__details-ammount.old-ammount {
    font-weight: 400;
    font-size: 20px;
    text-decoration-line: line-through;
    color: #565969;
}

.product__details-ammount.new-ammount {
    font-weight: 800;
    font-size: 30px;
    color: var(--tp-heading-primary);
    margin-left: 1px;
}

.product__details-offer {
    margin-left: 2px;
    font-weight: 700;
    font-size: 13px;
    color: var(--tp-common-white);
    background-color: var(--tp-theme-primary);
    padding: 3px 9px;
    display: inline-block;
    -webkit-transform: translateY(-4px);
    -moz-transform: translateY(-4px);
    -ms-transform: translateY(-4px);
    -o-transform: translateY(-4px);
    transform: translateY(-4px);
}

.product__details-quantity {
    margin-bottom: 40px;
}

.product__details-action {
    padding-bottom: 34px;
    border-bottom: 1px solid #DADCE0;
    margin-bottom: 32px;
}

.product__details-action a,
.product__details-action button {
    margin-bottom: 6px;
}

.product__details-action button:not(:last-child),
.product__details-action a:not(:last-child) {
    margin-right: 6px;
}

.product__details-action button.product-action-btn,
.product__details-action a.product-action-btn {
    width: 50px;
    min-height: 50px;
    line-height: 48px;
    text-align: center;
    font-size: 18px;
    color: var(--tp-common-black);
    border: 1px solid #DADCE0;
    padding: 12px 45px;
}

.product__details-action button.product-action-btn .product-action-tooltip,
.product__details-action a.product-action-btn .product-action-tooltip {
    top: auto;
    bottom: 90%;
    left: auto;
    right: auto;
    margin: auto;
    margin-bottom: 10px;
    -webkit-transform: translate(-57%, 0%);
    -moz-transform: translate(-57%, 0%);
    -ms-transform: translate(-57%, 0%);
    -o-transform: translate(-57%, 0%);
    transform: translate(-57%, 0%);
}

.product__details-action button.product-action-btn .product-action-tooltip::after,
.product__details-action a.product-action-btn .product-action-tooltip::after {
    top: 100%;
    right: 50%;
    left: auto;
    -webkit-transform: translate(50%, -50%) rotate(45deg);
    -moz-transform: translate(50%, -50%) rotate(45deg);
    -ms-transform: translate(50%, -50%) rotate(45deg);
    -o-transform: translate(50%, -50%) rotate(45deg);
    transform: translate(50%, -50%) rotate(45deg);
}

.product__details-action a.product-action-btn:hover,
.product__details-action button.product-action-btn:hover {
    color: var(--tp-common-white);
    background-color: var(--tp-theme-primary);
    border-color: var(--tp-theme-primary);
}

.product__details-action button.product-action-btn:hover .product-action-tooltip,
.product__details-action a.product-action-btn:hover .product-action-tooltip {
    bottom: 100%;
    top: auto;
    right: auto;
}

.product__details-more p {
    display: inline-block;
    margin-bottom: 5px;
    font-weight: 600;
    font-size: 15px;
    line-height: 1;
    color: var(--tp-common-black);
}

.product__details-more span {
    font-size: 15px;
    line-height: 1;
    color: #565969;
}

.product__details-more span a:hover {
    color: var(--tp-theme-primary);
}

.product__details-categories {
    margin-bottom: 15px;
}

.product__details-tags {
    margin-bottom: 15px;
}

.product__details-tags span {
    display: inline-block;
    margin-bottom: 5px;
    font-weight: 600;
    font-size: 15px;
    line-height: 1;
    color: var(--tp-common-black);
    margin-right: 3px;
}

.product__details-tags a {
    font-size: 14px;
    line-height: 1;
    color: #565969;
    border: 1px solid #DADCE0;
    padding: 8px 12px;
    margin-right: 5px;
    margin-bottom: 6px;
    display: inline-block;
}

.product__details-tags a:hover {
    background-color: var(--tp-theme-primary);
    border-color: var(--tp-theme-primary);
    color: var(--tp-common-white);
}

.product__details-share span {
    display: inline-block;
    margin-bottom: 5px;
    font-weight: 600;
    font-size: 15px;
    line-height: 1;
    color: var(--tp-common-black);
    margin-right: 9px;
}

.product__details-share a {
    font-size: 14px;
    line-height: 1;
    color: #565969;
    margin-bottom: 6px;
    margin-right: 10px;
}

.product__details-share a:hover {
    color: var(--tp-theme-primary);
}

.product__details-tab-nav {
    border-bottom: 1px solid #DADCE0;
}

.product__details-tab-nav-inner {
    position: relative;
}

.product__details-tab-nav .nav-link {
    position: relative;
    padding: 11px 14px;
    font-weight: 500;
    font-size: 16px;
    color: #565969;
}

.product__details-tab-nav .nav-link.active, .product__details-tab-nav .nav-link:hover {
    color: var(--tp-common-black);
}

.product__details-tab-nav .nav-link.active::after, .product__details-tab-nav .nav-link:hover::after {
    width: 100%;
    left: 0;
    right: auto;
}

@media (max-width: 575px) {
    .product__details-tab-nav .nav-link::after {
        position: absolute;
        content: "";
        left: auto;
        right: 0;
        bottom: -1px;
        width: 0%;
        height: 2px;
        background-color: var(--tp-theme-3);
        -webkit-transition: all 0.3s 0s ease-out;
        -moz-transition: all 0.3s 0s ease-out;
        -ms-transition: all 0.3s 0s ease-out;
        -o-transition: all 0.3s 0s ease-out;
        transition: all 0.3s 0s ease-out;
    }
}

.product__details-tab-nav .tp-tab-menu {
    position: relative;
}

.product__details-tab-nav .tp-tab-line {
    position: absolute;
    content: "";
    left: 0;
    bottom: -1px;
    width: 38%;
    height: 2px;
    background-color: var(--tp-theme-primary);
}

@media (max-width: 575px) {
    .product__details-description-content {
        padding-right: 0;
    }
}

.product__details-description-content .product-desc-title {
    font-weight: 500;
    font-size: 34px;
    color: var(--tp-heading-primary);
    margin-bottom: 10px;
}

@media (max-width: 575px) {
    .product__details-description-content .product-desc-title {
        font-size: 25px;
    }
}

.product__details-description-content p {
    margin-bottom: 25px;
}

.product__details-description .product-desc-feature-thumb {
    margin-bottom: 30px;
}

.product__details-description .product-desc-feature-content p {
    line-height: 1.56;
    padding-right: 75px;
}

@media (max-width: 575px) {
    .product__details-description .product-desc-feature-content p {
        padding-right: 0;
    }
}

.product__details-additional {
    margin-top: 67px;
    background-color: var(--tp-common-white);
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .product__details-additional {
        overflow-x: scroll;
    }
}

.product__details-additional-inner {
    padding: 45px 70px 45px;
    border: 1px solid #E9E9F0;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .product__details-additional-inner {
        padding: 15px 40px 15px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .product__details-additional-inner {
        width: 768px;
    }
}

.product__details-additional table {
    width: 100%;
}

.product__details-additional table tr {
    padding: 16px 0 14px;
    display: block;
}

.product__details-additional table tr:not(:last-child) {
    border-bottom: 1px solid #E9E9F0;
}

.product__details-additional table tr th {
    font-weight: 600;
    font-size: 16px;
    color: var(--tp-common-black);
    width: 28%;
    display: inline-block;
}

.product__details-additional table tr td {
    width: 71%;
    display: inline-block;
    font-size: 15px;
    color: var(--tp-text-body);
}

.product__details-review-inner {
    padding: 62px 70px 70px;
    border: 1px solid #E9E9F0;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .product__details-review-inner {
        padding: 35px 40px 40px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .product__details-review-inner {
        padding: 25px 30px 30px;
    }
}

@media (max-width: 575px) {
    .product__details-review-inner {
        padding: 15px 20px 20px;
    }
}

.product__details-review .product-rating-title {
    font-weight: 600;
    font-size: 24px;
    margin-bottom: 22px;
}

.product__details-review .product-rating-number {
    text-align: center;
    width: 160px;
    height: 125px;
    border: 1px solid #E9E9F0;
    padding-top: 21px;
    padding-bottom: 21px;
}

@media (max-width: 575px) {
    .product__details-review .product-rating-number {
        margin-bottom: 15px;
    }
}

.product__details-review .product-rating-number-title {
    font-weight: 400;
    font-size: 60px;
    line-height: 1;
    color: var(--tp-common-black-solid);
    margin-bottom: 0;
}

.product__details-review .product-rating-star {
    line-height: 1;
}

.product__details-review .product-rating-star span {
    line-height: 1;
    font-size: 12px;
    display: inline-block;
}

.product__details-review .product-rating-star span:not(:last-child) {
    margin-right: 3px;
}

.product__details-review .product-rating-bar {
    width: 100%;
    background-color: #E9E9E9;
    border-radius: 10px;
}

.product__details-review .product-rating-bar-wrapper {
    width: 71.5%;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .product__details-review .product-rating-bar-wrapper {
        width: 70%;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .product__details-review .product-rating-bar-wrapper {
        width: 66%;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .product__details-review .product-rating-bar-wrapper {
        width: 52%;
    }
}

@media (max-width: 575px) {
    .product__details-review .product-rating-bar-wrapper {
        width: 100%;
    }
}

.product__details-review .product-rating-bar .single-progress {
    background-color: var(--tp-theme-primary);
    height: 8px;
    border-radius: 10px;
}

.product__details-review .product-rating-bar-text {
    width: 3.5%;
    line-height: 1;
}

@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
    .product__details-review .product-rating-bar-text {
        width: 7.5%;
    }
}

.product__details-review .product-rating-bar-text span {
    font-size: 14px;
    color: #565969;
}

.product__details-review .product-rating-bar-item:not(:last-child) {
    margin-bottom: 5px;
}

.product__details-review .product-review-item {
    margin-bottom: 30px;
}

.product__details-review .product-review-item:last-child {
    margin-bottom: 0;
}

.product__details-review .product-review-item p {
    font-size: 15px;
    line-height: 1.6;
    color: #565969;
}

.product__details-review .product-review-avater {
    margin-bottom: 11px;
}

.product__details-review .product-review-avater-thumb img {
    width: 34px;
    height: 34px;
    border-radius: 50%;
    margin-right: 10px;
}

.product__details-review .product-review-avater-title {
    font-weight: 500;
    font-size: 16px;
    line-height: 1;
    margin-bottom: 0;
}

.product__details-review .product-review-rating {
    margin-bottom: 5px;
}

.product__details-review .product-review-rating-wrapper {
    margin-right: 10px;
}

.product__details-review .product-review-rating-wrapper span {
    line-height: 1;
    font-size: 12px;
    display: inline-block;
}

.product__details-review .product-review-rating-wrapper span:not(:last-child) {
    margin-right: 3px;
}

.product__details-review .product-review-rating-wrapper span i {
    color: var(--tp-theme-primary);
}

.product__details-review .product-review-rating-date span {
    font-weight: 400;
    font-size: 13px;
    color: #565969;
}

.product__details-review .product-review-rating span i {
    color: var(--tp-theme-primary);
}

.product__details-review .product-review-form p {
    font-weight: 400;
    font-size: 14px;
    color: #565969;
    margin-bottom: 20px;
}

.product__details-review .product-review-form-title {
    font-weight: 600;
    font-size: 24px;
    letter-spacing: -0.02em;
    margin-bottom: 4px;
}

.product__details-review .product-review-form-rating .rate-title {
    font-weight: 500;
    font-size: 15px;
    line-height: 14px;
    letter-spacing: -0.02em;
    color: #565969;
}

.product__details-review .product-review-input {
    margin-bottom: 18px;
    line-height: 1;
}

.product__details-review .product-review-input.is-textarea {
    margin-bottom: 30px;
}

.product__details-review .product-review-input input, .product__details-review .product-review-input textarea {
    height: 60px;
    line-height: 60px;
    background-color: #F7F7F7;
    border: 1.5px solid #F7F7F7;
}

.product__details-review .product-review-input input:focus, .product__details-review .product-review-input textarea:focus {
    border-color: var(--tp-theme-primary);
    background-color: var(--tp-common-white);
}

.product__details-review .product-review-input textarea {
    height: 180px;
    padding-bottom: 20px;
    resize: none;
    line-height: 1.4;
}

.product__details-review .product-review-agree input {
    margin: 0;
    appearance: none;
    -moz-appearance: none;
    display: block;
    width: 18px;
    height: 18px;
    background: transparent;
    border: 2px solid #B2B2B9;
    outline: none;
    flex: 0 0 auto;
    -webkit-transform: translateY(3px);
    -moz-transform: translateY(3px);
    -ms-transform: translateY(3px);
    -o-transform: translateY(3px);
    transform: translateY(3px);
    padding: 0;
}

.product__details-review .product-review-agree input:checked {
    position: relative;
    background-color: var(--tp-theme-primary);
    border-color: transparent;
}

.product__details-review .product-review-agree input:checked::after {
    box-sizing: border-box;
    position: absolute;
    content: "\f00c";
    font-weight: 700;
    font-family: var(--tp-ff-fontawesome);
    font-size: 10px;
    color: var(--tp-common-white);
    top: 46%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}

.product__details-review .product-review-agree input:hover {
    cursor: pointer;
}

.product__details-review .product-review-agree label {
    padding-left: 8px;
    font-size: 14px;
    line-height: 1.71;
    color: #565969;
}

.product__details-review .product-review-agree label a {
    color: var(--tp-common-black);
    font-weight: 600;
    padding-left: 4px;
}

.product__details-review .product-review-agree label a:hover {
    color: var(--tp-theme-primary);
}

.product__details-review .product-review-agree label:hover {
    cursor: pointer;
}

.product__details-review .product-review-btn .tp-btn {
    font-weight: 600;
    font-size: 15px;
    padding: 12px 37px;
}

.product__details-review .product-review-btn .tp-btn:hover {
    background-color: var(--tp-common-black);
}

.product__modal .modal-dialog {
    max-width: 1200px;
    border-radius: 0;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .product__modal .modal-dialog {
        max-width: 1100px;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .product__modal .modal-dialog {
        max-width: 900px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .product__modal .modal-dialog {
        max-width: 700px;
    }
}

.product__modal .modal-content {
    padding: 40px;
    border-radius: 0;
}

@media (max-width: 575px) {
    .product__modal .modal-content {
        padding: 20px;
    }
}

.product__modal .product__details-thumb-nav .nav-link {
    width: 90px;
    height: 90px;
    margin-bottom: 10px;
}

.product__modal .product__details-thumb-nav .nav-link img {
    width: 100%;
    height: 100%;
}

@media (max-width: 575px) {
    .product__modal .product__details-thumb-nav .nav-link {
        width: 70px;
        height: 70px;
    }
}

.product__modal-close {
    position: absolute;
    top: 20px;
    right: 20px;
}

.product__modal-close-btn {
    display: inline-block;
    font-size: 16px;
    height: 44px;
    width: 44px;
    line-height: 44px;
    background-color: transparent;
    color: #565969;
    border-radius: 50%;
}

.product__modal-close-btn:hover {
    color: var(--tp-theme-primary);
    -webkit-transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    -o-transform: rotate(90deg);
    transform: rotate(90deg);
}

.product__modal-close-btn svg {
    -webkit-transform: translateY(-2px);
    -moz-transform: translateY(-2px);
    -ms-transform: translateY(-2px);
    -o-transform: translateY(-2px);
    transform: translateY(-2px);
}

.tp-product-quantity {
    width: 148px;
    position: relative;
}

.tp-cart-plus,
.tp-cart-minus {
    width: 45px;
    height: 44px;
    line-height: 44px;
    display: inline-block;
    text-align: center;
    font-size: 16px;
    color: var(--tp-common-black);
    position: absolute;
    top: 50%;
    left: 0;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    transform: translateY(-50%);
}

.tp-cart-plus::after,
.tp-cart-minus::after {
    position: absolute;
    content: "";
    width: 1px;
    height: 26px;
    top: 50%;
    right: 0;
    background-color: #DADCE0;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    transform: translateY(-50%);
}

.tp-cart-plus svg,
.tp-cart-minus svg {
    -webkit-transform: translateY(-2px);
    -moz-transform: translateY(-2px);
    -ms-transform: translateY(-2px);
    -o-transform: translateY(-2px);
    transform: translateY(-2px);
}

.tp-cart-plus:hover,
.tp-cart-minus:hover {
    cursor: pointer;
    color: var(--tp-theme-primary);
}

.tp-cart-plus.tp-cart-plus,
.tp-cart-minus.tp-cart-plus {
    left: auto;
    right: 0;
}

.tp-cart-plus.tp-cart-plus::after,
.tp-cart-minus.tp-cart-plus::after {
    left: 0;
    right: auto;
}

.tp-cart-input[type=text] {
    width: 100%;
    height: 44px;
    text-align: center;
    font-size: 14px;
    border: 1px solid #DADCE0;
    background-color: var(--tp-common-white);
    padding: 0 45px;
}

[dir=rtl] .tp-cart-input[type=text] {
    text-align: center;
}

.tp-cart-input[type=text]:focus {
    outline: none;
}

.product-action-btn {
    position: relative;
}

.product-action-btn .product-action-tooltip {
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    transform: translateY(-50%);
    right: 90%;
    font-weight: 500;
    font-size: 12px;
    color: var(--tp-common-white);
    background-color: var(--tp-common-black);
    z-index: 1;
    display: inline-block;
    width: max-content;
    line-height: 1;
    padding: 4px 8px;
    margin-right: 8px;
    visibility: hidden;
    opacity: 0;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.product-action-btn .product-action-tooltip::after {
    position: absolute;
    content: "";
    right: 0;
    top: 50%;
    -webkit-transform: translate(50%, -50%) rotate(45deg);
    -moz-transform: translate(50%, -50%) rotate(45deg);
    -ms-transform: translate(50%, -50%) rotate(45deg);
    -o-transform: translate(50%, -50%) rotate(45deg);
    transform: translate(50%, -50%) rotate(45deg);
    height: 8px;
    width: 8px;
    background-color: var(--tp-common-black);
}

.product-action-btn svg {
    -webkit-transform: translateY(-1px);
    -moz-transform: translateY(-1px);
    -ms-transform: translateY(-1px);
    -o-transform: translateY(-1px);
    transform: translateY(-1px);
}

.product-action-btn:hover .product-action-tooltip {
    visibility: visible;
    opacity: 1;
    right: 100%;
}

.product-add-cart-btn {
    font-weight: 600;
    font-size: 15px;
    color: var(--tp-common-white);
    background-color: var(--tp-common-black);
    display: inline-block;
    padding: 7px 25px;
    text-align: center;
}

.product-add-cart-btn svg, .product-add-cart-btn i {
    margin-right: 7px;
}

.product-add-cart-btn svg {
    -webkit-transform: translateY(-2px);
    -moz-transform: translateY(-2px);
    -ms-transform: translateY(-2px);
    -o-transform: translateY(-2px);
    transform: translateY(-2px);
}

.product-add-cart-btn:hover {
    color: var(--tp-common-white);
    background-color: var(--tp-theme-primary);
}

.product-add-cart-btn-2 {
    border: 1px solid #DADCE0;
    color: var(--tp-common-black);
    background-color: var(--tp-common-white);
    font-size: 15px;
    font-weight: 500;
    padding: 6px 33px;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .product-add-cart-btn-2 {
        padding: 6px 21px;
    }
}

@media (max-width: 575px) {
    .product-add-cart-btn-2 {
        width: 100%;
    }
}

.product-add-cart-btn-2:hover {
    background-color: var(--tp-theme-primary);
    border-color: var(--tp-theme-primary);
    color: var(--tp-common-white);
}

.product-add-cart-btn-3 {
    background-color: var(--tp-heading-primary);
    color: var(--tp-common-white);
    padding: 12px 45px;
}

.product-add-cart-btn-3:hover {
    color: var(--tp-common-white);
    background-color: var(--tp-theme-primary);
}

@media (max-width: 575px) {
    .product-add-cart-btn-3 {
        width: 100%;
    }
}

.cartmini__wrapper {
    position: relative;
    min-height: 100%;
    padding-left: 25px;
    padding-right: 25px;
}

.cartmini__top-title {
    padding: 20px 0;
    border-bottom: 1px solid var(--tp-border-primary);
}

.cartmini__top-title h4 {
    font-size: 16px;
    text-transform: capitalize;
    font-weight: 600;
    margin-bottom: 0;
}

.cartmini__close {
    position: absolute;
    top: 17px;
    right: 0;
}

.cartmini__close-btn {
    background: transparent;
    color: var(--tp-common-black);
    font-size: 22px;
}

.cartmini__close-btn:hover {
    -webkit-transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    -o-transform: rotate(90deg);
    transform: rotate(90deg);
}

.cartmini__shipping {
    padding: 15px 0;
    border-bottom: 1px solid var(--tp-border-primary);
}

.cartmini__shipping .progress {
    height: 10px;
    border-radius: 0;
}

.cartmini__shipping .progress-bar {
    background-color: var(--tp-theme-primary);
}

.cartmini__shipping p {
    margin-bottom: 5px;
}

.cartmini__shipping p span {
    color: var(--tp-pink-1);
    font-weight: 600;
}

.cartmini__widget {
    height: calc(100vh - 380px);
    overflow-y: scroll;
    overscroll-behavior-y: contain;
    scrollbar-width: none;
}

.cartmini__widget::-webkit-scrollbar {
    display: none; /* for Chrome, Safari, and Opera */
}

.cartmini__widget-item {
    position: relative;
    display: flex;
    padding: 20px 0;
    border-bottom: 1px solid rgba(129, 129, 129, 0.2);
}

.cartmini__widget-item:not(:last-of-type) {
    border-bottom: 0;
}

.cartmini__thumb {
    border: 1px solid var(--tp-border-primary);
    margin-right: 15px;
}

.cartmini__thumb img {
    width: 70px;
    height: auto;
}

.cartmini__title {
    font-size: 15px;
    margin-bottom: 4px;
    font-weight: 500;
}

.cartmini__title a:hover {
    color: var(--tp-theme-primary);
}

.cartmini__content {
    padding-right: 15px;
}

.cartmini__content .tp-product-quantity {
    width: 75px;
    padding: 0;
}

.cartmini__content .tp-product-quantity .tp-cart-input[type=text] {
    height: 30px;
    text-align: center;
    font-size: 13px;
    border: 1px solid var(--tp-border-primary);
    background-color: var(--tp-common-white);
    padding: 0;
}

.cartmini__content .tp-product-quantity .tp-cart-plus,
.cartmini__content .tp-product-quantity .tp-cart-minus {
    width: 20px;
    height: 30px;
    line-height: 30px;
    display: inline-block;
    text-align: center;
    font-size: 13px;
    left: 3px;
}

.cartmini__content .tp-product-quantity .tp-cart-plus svg,
.cartmini__content .tp-product-quantity .tp-cart-minus svg {
    -webkit-transform: translateY(-1px);
    -moz-transform: translateY(-1px);
    -ms-transform: translateY(-1px);
    -o-transform: translateY(-1px);
    transform: translateY(-1px);
    width: 10px;
}

.cartmini__content .tp-product-quantity .tp-cart-plus::after,
.cartmini__content .tp-product-quantity .tp-cart-minus::after {
    display: none;
}

.cartmini__content .tp-product-quantity .tp-cart-plus {
    left: auto;
    right: 3px;
}

.cartmini__del {
    position: absolute;
    top: 15px;
    right: 0;
    width: 25px;
    height: 25px;
    line-height: 25px;
    text-align: center;
    color: var(--tp-common-black);
    font-size: 14px;
}

.cartmini__del:hover {
    color: var(--tp-theme-primary);
}

.cartmini__checkout {
    padding-top: 15px;
    padding-bottom: 85px;
    width: 100%;
    background: var(--tp-common-white);
    border-top: 2px solid var(--tp-border-primary);
}

.cartmini__checkout-title h4 {
    font-size: 15px;
    display: inline-block;
    font-weight: 500;
    margin-bottom: 0;
    text-transform: capitalize;
}

.cartmini__checkout-title span {
    float: right;
    font-size: 15px;
    color: var(--tp-common-black);
    font-weight: 500;
}

.cartmini__checkout-btn .tp-btn {
    font-size: 15px;
    text-transform: capitalize;
    padding: 10px 30px;
    text-align: center;
}

.cartmini__checkout-btn .tp-btn:hover {
    background-color: var(--tp-theme-primary);
    color: var(--tp-common-white);
    border-color: var(--tp-theme-primary);
}

.cartmini__checkout-btn .tp-btn-border:hover {
    background-color: var(--tp-theme-primary);
    color: var(--tp-common-white);
    border-color: var(--tp-theme-primary);
}

.cartmini__checkout-btn .tp-btn-border {
    background: none;
    color: var(--tp-heading-primary);
    border: 1px solid var(--tp-text-body);
}

.cartmini__checkout-btn .tp-btn-border:hover {
    border: 1px solid var(--tp-heading-primary);
    color: var(--tp-common-white);
    background: var(--tp-heading-primary);
}

.cartmini__price {
    font-size: 14px;
    font-weight: 500;
    color: var(--tp-theme-primary);
}

.cartmini__quantity {
    font-size: 12px;
    font-weight: 500;
}

.cartmini__empty {
    margin-top: 150px;
}

.cartmini__empty img {
    margin-bottom: 30px;
}

.cartmini__empty p {
    font-size: 16px;
    color: var(--tp-common-black);
    margin-bottom: 15px;
}

.cartmini__empty .tp-btn {
    background-color: var(--tp-grey-1);
    font-size: 15px;
    text-transform: capitalize;
    color: var(--tp-common-black);
    padding: 10px 30px;
    text-align: center;
}

.cartmini__empty .tp-btn:hover {
    background-color: var(--tp-common-black);
    color: var(--tp-common-white);
}

.table-content table {
    background: var(--tp-common-white);
    border-color: var(--tp-border-1);
    border-radius: 0;
    border-style: solid;
    border-width: 1px 0 0 1px;
    text-align: center;
    width: 100%;
    margin-bottom: 0;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .table-content table {
        width: 150%;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .table-content table {
        width: 200%;
    }
}

@media (max-width: 575px) {
    .table-content table {
        width: 300%;
    }
}

.table-content table.table > :not(:first-child) {
    border-top: 0;
}

.table-content table td {
    border-top: medium none;
    padding: 15px 10px;
    vertical-align: middle;
    font-size: 16px;
}

.table-content table td.product-name {
    font-size: 16px;
    font-weight: 500;
    text-transform: capitalize;
}

.table-content table td.product-name a:hover {
    color: var(--tp-text-primary);
}

.table-content .product-quantity {
    float: none;
}

.table-content .product-quantity input {
    color: #000;
    font-size: 14px;
    font-weight: normal;
    border: 1px solid var(--tp-border-1);
}

.table-content .table > :not(:last-child) > :last-child > * {
    border-bottom-color: var(--tp-border-1);
}

.table-content table th,
.table-content table td {
    border-bottom: 1px solid var(--tp-border-1);
    border-right: 1px solid var(--tp-border-1);
}

.table td,
.table th {
    border-top: 1px solid var(--tp-border-1);
}

/* Checkout */
.coupon-accordion h3 {
    background-color: #f6f6f6;
    border-top: 2px solid rgb(234, 187, 0);
    font-size: 14px;
    font-weight: 600;
    margin: 0 0 25px;
    padding: 1em 2em 1em 3.5em;
    position: relative;
    width: auto;
}

.coupon-accordion h3::before {
    content: "\f07b";
    left: 15px;
    top: 13px;
    position: absolute;
    color: #6f7172;
    font-family: "Font Awesome 5 Pro";
    font-weight: 700;
}

.coupon-accordion span {
    color: #6f7172;
    cursor: pointer;
    transition: 0.3s;
    font-weight: 500;
}

.coupon-accordion span:hover, .coupon-accordion span p.lost-password a:hover {
    color: var(--tp-theme-primary);
}

.payment-method {
    margin-top: 40px;
}

.payment-method .accordion-item:last-of-type {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
}

.payment-method .accordion-item {
    background-color: #fff;
    border: 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1490196078);
}

.payment-method .accordion-button {
    font-size: 16px;
    font-weight: 500;
    color: var(--tp-theme-primary);
    padding: 23px 0;
    border: none;
}

.payment-method .accordion-button:focus {
    box-shadow: none;
}

.payment-method .accordion-button::after {
    position: absolute;
    content: "\f067";
    right: 0;
    top: 50%;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    transform: translateY(-50%);
    font-family: "Font Awesome 5 Pro";
    font-size: 16px;
    font-weight: 400;
    margin-left: 0;
    background-image: none;
}

.payment-method .accordion-button:not(.collapsed) {
    color: var(--tp-theme-primary);
    background-color: var(--tp-common-white);
    box-shadow: none;
}

.payment-method .accordion-button:not(.collapsed)::after {
    content: "\f068";
}

.payment-method .accordion-body {
    font-size: 16px;
    padding: 8px 0;
    padding-bottom: 40px;
    color: var(--tp-text-body);
}

.payment-method .accordion-collapse {
    border: none;
}

.payment-method .accordion .card:first-of-type {
    border: 1px solid #eaedff;
}

.payment-method .card-header {
    background-color: #ffffff;
    border-bottom: 1px solid #eaedff;
}

.payment-method .card {
    background-color: #ffffff;
    border: 1px solid #eaedff;
    border-radius: 0;
    margin-bottom: 10px;
}

.payment-method .btn-link {
    background: no-repeat;
    border: medium none;
    border-radius: 0;
    color: #444;
    cursor: pointer;
    display: inline-block;
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 1px;
    line-height: 1;
    margin-bottom: 0;
    padding: 3px 10px;
    text-align: center;
    text-transform: uppercase;
    transition: all 0.3s ease 0s;
    vertical-align: middle;
    white-space: nowrap;
    text-decoration: none;
}

.product-quantity > input {
    background-color: #fff;
    height: 30px;
    width: 80px;
    border-radius: 3px;
}

.table-content table td.product-subtotal {
    font-size: 16px;
}

.table-content table td .cart-plus-minus {
    float: none;
    margin: 0 auto;
}

.coupon-all {
    margin-top: 50px;
}

.coupon {
    float: left;
}

@media (max-width: 767px) {
    .coupon {
        float: none;
    }
}

#coupon_code {
    height: 52px;
    border: 1px solid var(--tp-border-1);
    padding: 0 15px;
    margin-right: 10px;
    border-radius: 0;
    background: #fff;
    margin-bottom: 15px;
    width: 265px;
}

#coupon_code:focus {
    border: 1px solid var(--tp-theme-primary);
}

.tp-btn.tp-color-btn {
    padding: 13px 33px;
}

@media (max-width: 767px) {
    #coupon_code {
        margin-bottom: 15px;
    }
}

.coupon2 {
    float: right;
}

@media (max-width: 767px) {
    .coupon2 {
        float: none;
        margin-top: 15px;
    }
}

.cart-page-total {
    padding-top: 50px;
}

.cart-page-total > h2 {
    font-size: 25px;
    margin-bottom: 20px;
    text-transform: capitalize;
}

.cart-page-total > ul {
    border: 1px solid var(--tp-border-1);
}

.cart-page-total > ul > li {
    list-style: none;
    font-size: 15px;
    color: #6f7172;
    padding: 10px 30px;
    border-bottom: 1px solid var(--tp-border-1);
    font-weight: 400;
}

.cart-page-total ul > li > span {
    float: right;
}

.cart-page-total li:last-child {
    border-bottom: 0;
}

td.product-thumbnail img {
    width: 125px;
}

.tp-product-quantity .cart-input {
    height: 30px;
    background-color: #FFF;
}

.product-quantity .cart-plus, .product-quantity .cart-minus {
    width: 25px;
    height: 30px;
    border: 1px solid var(--tp-border-1);
    display: inline-block;
    vertical-align: middle;
    text-align: center;
    font-size: 14px;
    transition: 0.3s;
}

.product-quantity .cart-plus:hover, .product-quantity .cart-minus:hover {
    cursor: pointer;
    color: var(--tp-common-white);
    background: var(--tp-theme-primary);
}

.cart-input {
    height: 30px;
    width: 32px;
    text-align: center;
    font-size: 14px;
    border: none;
    border-top: 2px solid var(--tp-border-1);
    border-bottom: 2px solid var(--tp-border-1);
    display: inline-block;
    vertical-align: middle;
    margin: 0 -3px;
    padding-bottom: 0px;
}

.tp-wish-cart {
    min-width: 150px;
}

.coupon-content {
    border: 1px solid #eaedff;
    display: none;
    margin-bottom: 20px;
    padding: 30px;
}

.coupon-info p.coupon-text {
    margin-bottom: 15px;
}

.coupon-info p {
    margin-bottom: 0;
}

.coupon-info p.form-row-first label, .coupon-info p.form-row-last label {
    display: block;
    color: #6f7172;
}

.coupon-info p.form-row-first label span.required, .coupon-info p.form-row-last label span.required {
    color: red;
    font-weight: 700;
}

.coupon-info p.form-row-first input, .coupon-info p.form-row-last input {
    border: 1px solid #eaedff;
    height: 45px;
    margin: 0 0 14px;
    max-width: 100%;
    padding: 0 0 0 10px;
    width: 100%;
    outline: none;
    box-shadow: none;
}

.coupon-info p.form-row-first input:focus, .coupon-info p.form-row-last input:focus {
    border-color: var(--tp-theme-primary);
}

.coupon-info p.form-row input[type=submit]:hover, p.checkout-coupon input[type=submit]:hover {
    background: #3e976c none repeat scroll 0 0;
}

.coupon-info p.form-row input[type=checkbox] {
    position: relative;
    top: 4px;
    transform: translateY(-4px);
}

.form-row > label {
    margin-top: 15px;
    margin-left: 15px;
    color: #6f7172;
}

.buttons-cart input, .coupon input[type=submit], .buttons-cart a, .coupon-info p.form-row input[type=submit] {
    background: #252525 none repeat scroll 0 0;
    border: medium none;
    color: #fff;
    display: inline-block;
    font-size: 12px;
    font-weight: 700;
    height: 40px;
    line-height: 40px;
    margin-right: 15px;
    padding: 0 15px;
    text-transform: uppercase;
    transition: all 0.3s ease 0s;
}

p.lost-password {
    margin-top: 15px;
}

p.lost-password a {
    color: #6f6f6f;
}

p.checkout-coupon input[type=text] {
    height: 45px;
    padding: 0 15px;
    width: 100%;
    border: 1px solid #eaedff;
    margin-bottom: 15px;
    outline: none;
    box-shadow: none;
}

p.checkout-coupon input[type=text]:focus {
    border-color: var(--tp-theme-primary);
}

.coupon-checkout-content {
    display: none;
}

.checkbox-form h3 {
    border-bottom: 1px solid #eaedff;
    font-size: 24px;
    font-weight: 800;
    margin: 0 0 20px;
    padding-bottom: 10px;
    width: 100%;
}

.country-select {
    margin-bottom: 30px;
    position: relative;
}

.country-select select {
    width: 100%;
    font-size: 16px;
    background-color: transparent;
    border: 1px solid #eaedff;
    padding: 0 10px;
    height: 50px;
}

.country-select label, .checkout-form-list label {
    color: #6f7172;
    display: block;
    margin: 0 0 5px;
}

.country-select label span.required, .checkout-form-list label span.required {
    color: red;
}

.country-select .nice-select {
    border: 1px solid #eaedff;
    height: 52px;
    padding-left: 10px;
    width: 100%;
    color: var(--tp-text-body);
    margin-bottom: 20px;
    line-height: 50px;
}

.country-select .nice-select::after {
    right: 20px;
    top: 25px;
}

.country-select .nice-select .list {
    width: 100%;
}

.checkout-form-list {
    margin-bottom: 30px;
}

.checkout-form-list label {
    color: var(--tp-text-body);
}

.checkout-form-list input[type=text], .checkout-form-list input[type=password], .checkout-form-list input[type=email] {
    background: #ffffff;
    border: 1px solid #eaedff;
    border-radius: 0;
    height: 52px;
    padding: 0 0 0 10px;
    width: 100%;
    outline: none;
    box-shadow: none;
}

.checkout-form-list input[type=text]:focus, .checkout-form-list input[type=password]:focus, .checkout-form-list input[type=email]:focus {
    border-color: var(--tp-theme-primary);
}

.checkout-form-list input[type=text]::-moz-placeholder,
.checkout-form-list input[type=password]::-moz-placeholder,
.checkout-form-list input[type=email]::-moz-placeholder {
    color: #6f7172;
    opacity: 1;
}

.checkout-form-list input[type=text]::placeholder,
.checkout-form-list input[type=password]::placeholder,
.checkout-form-list input[type=email]::placeholder {
    color: #6f7172;
    opacity: 1;
}

.checkout-form-list input[type=checkbox] {
    display: inline-block;
    margin-right: 10px;
    position: relative;
    top: 4px;
    transform: translateY(-3px);
}

.create-acc label {
    color: var(--tp-text-body);
    display: inline-block;
}

.checkout-form-list #cbox {
    transform: translateY(-5px);
}

.create-account {
    display: none;
}

.ship-different-title h3 label {
    display: inline-block;
    margin-right: 20px;
    color: var(--tp-heading-primary);
}

.ship-different-title h3 #ship-box {
    transform: translateY(-2px);
}

.order-notes textarea {
    border: 1px solid #eaedff;
    height: 120px;
    padding: 15px;
    width: 100%;
    outline: 0;
    resize: none;
}

.order-notes textarea:focus {
    border-color: var(--tp-theme-primary);
}

.order-notes textarea::-moz-placeholder {
    color: #6f7172;
    opacity: 1;
}

.order-notes textarea::placeholder {
    color: #6f7172;
    opacity: 1;
}

#ship-box-info {
    display: none;
}

.panel-group .panel {
    border-radius: 0;
}

.panel-default > .panel-heading {
    border-radius: 0;
}

.your-order {
    padding: 30px 40px 45px;
    border: 1px solid rgb(234, 187, 0);
}

@media (max-width: 767px) {
    .your-order {
        padding: 15px;
    }
}

.your-order h3 {
    border-bottom: 1px solid #eaedff;
    font-size: 30px;
    font-weight: 800;
    margin: 0 0 20px;
    padding-bottom: 10px;
    width: 100%;
}

.your-order-table table {
    background: none;
    border: 0;
    width: 100%;
}

.your-order-table table th, .your-order-table table td {
    border-bottom: 1px solid #eaedff;
    border-right: medium none;
    color: var(--tp-text-body);
    font-size: 16px;
    padding: 15px 0;
    text-align: left;
}

@media (max-width: 767px) {
    .your-order-table table th, .your-order-table table td {
        padding-right: 10px;
    }
}

.your-order-table table th {
    border-top: medium none;
    color: var(--tp-text-body);
    font-weight: normal;
    text-align: left;
    vertical-align: middle;
    white-space: nowrap;
    width: 250px;
}

.panel-body > p {
    color: #222;
}

.your-order-table table .shipping ul li {
    list-style: none;
}

.your-order-table table .shipping ul li input {
    position: relative;
    top: 0px;
}

.your-order-table table .shipping ul li label {
    color: #6f7172;
}

.your-order-table table .shipping th {
    vertical-align: top;
}

.your-order-table table .order-total th {
    border-bottom: 0;
    font-size: 14px;
}

.your-order-table table .order-total td {
    border-bottom: medium none;
}

.your-order-table table tr.cart_item:hover {
    background: #F9F9F9;
}

.your-order-table table tr.order-total td span {
    color: var(--tp-theme-primary);
    font-size: 18px;
    font-weight: 500;
}

.order-button-payment input {
    background: #232323 none repeat scroll 0 0;
    border: medium none;
    color: #fff;
    font-size: 15px;
    font-weight: 600;
    height: 40px;
    margin: 0px 0 0;
    padding: 0;
    text-transform: uppercase;
    transition: all 0.3s ease 0s;
    width: 100%;
}

.order-button-payment input:hover {
    background: #3e976c none repeat scroll 0 0;
}

.panel-title > a {
    display: block;
}

.card-header:first-child {
    border-radius: 0;
}

/*----------------------------------------*/
/*  7.3 Awerds
/*----------------------------------------*/
.tp-awards-item {
    padding-top: 35px;
    padding-bottom: 35px;
    border-bottom: 1px solid rgb(230, 230, 230);
}

.tp-awards-item:nth-child(1) {
    border-top: 1px solid rgb(230, 230, 230);
}

.tp-awards-item a {
    display: block;
    height: 100%;
    width: 100%;
}

.tp-awards-item::after {
    position: absolute;
    content: "";
    bottom: 0;
    left: 0;
    width: 0;
    height: 1px;
    transition: all 0.7s ease-in-out;
    background: var(--tp-theme-primary);
}

.tp-awards-item:hover::after {
    width: 100%;
}

.tp-awards-item-inner:hover .tp-awards-title {
    color: var(--tp-theme-primary);
}

.tp-awards-item-inner:hover .tp-awards-icon span {
    color: var(--tp-theme-primary);
}

.tp-awards-item-inner:hover .tp-awards-date-title {
    color: var(--tp-theme-primary);
}

@media (max-width: 575px) {
    .tp-awards-item-left {
        flex-wrap: wrap;
    }
}

.tp-awards-date {
    margin-right: 180px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    .tp-awards-date {
        margin-right: 140px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .tp-awards-date {
        margin-right: 100px;
    }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
    .tp-awards-date {
        margin-right: 20px;
    }
}

@media (max-width: 575px) {
    .tp-awards-date {
        margin-right: 0;
        margin-bottom: 5px;
    }
}

.tp-awards-date-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--tp-theme-secondary);
}

.tp-awards-title {
    font-size: 28px;
    font-weight: 600;
    color: var(--tp-theme-secondary);
}

.tp-awards-title-wrap {
    margin-bottom: 50px;
}

.tp-awards-text-wrap span {
    font-size: 16px;
    font-weight: 600;
    display: inline-block;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
    color: var(--tp-text-body);
}

.tp-awards-icon span {
    font-size: 20px;
}

.tp-awards-item .tp-hover-reveal-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 252px;
    height: 257px;
    opacity: 0;
    margin: -150px 0 0 -150px;
    overflow: hidden;
    pointer-events: none;
    z-index: 99;
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    -webkit-transition: opacity 0.3s, transform 0.5s cubic-bezier(0.215, 0.61, 0.355, 1), -webkit-transform 0.5s cubic-bezier(0.215, 0.61, 0.355, 1);
    -moz-transition: opacity 0.3s, transform 0.5s cubic-bezier(0.215, 0.61, 0.355, 1), -webkit-transform 0.5s cubic-bezier(0.215, 0.61, 0.355, 1);
    -ms-transition: opacity 0.3s, transform 0.5s cubic-bezier(0.215, 0.61, 0.355, 1), -webkit-transform 0.5s cubic-bezier(0.215, 0.61, 0.355, 1);
    -o-transition: opacity 0.3s, transform 0.5s cubic-bezier(0.215, 0.61, 0.355, 1), -webkit-transform 0.5s cubic-bezier(0.215, 0.61, 0.355, 1);
    transition: opacity 0.3s, transform 0.5s cubic-bezier(0.215, 0.61, 0.355, 1), -webkit-transform 0.5s cubic-bezier(0.215, 0.61, 0.355, 1);
}

.tp-awards-item:hover .tp-hover-reveal-bg {
    opacity: 1;
}

/*# sourceMappingURL=main.css.map */

/* Choose Language Button */
.btn-choose-language {
    border: 1px solid #828282;
    padding: 2px 10px !important;
    color: #fff;
    border-radius: 0px;
}

.btn-choose-language:hover {
    color: #fff;
}

.tp-header-change-language {
    margin-right: 20px;
}

.tp-video-area {
    padding-top: 120px;
}

.tp-feature-area {
    padding-bottom: 0px;
}

.tp-counter-area {
    padding-bottom: 60px;
}

/* no padding on mobile */
@media (max-width: 767px) {
    .tp-video-area {
        padding-top: 0;
    }

    .tp-counter-area {
        padding-bottom: 0;
    }

    .tp-feature-area {
        padding-bottom: 160px;
    }
}

.tp-feature-6-arae .tp-header-contact-icon button:hover {
    color: white;
}
* { box-sizing: border-box; }
.slider-video-background {
  background: #000;
  position: fixed;
  top: 0; right: 0; bottom: 0; left: 0;
  z-index: -99;
}
.slider-video-foreground,
.slider-video-background iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

@media (min-aspect-ratio: 16/9) {
  .slider-video-foreground { height: 300%; top: -100%; }
}
@media (max-aspect-ratio: 16/9) {
  .slider-video-foreground { width: 300%; left: -100%; }
}
@media all and (max-width: 600px) {
.vid-info { width: 50%; padding: .5rem; }
.vid-info h1 { margin-bottom: .2rem; }
}
@media all and (max-width: 500px) {
.vid-info .acronym { display: none; }
}

.tp-history-thumb {
    max-width: 250px;
}

.about-desktop-slider .swiper-slide {
    height: auto;
}

.about-desktop-slider .swiper-slide .tp-service-3-item {
    height: calc(100% - 100px);
}

.tp-service-3-item .tp-service-3-btn {
    border: 1px solid var(--tp-theme-primary);
}

.about-desktop-slider .swiper-slide .tp-service-3-img {
    opacity: 1;
    /*top: 50%;*/
    /*transform: translateY(calc(-50% - 15px));*/
    z-index: 1;
    -webkit-transition: all 0.3s 0s ease-out;
    -moz-transition: all 0.3s 0s ease-out;
    -ms-transition: all 0.3s 0s ease-out;
    -o-transition: all 0.3s 0s ease-out;
    transition: all 0.3s 0s ease-out;
}

.about-desktop-slider .swiper-slide:hover .tp-service-3-img {
    opacity: 1;
}

/*.about-desktop-slider .swiper-slide:hover .tp-service-3-content {*/
/*    opacity: 0;*/
/*}*/

.image-resizer {
    width: 100%;
    height: 0;
    padding-bottom: 100%;
    background-size: cover;
}

.breadcrumbs-category .breadcrumb-category-item {
    display: flex;
    align-items: center;
    justify-content: space-around;
    text-align: center;
    line-height: 1.5;
    font-size: 1rem;
    padding: 12px .5rem;
    width: 100%;
    --bs-border-width: 1px;
    border: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color);
    min-height: 50px;
    color: var(--tp-common-black);
}
.breadcrumbs-category .breadcrumb-category-item.product-add-cart-btn-3 {
    color: var(--tp-common-white);
}

.breadcrumbs-category .breadcrumb-category-item:hover {
    color: var(--tp-common-white);
    background-color: var(--tp-theme-primary);
    border-color: var(--tp-theme-primary);
}

.homepage-product-thumb {
    height: 125px;
    width: auto;
    overflow: hidden;
}
.mt-5{margin-top:5px !important}.mt-10{margin-top:10px}.mt-15{margin-top:15px}.mt-20{margin-top:20px}.mt-25{margin-top:25px}.mt-30{margin-top:30px}.mt-35{margin-top:35px}.mt-40{margin-top:40px}.mt-45{margin-top:45px}.mt-50{margin-top:50px}.mt-55{margin-top:55px}.mt-60{margin-top:60px}.mt-65{margin-top:65px}.mt-70{margin-top:70px}.mt-75{margin-top:75px}.mt-80{margin-top:80px}.mt-85{margin-top:85px}.mt-90{margin-top:90px}.mt-95{margin-top:95px}.mt-100{margin-top:100px}.mt-105{margin-top:105px}.mt-110{margin-top:110px}.mt-115{margin-top:115px}.mt-120{margin-top:120px}.mt-125{margin-top:125px}.mt-130{margin-top:130px}.mt-135{margin-top:135px}.mt-140{margin-top:140px}.mt-145{margin-top:145px}.mt-150{margin-top:150px}.mt-155{margin-top:155px}.mt-160{margin-top:160px}.mt-165{margin-top:165px}.mt-170{margin-top:170px}.mt-175{margin-top:175px}.mt-180{margin-top:180px}.mt-185{margin-top:185px}.mt-190{margin-top:190px}.mt-195{margin-top:195px}.mt-200{margin-top:200px}.mb-5{margin-bottom:5px !important}.mb-10{margin-bottom:10px}.mb-15{margin-bottom:15px}.mb-20{margin-bottom:20px}.mb-25{margin-bottom:25px}.mb-30{margin-bottom:30px}.mb-35{margin-bottom:35px}.mb-40{margin-bottom:40px}.mb-45{margin-bottom:45px}.mb-50{margin-bottom:50px}.mb-55{margin-bottom:55px}.mb-60{margin-bottom:60px}.mb-65{margin-bottom:65px}.mb-70{margin-bottom:70px}.mb-75{margin-bottom:75px}.mb-80{margin-bottom:80px}.mb-85{margin-bottom:85px}.mb-90{margin-bottom:90px}.mb-95{margin-bottom:95px}.mb-100{margin-bottom:100px}.mb-105{margin-bottom:105px}.mb-110{margin-bottom:110px}.mb-115{margin-bottom:115px}.mb-120{margin-bottom:120px}.mb-125{margin-bottom:125px}.mb-130{margin-bottom:130px}.mb-135{margin-bottom:135px}.mb-140{margin-bottom:140px}.mb-145{margin-bottom:145px}.mb-150{margin-bottom:150px}.mb-155{margin-bottom:155px}.mb-160{margin-bottom:160px}.mb-165{margin-bottom:165px}.mb-170{margin-bottom:170px}.mb-175{margin-bottom:175px}.mb-180{margin-bottom:180px}.mb-185{margin-bottom:185px}.mb-190{margin-bottom:190px}.mb-195{margin-bottom:195px}.mb-200{margin-bottom:200px}.ml-5{margin-left:5px}.ml-10{margin-left:10px}.ml-15{margin-left:15px}.ml-20{margin-left:20px}.ml-25{margin-left:25px}.ml-30{margin-left:30px}.ml-35{margin-left:35px}.ml-40{margin-left:40px}.ml-45{margin-left:45px}.ml-50{margin-left:50px}.ml-55{margin-left:55px}.ml-60{margin-left:60px}.ml-65{margin-left:65px}.ml-70{margin-left:70px}.ml-75{margin-left:75px}.ml-80{margin-left:80px}.ml-85{margin-left:85px}.ml-90{margin-left:90px}.ml-95{margin-left:95px}.ml-100{margin-left:100px}.ml-105{margin-left:105px}.ml-110{margin-left:110px}.ml-115{margin-left:115px}.ml-120{margin-left:120px}.ml-125{margin-left:125px}.ml-130{margin-left:130px}.ml-135{margin-left:135px}.ml-140{margin-left:140px}.ml-145{margin-left:145px}.ml-150{margin-left:150px}.ml-155{margin-left:155px}.ml-160{margin-left:160px}.ml-165{margin-left:165px}.ml-170{margin-left:170px}.ml-175{margin-left:175px}.ml-180{margin-left:180px}.ml-185{margin-left:185px}.ml-190{margin-left:190px}.ml-195{margin-left:195px}.ml-200{margin-left:200px}.mr-5{margin-right:5px}.mr-10{margin-right:10px}.mr-15{margin-right:15px}.mr-20{margin-right:20px}.mr-25{margin-right:25px}.mr-30{margin-right:30px}.mr-35{margin-right:35px}.mr-40{margin-right:40px}.mr-45{margin-right:45px}.mr-50{margin-right:50px}.mr-55{margin-right:55px}.mr-60{margin-right:60px}.mr-65{margin-right:65px}.mr-70{margin-right:70px}.mr-75{margin-right:75px}.mr-80{margin-right:80px}.mr-85{margin-right:85px}.mr-90{margin-right:90px}.mr-95{margin-right:95px}.mr-100{margin-right:100px}.mr-105{margin-right:105px}.mr-110{margin-right:110px}.mr-115{margin-right:115px}.mr-120{margin-right:120px}.mr-125{margin-right:125px}.mr-130{margin-right:130px}.mr-135{margin-right:135px}.mr-140{margin-right:140px}.mr-145{margin-right:145px}.mr-150{margin-right:150px}.mr-155{margin-right:155px}.mr-160{margin-right:160px}.mr-165{margin-right:165px}.mr-170{margin-right:170px}.mr-175{margin-right:175px}.mr-180{margin-right:180px}.mr-185{margin-right:185px}.mr-190{margin-right:190px}.mr-195{margin-right:195px}.mr-200{margin-right:200px}.pt-5{padding-top:5px !important}.pt-10{padding-top:10px}.pt-15{padding-top:15px}.pt-20{padding-top:20px}.pt-25{padding-top:25px}.pt-30{padding-top:30px}.pt-35{padding-top:35px}.pt-40{padding-top:40px}.pt-45{padding-top:45px}.pt-50{padding-top:50px}.pt-55{padding-top:55px}.pt-60{padding-top:60px}.pt-65{padding-top:65px}.pt-70{padding-top:70px}.pt-75{padding-top:75px}.pt-80{padding-top:80px}.pt-85{padding-top:85px}.pt-90{padding-top:90px}.pt-95{padding-top:95px}.pt-100{padding-top:100px}.pt-105{padding-top:105px}.pt-110{padding-top:110px}.pt-115{padding-top:115px}.pt-120{padding-top:120px}.pt-125{padding-top:125px}.pt-130{padding-top:130px}.pt-135{padding-top:135px}.pt-140{padding-top:140px}.pt-145{padding-top:145px}.pt-150{padding-top:150px}.pt-155{padding-top:155px}.pt-160{padding-top:160px}.pt-165{padding-top:165px}.pt-170{padding-top:170px}.pt-175{padding-top:175px}.pt-180{padding-top:180px}.pt-185{padding-top:185px}.pt-190{padding-top:190px}.pt-195{padding-top:195px}.pt-200{padding-top:200px}.pt-245{padding-top:245px}.pt-250{padding-top: 250px;}.pt-305{padding-top:305px}.pb-5{padding-bottom:5px !important}.pb-10{padding-bottom:10px}.pb-15{padding-bottom:15px}.pb-20{padding-bottom:20px}.pb-25{padding-bottom:25px}.pb-30{padding-bottom:30px}.pb-35{padding-bottom:35px}.pb-40{padding-bottom:40px}.pb-45{padding-bottom:45px}.pb-50{padding-bottom:50px}.pb-55{padding-bottom:55px}.pb-60{padding-bottom:60px}.pb-65{padding-bottom:65px}.pb-70{padding-bottom:70px}.pb-75{padding-bottom:75px}.pb-80{padding-bottom:80px}.pb-85{padding-bottom:85px}.pb-90{padding-bottom:90px}.pb-95{padding-bottom:95px}.pb-100{padding-bottom:100px}.pb-105{padding-bottom:105px}.pb-110{padding-bottom:110px}.pb-115{padding-bottom:115px}.pb-120{padding-bottom:120px}.pb-125{padding-bottom:125px}.pb-130{padding-bottom:130px}.pb-135{padding-bottom:135px}.pb-140{padding-bottom:140px}.pb-145{padding-bottom:145px}.pb-150{padding-bottom:150px}.pb-155{padding-bottom:155px}.pb-160{padding-bottom:160px}.pb-165{padding-bottom:165px}.pb-170{padding-bottom:170px}.pb-175{padding-bottom:175px}.pb-180{padding-bottom:180px}.pb-185{padding-bottom:185px}.pb-190{padding-bottom:190px}.pb-195{padding-bottom:195px}.pb-200{padding-bottom:200px}.pb-250{padding-bottom:250px}.pl-5{padding-left:5px}.pl-10{padding-left:10px}.pl-15{padding-left:15px}.pl-20{padding-left:20px}.pl-25{padding-left:25px}.pl-30{padding-left:30px}.pl-35{padding-left:35px}.pl-40{padding-left:40px}.pl-45{padding-left:45px}.pl-50{padding-left:50px}.pl-55{padding-left:55px}.pl-60{padding-left:60px}.pl-65{padding-left:65px}.pl-70{padding-left:70px}.pl-75{padding-left:75px}.pl-80{padding-left:80px}.pl-85{padding-left:85px}.pl-90{padding-left:90px}.pl-95{padding-left:95px}.pl-100{padding-left:100px}.pl-105{padding-left:105px}.pl-110{padding-left:110px}.pl-115{padding-left:115px}.pl-120{padding-left:120px}.pl-125{padding-left:125px}.pl-130{padding-left:130px}.pl-135{padding-left:135px}.pl-140{padding-left:140px}.pl-145{padding-left:145px}.pl-150{padding-left:150px}.pl-155{padding-left:155px}.pl-160{padding-left:160px}.pl-165{padding-left:165px}.pl-170{padding-left:170px}.pl-175{padding-left:175px}.pl-180{padding-left:180px}.pl-185{padding-left:185px}.pl-190{padding-left:190px}.pl-195{padding-left:195px}.pl-200{padding-left:200px}.pr-5{padding-right:5px}.pr-10{padding-right:10px}.pr-15{padding-right:15px}.pr-20{padding-right:20px}.pr-25{padding-right:25px}.pr-30{padding-right:30px}.pr-35{padding-right:35px}.pr-40{padding-right:40px}.pr-45{padding-right:45px}.pr-50{padding-right:50px}.pr-55{padding-right:55px}.pr-60{padding-right:60px}.pr-65{padding-right:65px}.pr-70{padding-right:70px}.pr-75{padding-right:75px}.pr-80{padding-right:80px}.pr-85{padding-right:85px}.pr-90{padding-right:90px}.pr-95{padding-right:95px}.pr-100{padding-right:100px}.pr-105{padding-right:105px}.pr-110{padding-right:110px}.pr-115{padding-right:115px}.pr-120{padding-right:120px}.pr-125{padding-right:125px}.pr-130{padding-right:130px}.pr-135{padding-right:135px}.pr-140{padding-right:140px}.pr-145{padding-right:145px}.pr-150{padding-right:150px}.pr-155{padding-right:155px}.pr-160{padding-right:160px}.pr-165{padding-right:165px}.pr-170{padding-right:170px}.pr-175{padding-right:175px}.pr-180{padding-right:180px}.pr-185{padding-right:185px}.pr-190{padding-right:190px}.pr-195{padding-right:195px}.pr-200{padding-right:200px}