/**
 * Advanced JavaScript Lazy Loader
 * Optimizes page load performance by deferring non-critical scripts
 * Compatible with PHP 7.3+
 */
class LazyScriptLoader {
    constructor() {
        this.loadedScripts = new Set();
        this.scriptQueue = [];
        this.isLoading = false;
        this.observers = new Map();
        
        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.init());
        } else {
            this.init();
        }
    }
    
    init() {
        this.setupIntersectionObservers();
        this.loadConditionalScripts();
        this.setupEventListeners();
    }
    
    /**
     * Load script dynamically
     */
    loadScript(src, options = {}) {
        return new Promise((resolve, reject) => {
            if (this.loadedScripts.has(src)) {
                resolve();
                return;
            }
            
            const script = document.createElement('script');
            script.src = src;
            
            // Apply options
            if (options.integrity) script.integrity = options.integrity;
            if (options.crossOrigin) script.crossOrigin = options.crossOrigin;
            if (options.referrerPolicy) script.referrerPolicy = options.referrerPolicy;
            if (options.defer) script.defer = true;
            if (options.async) script.async = true;
            
            script.onload = () => {
                this.loadedScripts.add(src);
                resolve();
            };
            
            script.onerror = () => {
                reject(new Error(`Failed to load script: ${src}`));
            };
            
            document.head.appendChild(script);
        });
    }
    
    /**
     * Load multiple scripts in sequence
     */
    async loadScripts(scripts) {
        for (const script of scripts) {
            if (typeof script === 'string') {
                await this.loadScript(script);
            } else {
                await this.loadScript(script.src, script.options || {});
            }
        }
    }
    
    /**
     * Setup intersection observers for lazy loading
     */
    setupIntersectionObservers() {
        // Observer for animation elements (WOW.js, GSAP)
        const animationObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.loadAnimationScripts();
                    animationObserver.unobserve(entry.target);
                }
            });
        }, { rootMargin: '50px' });
        
        // Observer for gallery elements
        const galleryObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.loadGalleryScripts();
                    galleryObserver.unobserve(entry.target);
                }
            });
        }, { rootMargin: '100px' });
        
        // Observer for slider elements
        const sliderObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.loadSliderScripts();
                    sliderObserver.unobserve(entry.target);
                }
            });
        }, { rootMargin: '100px' });
        
        // Observe elements
        document.querySelectorAll('[data-wow], .wow, .animate__animated').forEach(el => {
            animationObserver.observe(el);
        });
        
        document.querySelectorAll('[data-lightgallery], .lightgallery, .lg-gallery').forEach(el => {
            galleryObserver.observe(el);
        });
        
        document.querySelectorAll('.swiper, .tp-slider, [data-swiper]').forEach(el => {
            sliderObserver.observe(el);
        });
        
        this.observers.set('animation', animationObserver);
        this.observers.set('gallery', galleryObserver);
        this.observers.set('slider', sliderObserver);
    }
    
    /**
     * Load scripts based on page content
     */
    loadConditionalScripts() {
        const assetsUrl = window.ASSETS_URL || '/assets';
        const version = window.ASSETS_VERSION || '';
        const versionParam = version ? '?v=' + version : '';

        // Load form scripts if forms are present
        if (document.querySelectorAll('form').length > 0) {
            this.loadScript(assetsUrl + '/js/ajax-form.js' + versionParam);
        }

        // Load counter scripts if counter elements are present
        if (document.querySelectorAll('[data-purecounter], .purecounter').length > 0) {
            this.loadScript(assetsUrl + '/js/purecounter.js' + versionParam);
        }

        // Load countdown scripts if countdown elements are present
        if (document.querySelectorAll('[data-countdown], .countdown').length > 0) {
            this.loadScript(assetsUrl + '/js/countdown.js' + versionParam);
        }

        // Load nice-select if custom selects are present
        if (document.querySelectorAll('select.nice-select, [data-nice-select]').length > 0) {
            this.loadScript(assetsUrl + '/js/nice-select.js' + versionParam);
        }
    }
    
    /**
     * Load animation-related scripts
     */
    async loadAnimationScripts() {
        const assetsUrl = window.ASSETS_URL || '/assets';
        const version = window.ASSETS_VERSION || '';
        const versionParam = version ? '?v=' + version : '';

        const scripts = [
            assetsUrl + '/js/gsap.js' + versionParam,
            assetsUrl + '/js/wow.js' + versionParam
        ];

        try {
            await this.loadScripts(scripts);

            // Initialize WOW.js if available
            if (typeof WOW !== 'undefined') {
                new WOW().init();
            }
        } catch (error) {
            console.warn('Failed to load animation scripts:', error);
        }
    }
    
    /**
     * Load gallery-related scripts
     */
    async loadGalleryScripts() {
        const scripts = [
            {
                src: 'https://cdnjs.cloudflare.com/ajax/libs/lightgallery/2.8.1/lightgallery.min.js',
                options: {
                    integrity: 'sha512-n82wdm8yNoOCDS7jsP6OEe12S0GHQV7jGSwj5V2tcNY/KM3z+oSDraUN3Hjf3EgOS9HWa4s3DmSSM2Z9anVVRQ==',
                    crossOrigin: 'anonymous',
                    referrerPolicy: 'no-referrer'
                }
            },
            {
                src: 'https://cdnjs.cloudflare.com/ajax/libs/lightgallery/2.8.1/plugins/zoom/lg-zoom.min.js',
                options: {
                    integrity: 'sha512-/nVDv6BV2iCSxA44tgJLUgxmURDKfoUYdpLah0Hz8s3rpWBMHeiSzrr7bplsMBf+mF2L//RXX2q/SE6B0UhCRA==',
                    crossOrigin: 'anonymous',
                    referrerPolicy: 'no-referrer'
                }
            },
            {
                src: 'https://cdnjs.cloudflare.com/ajax/libs/lightgallery/2.8.1/plugins/thumbnail/lg-thumbnail.min.js',
                options: {
                    integrity: 'sha512-JJ6kXdrz+7LlX78Nb0TttHqmOXaRmfmzrL1Z1W33cILbfhCK6/4IdDdi9COOfeggOWJTbqVoNiC/j9fLCJGLlw==',
                    crossOrigin: 'anonymous',
                    referrerPolicy: 'no-referrer'
                }
            }
        ];
        
        try {
            await this.loadScripts(scripts);
            
            // Initialize lightgallery
            if (typeof lightGallery !== 'undefined') {
                document.querySelectorAll('[data-lightgallery], .lightgallery, .lg-gallery').forEach(element => {
                    lightGallery(element, {
                        plugins: [lgZoom, lgThumbnail],
                        speed: 500,
                        download: false
                    });
                });
            }
        } catch (error) {
            console.warn('Failed to load gallery scripts:', error);
        }
    }
    
    /**
     * Load slider-related scripts
     */
    async loadSliderScripts() {
        const assetsUrl = window.ASSETS_URL || '/assets';
        const version = window.ASSETS_VERSION || '';
        const versionParam = version ? '?v=' + version : '';

        const scripts = [
            assetsUrl + '/js/swiper-bundle.js' + versionParam,
            assetsUrl + '/js/three.js',
            assetsUrl + '/js/webgl.js' + versionParam
        ];

        try {
            await this.loadScripts(scripts);
        } catch (error) {
            console.warn('Failed to load slider scripts:', error);
        }
    }
    
    /**
     * Setup event listeners for user interactions
     */
    setupEventListeners() {
        const assetsUrl = window.ASSETS_URL || '/assets';
        const version = window.ASSETS_VERSION || '';
        const versionParam = version ? '?v=' + version : '';

        // Load interaction scripts on first user interaction
        const interactionEvents = ['click', 'touchstart', 'keydown', 'mouseover'];
        const loadInteractionScripts = () => {
            this.loadScript(assetsUrl + '/js/hover-effect.umd.js' + versionParam);
            this.loadScript(assetsUrl + '/js/tilt.jquery.min.js' + versionParam);
            this.loadScript(assetsUrl + '/js/magnific-popup.js' + versionParam);
            this.loadScript(assetsUrl + '/js/imagesloaded-pkgd.js' + versionParam);
            this.loadScript(assetsUrl + '/js/isotope-pkgd.js' + versionParam);

            // Remove listeners after first interaction
            interactionEvents.forEach(event => {
                document.removeEventListener(event, loadInteractionScripts, { passive: true });
            });
        };

        interactionEvents.forEach(event => {
            document.addEventListener(event, loadInteractionScripts, { passive: true, once: true });
        });
    }
    
    /**
     * Preload critical scripts for next page
     */
    preloadNextPageScripts() {
        const assetsUrl = window.ASSETS_URL || '/assets';
        const version = window.ASSETS_VERSION || '';
        const versionParam = version ? '?v=' + version : '';

        const criticalScripts = [
            assetsUrl + '/js/vendor/jquery.js' + versionParam,
            assetsUrl + '/js/bootstrap-bundle.js' + versionParam,
            assetsUrl + '/js/main.js' + versionParam
        ];

        criticalScripts.forEach(src => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.href = src;
            link.as = 'script';
            document.head.appendChild(link);
        });
    }
    
    /**
     * Clean up observers
     */
    destroy() {
        this.observers.forEach(observer => observer.disconnect());
        this.observers.clear();
    }
}

// Initialize the lazy loader
window.lazyScriptLoader = new LazyScriptLoader();

// Preload scripts for better navigation
window.addEventListener('load', () => {
    window.lazyScriptLoader.preloadNextPageScripts();
});
