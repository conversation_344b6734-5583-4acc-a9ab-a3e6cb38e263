/**
 * UI Initiative Swiper GL
 *
 * Stunning WebGL image transitions
 *
 * https://uiinitiative.com
 *
 * Copyright 2022 UI Initiative
 *
 * Released under the UI Initiative Regular License
 *
 */

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).SwiperGL=e()}(this,(function(){"use strict";function t(t){let e=t[0],i=t[1],s=t[2];return Math.sqrt(e*e+i*i+s*s)}function e(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t}function i(t,e,i){return t[0]=e[0]+i[0],t[1]=e[1]+i[1],t[2]=e[2]+i[2],t}function s(t,e,i){return t[0]=e[0]-i[0],t[1]=e[1]-i[1],t[2]=e[2]-i[2],t}function r(t,e,i){return t[0]=e[0]*i,t[1]=e[1]*i,t[2]=e[2]*i,t}function n(t){let e=t[0],i=t[1],s=t[2];return e*e+i*i+s*s}function a(t,e){let i=e[0],s=e[1],r=e[2],n=i*i+s*s+r*r;return n>0&&(n=1/Math.sqrt(n)),t[0]=e[0]*n,t[1]=e[1]*n,t[2]=e[2]*n,t}function o(t,e){return t[0]*e[0]+t[1]*e[1]+t[2]*e[2]}function h(t,e,i){let s=e[0],r=e[1],n=e[2],a=i[0],o=i[1],h=i[2];return t[0]=r*h-n*o,t[1]=n*a-s*h,t[2]=s*o-r*a,t}const l=function(){const t=[0,0,0],i=[0,0,0];return function(s,r){e(t,s),e(i,r),a(t,t),a(i,i);let n=o(t,i);return n>1?0:n<-1?Math.PI:Math.acos(n)}}();class c extends Array{constructor(t=0,e=t,i=t){return super(t,e,i),this}get x(){return this[0]}get y(){return this[1]}get z(){return this[2]}set x(t){this[0]=t}set y(t){this[1]=t}set z(t){this[2]=t}set(t,e=t,i=t){return t.length?this.copy(t):(function(t,e,i,s){t[0]=e,t[1]=i,t[2]=s}(this,t,e,i),this)}copy(t){return e(this,t),this}add(t,e){return e?i(this,t,e):i(this,this,t),this}sub(t,e){return e?s(this,t,e):s(this,this,t),this}multiply(t){var e,i,s;return t.length?(i=this,s=t,(e=this)[0]=i[0]*s[0],e[1]=i[1]*s[1],e[2]=i[2]*s[2]):r(this,this,t),this}divide(t){var e,i,s;return t.length?(i=this,s=t,(e=this)[0]=i[0]/s[0],e[1]=i[1]/s[1],e[2]=i[2]/s[2]):r(this,this,1/t),this}inverse(t=this){var e,i;return i=t,(e=this)[0]=1/i[0],e[1]=1/i[1],e[2]=1/i[2],this}len(){return t(this)}distance(e){return e?function(t,e){let i=e[0]-t[0],s=e[1]-t[1],r=e[2]-t[2];return Math.sqrt(i*i+s*s+r*r)}(this,e):t(this)}squaredLen(){return n(this)}squaredDistance(t){return t?function(t,e){let i=e[0]-t[0],s=e[1]-t[1],r=e[2]-t[2];return i*i+s*s+r*r}(this,t):n(this)}negate(t=this){var e,i;return i=t,(e=this)[0]=-i[0],e[1]=-i[1],e[2]=-i[2],this}cross(t,e){return e?h(this,t,e):h(this,this,t),this}scale(t){return r(this,this,t),this}normalize(){return a(this,this),this}dot(t){return o(this,t)}equals(t){return i=t,(e=this)[0]===i[0]&&e[1]===i[1]&&e[2]===i[2];var e,i}applyMatrix3(t){return function(t,e,i){let s=e[0],r=e[1],n=e[2];t[0]=s*i[0]+r*i[3]+n*i[6],t[1]=s*i[1]+r*i[4]+n*i[7],t[2]=s*i[2]+r*i[5]+n*i[8]}(this,this,t),this}applyMatrix4(t){return function(t,e,i){let s=e[0],r=e[1],n=e[2],a=i[3]*s+i[7]*r+i[11]*n+i[15];a=a||1,t[0]=(i[0]*s+i[4]*r+i[8]*n+i[12])/a,t[1]=(i[1]*s+i[5]*r+i[9]*n+i[13])/a,t[2]=(i[2]*s+i[6]*r+i[10]*n+i[14])/a}(this,this,t),this}scaleRotateMatrix4(t){return function(t,e,i){let s=e[0],r=e[1],n=e[2],a=i[3]*s+i[7]*r+i[11]*n+i[15];a=a||1,t[0]=(i[0]*s+i[4]*r+i[8]*n)/a,t[1]=(i[1]*s+i[5]*r+i[9]*n)/a,t[2]=(i[2]*s+i[6]*r+i[10]*n)/a}(this,this,t),this}applyQuaternion(t){return function(t,e,i){let s=e[0],r=e[1],n=e[2],a=i[0],o=i[1],h=i[2],l=o*n-h*r,c=h*s-a*n,u=a*r-o*s,g=o*u-h*c,m=h*l-a*u,d=a*c-o*l,p=2*i[3];l*=p,c*=p,u*=p,g*=2,m*=2,d*=2,t[0]=s+l+g,t[1]=r+c+m,t[2]=n+u+d}(this,this,t),this}angle(t){return l(this,t)}lerp(t,e){return function(t,e,i,s){let r=e[0],n=e[1],a=e[2];t[0]=r+s*(i[0]-r),t[1]=n+s*(i[1]-n),t[2]=a+s*(i[2]-a)}(this,this,t,e),this}clone(){return new c(this[0],this[1],this[2])}fromArray(t,e=0){return this[0]=t[e],this[1]=t[e+1],this[2]=t[e+2],this}toArray(t=[],e=0){return t[e]=this[0],t[e+1]=this[1],t[e+2]=this[2],t}transformDirection(t){const e=this[0],i=this[1],s=this[2];return this[0]=t[0]*e+t[4]*i+t[8]*s,this[1]=t[1]*e+t[5]*i+t[9]*s,this[2]=t[2]*e+t[6]*i+t[10]*s,this.normalize()}}const u=new c;let g=1,m=1,d=!1;class p{constructor(t,e={}){t.canvas||console.error("gl not passed as first argument to Geometry"),this.gl=t,this.attributes=e,this.id=g++,this.VAOs={},this.drawRange={start:0,count:0},this.instancedCount=0,this.gl.renderer.bindVertexArray(null),this.gl.renderer.currentGeometry=null,this.glState=this.gl.renderer.state;for(let t in e)this.addAttribute(t,e[t])}addAttribute(t,e){if(this.attributes[t]=e,e.id=m++,e.size=e.size||1,e.type=e.type||(e.data.constructor===Float32Array?this.gl.FLOAT:e.data.constructor===Uint16Array?this.gl.UNSIGNED_SHORT:this.gl.UNSIGNED_INT),e.target="index"===t?this.gl.ELEMENT_ARRAY_BUFFER:this.gl.ARRAY_BUFFER,e.normalized=e.normalized||!1,e.stride=e.stride||0,e.offset=e.offset||0,e.count=e.count||(e.stride?e.data.byteLength/e.stride:e.data.length/e.size),e.divisor=e.instanced||0,e.needsUpdate=!1,e.usage=e.usage||this.gl.STATIC_DRAW,e.buffer||this.updateAttribute(e),e.divisor){if(this.isInstanced=!0,this.instancedCount&&this.instancedCount!==e.count*e.divisor)return console.warn("geometry has multiple instanced buffers of different length"),this.instancedCount=Math.min(this.instancedCount,e.count*e.divisor);this.instancedCount=e.count*e.divisor}else"index"===t?this.drawRange.count=e.count:this.attributes.index||(this.drawRange.count=Math.max(this.drawRange.count,e.count))}updateAttribute(t){const e=!t.buffer;e&&(t.buffer=this.gl.createBuffer()),this.glState.boundBuffer!==t.buffer&&(this.gl.bindBuffer(t.target,t.buffer),this.glState.boundBuffer=t.buffer),e?this.gl.bufferData(t.target,t.data,t.usage):this.gl.bufferSubData(t.target,0,t.data),t.needsUpdate=!1}setIndex(t){this.addAttribute("index",t)}setDrawRange(t,e){this.drawRange.start=t,this.drawRange.count=e}setInstancedCount(t){this.instancedCount=t}createVAO(t){this.VAOs[t.attributeOrder]=this.gl.renderer.createVertexArray(),this.gl.renderer.bindVertexArray(this.VAOs[t.attributeOrder]),this.bindAttributes(t)}bindAttributes(t){t.attributeLocations.forEach(((t,{name:e,type:i})=>{if(!this.attributes[e])return void console.warn(`active attribute ${e} not being supplied`);const s=this.attributes[e];this.gl.bindBuffer(s.target,s.buffer),this.glState.boundBuffer=s.buffer;let r=1;35674===i&&(r=2),35675===i&&(r=3),35676===i&&(r=4);const n=s.size/r,a=1===r?0:r*r*r,o=1===r?0:r*r;for(let e=0;e<r;e++)this.gl.vertexAttribPointer(t+e,n,s.type,s.normalized,s.stride+a,s.offset+e*o),this.gl.enableVertexAttribArray(t+e),this.gl.renderer.vertexAttribDivisor(t+e,s.divisor)})),this.attributes.index&&this.gl.bindBuffer(this.gl.ELEMENT_ARRAY_BUFFER,this.attributes.index.buffer)}draw({program:t,mode:e=this.gl.TRIANGLES}){this.gl.renderer.currentGeometry!==`${this.id}_${t.attributeOrder}`&&(this.VAOs[t.attributeOrder]||this.createVAO(t),this.gl.renderer.bindVertexArray(this.VAOs[t.attributeOrder]),this.gl.renderer.currentGeometry=`${this.id}_${t.attributeOrder}`),t.attributeLocations.forEach(((t,{name:e})=>{const i=this.attributes[e];i.needsUpdate&&this.updateAttribute(i)})),this.isInstanced?this.attributes.index?this.gl.renderer.drawElementsInstanced(e,this.drawRange.count,this.attributes.index.type,this.attributes.index.offset+2*this.drawRange.start,this.instancedCount):this.gl.renderer.drawArraysInstanced(e,this.drawRange.start,this.drawRange.count,this.instancedCount):this.attributes.index?this.gl.drawElements(e,this.drawRange.count,this.attributes.index.type,this.attributes.index.offset+2*this.drawRange.start):this.gl.drawArrays(e,this.drawRange.start,this.drawRange.count)}getPosition(){const t=this.attributes.position;return t.data?t:d?void 0:(console.warn("No position buffer data found to compute bounds"),d=!0)}computeBoundingBox(t){t||(t=this.getPosition());const e=t.data,i=t.stride?t.stride/e.BYTES_PER_ELEMENT:t.size;this.bounds||(this.bounds={min:new c,max:new c,center:new c,scale:new c,radius:1/0});const s=this.bounds.min,r=this.bounds.max,n=this.bounds.center,a=this.bounds.scale;s.set(1/0),r.set(-1/0);for(let t=0,n=e.length;t<n;t+=i){const i=e[t],n=e[t+1],a=e[t+2];s.x=Math.min(i,s.x),s.y=Math.min(n,s.y),s.z=Math.min(a,s.z),r.x=Math.max(i,r.x),r.y=Math.max(n,r.y),r.z=Math.max(a,r.z)}a.sub(r,s),n.add(s,r).divide(2)}computeBoundingSphere(t){t||(t=this.getPosition());const e=t.data,i=t.stride?t.stride/e.BYTES_PER_ELEMENT:t.size;this.bounds||this.computeBoundingBox(t);let s=0;for(let t=0,r=e.length;t<r;t+=i)u.fromArray(e,t),s=Math.max(s,this.bounds.center.squaredDistance(u));this.bounds.radius=Math.sqrt(s)}remove(){for(let t in this.VAOs)this.gl.renderer.deleteVertexArray(this.VAOs[t]),delete this.VAOs[t];for(let t in this.attributes)this.gl.deleteBuffer(this.attributes[t].buffer),delete this.attributes[t]}}let x=1;const f={};class A{constructor(t,{vertex:e,fragment:i,uniforms:s={},transparent:r=!1,cullFace:n=t.BACK,frontFace:a=t.CCW,depthTest:o=!0,depthWrite:h=!0,depthFunc:l=t.LESS}={}){t.canvas||console.error("gl not passed as fist argument to Program"),this.gl=t,this.uniforms=s,this.id=x++,e||console.warn("vertex shader not supplied"),i||console.warn("fragment shader not supplied"),this.transparent=r,this.cullFace=n,this.frontFace=a,this.depthTest=o,this.depthWrite=h,this.depthFunc=l,this.blendFunc={},this.blendEquation={},this.transparent&&!this.blendFunc.src&&(this.gl.renderer.premultipliedAlpha?this.setBlendFunc(this.gl.ONE,this.gl.ONE_MINUS_SRC_ALPHA):this.setBlendFunc(this.gl.SRC_ALPHA,this.gl.ONE_MINUS_SRC_ALPHA));const c=t.createShader(t.VERTEX_SHADER);t.shaderSource(c,e),t.compileShader(c),""!==t.getShaderInfoLog(c)&&console.warn(`${t.getShaderInfoLog(c)}\nVertex Shader\n${M(e)}`);const u=t.createShader(t.FRAGMENT_SHADER);if(t.shaderSource(u,i),t.compileShader(u),""!==t.getShaderInfoLog(u)&&console.warn(`${t.getShaderInfoLog(u)}\nFragment Shader\n${M(i)}`),this.program=t.createProgram(),t.attachShader(this.program,c),t.attachShader(this.program,u),t.linkProgram(this.program),!t.getProgramParameter(this.program,t.LINK_STATUS))return console.warn(t.getProgramInfoLog(this.program));t.deleteShader(c),t.deleteShader(u),this.uniformLocations=new Map;let g=t.getProgramParameter(this.program,t.ACTIVE_UNIFORMS);for(let e=0;e<g;e++){let i=t.getActiveUniform(this.program,e);this.uniformLocations.set(i,t.getUniformLocation(this.program,i.name));const s=i.name.match(/(\w+)/g);i.uniformName=s[0],3===s.length?(i.isStructArray=!0,i.structIndex=Number(s[1]),i.structProperty=s[2]):2===s.length&&isNaN(Number(s[1]))&&(i.isStruct=!0,i.structProperty=s[1])}this.attributeLocations=new Map;const m=[],d=t.getProgramParameter(this.program,t.ACTIVE_ATTRIBUTES);for(let e=0;e<d;e++){const i=t.getActiveAttrib(this.program,e),s=t.getAttribLocation(this.program,i.name);-1!==s&&(m[s]=i.name,this.attributeLocations.set(i,s))}this.attributeOrder=m.join("")}setBlendFunc(t,e,i,s){this.blendFunc.src=t,this.blendFunc.dst=e,this.blendFunc.srcAlpha=i,this.blendFunc.dstAlpha=s,t&&(this.transparent=!0)}setBlendEquation(t,e){this.blendEquation.modeRGB=t,this.blendEquation.modeAlpha=e}applyState(){this.depthTest?this.gl.renderer.enable(this.gl.DEPTH_TEST):this.gl.renderer.disable(this.gl.DEPTH_TEST),this.cullFace?this.gl.renderer.enable(this.gl.CULL_FACE):this.gl.renderer.disable(this.gl.CULL_FACE),this.blendFunc.src?this.gl.renderer.enable(this.gl.BLEND):this.gl.renderer.disable(this.gl.BLEND),this.cullFace&&this.gl.renderer.setCullFace(this.cullFace),this.gl.renderer.setFrontFace(this.frontFace),this.gl.renderer.setDepthMask(this.depthWrite),this.gl.renderer.setDepthFunc(this.depthFunc),this.blendFunc.src&&this.gl.renderer.setBlendFunc(this.blendFunc.src,this.blendFunc.dst,this.blendFunc.srcAlpha,this.blendFunc.dstAlpha),this.gl.renderer.setBlendEquation(this.blendEquation.modeRGB,this.blendEquation.modeAlpha)}use({flipFaces:t=!1}={}){let e=-1;this.gl.renderer.state.currentProgram===this.id||(this.gl.useProgram(this.program),this.gl.renderer.state.currentProgram=this.id),this.uniformLocations.forEach(((t,i)=>{let s=i.uniformName,r=this.uniforms[s];if(i.isStruct&&(r=r[i.structProperty],s+=`.${i.structProperty}`),i.isStructArray&&(r=r[i.structIndex][i.structProperty],s+=`[${i.structIndex}].${i.structProperty}`),!r)return E(`Active uniform ${s} has not been supplied`);if(r&&void 0===r.value)return E(`${s} uniform is missing a value parameter`);if(r.value.texture)return e+=1,r.value.update(e),v(this.gl,i.type,t,e);if(r.value.length&&r.value[0].texture){const s=[];return r.value.forEach((t=>{e+=1,t.update(e),s.push(e)})),v(this.gl,i.type,t,s)}v(this.gl,i.type,t,r.value)})),this.applyState(),t&&this.gl.renderer.setFrontFace(this.frontFace===this.gl.CCW?this.gl.CW:this.gl.CCW)}remove(){this.gl.deleteProgram(this.program)}}function v(t,e,i,s){s=s.length?function(t){const e=t.length,i=t[0].length;if(void 0===i)return t;const s=e*i;let r=f[s];r||(f[s]=r=new Float32Array(s));for(let s=0;s<e;s++)r.set(t[s],s*i);return r}(s):s;const r=t.renderer.state.uniformLocations.get(i);if(s.length)if(void 0===r||r.length!==s.length)t.renderer.state.uniformLocations.set(i,s.slice(0));else{if(function(t,e){if(t.length!==e.length)return!1;for(let i=0,s=t.length;i<s;i++)if(t[i]!==e[i])return!1;return!0}(r,s))return;r.set?r.set(s):function(t,e){for(let i=0,s=t.length;i<s;i++)t[i]=e[i]}(r,s),t.renderer.state.uniformLocations.set(i,r)}else{if(r===s)return;t.renderer.state.uniformLocations.set(i,s)}switch(e){case 5126:return s.length?t.uniform1fv(i,s):t.uniform1f(i,s);case 35664:return t.uniform2fv(i,s);case 35665:return t.uniform3fv(i,s);case 35666:return t.uniform4fv(i,s);case 35670:case 5124:case 35678:case 35680:return s.length?t.uniform1iv(i,s):t.uniform1i(i,s);case 35671:case 35667:return t.uniform2iv(i,s);case 35672:case 35668:return t.uniform3iv(i,s);case 35673:case 35669:return t.uniform4iv(i,s);case 35674:return t.uniformMatrix2fv(i,!1,s);case 35675:return t.uniformMatrix3fv(i,!1,s);case 35676:return t.uniformMatrix4fv(i,!1,s)}}function M(t){let e=t.split("\n");for(let t=0;t<e.length;t++)e[t]=t+1+": "+e[t];return e.join("\n")}let w=0;function E(t){w>100||(console.warn(t),w++,w>100&&console.warn("More than 100 program warnings - stopping logs."))}const y=new c;let U=1;class F{constructor({canvas:t=document.createElement("canvas"),width:e=300,height:i=150,dpr:s=1,alpha:r=!1,depth:n=!0,stencil:a=!1,antialias:o=!1,premultipliedAlpha:h=!1,preserveDrawingBuffer:l=!1,powerPreference:c="default",autoClear:u=!0,webgl:g=2}={}){const m={alpha:r,depth:n,stencil:a,antialias:o,premultipliedAlpha:h,preserveDrawingBuffer:l,powerPreference:c};this.dpr=s,this.alpha=r,this.color=!0,this.depth=n,this.stencil=a,this.premultipliedAlpha=h,this.autoClear=u,this.id=U++,2===g&&(this.gl=t.getContext("webgl2",m)),this.isWebgl2=!!this.gl,this.gl||(this.gl=t.getContext("webgl",m)),this.gl||console.error("unable to create webgl context"),this.gl.renderer=this,this.setSize(e,i),this.state={},this.state.blendFunc={src:this.gl.ONE,dst:this.gl.ZERO},this.state.blendEquation={modeRGB:this.gl.FUNC_ADD},this.state.cullFace=null,this.state.frontFace=this.gl.CCW,this.state.depthMask=!0,this.state.depthFunc=this.gl.LESS,this.state.premultiplyAlpha=!1,this.state.flipY=!1,this.state.unpackAlignment=4,this.state.framebuffer=null,this.state.viewport={x:0,y:0,width:null,height:null},this.state.textureUnits=[],this.state.activeTextureUnit=0,this.state.boundBuffer=null,this.state.uniformLocations=new Map,this.state.currentProgram=null,this.extensions={},this.isWebgl2?(this.getExtension("EXT_color_buffer_float"),this.getExtension("OES_texture_float_linear")):(this.getExtension("OES_texture_float"),this.getExtension("OES_texture_float_linear"),this.getExtension("OES_texture_half_float"),this.getExtension("OES_texture_half_float_linear"),this.getExtension("OES_element_index_uint"),this.getExtension("OES_standard_derivatives"),this.getExtension("EXT_sRGB"),this.getExtension("WEBGL_depth_texture"),this.getExtension("WEBGL_draw_buffers")),this.getExtension("WEBGL_compressed_texture_astc"),this.getExtension("EXT_texture_compression_bptc"),this.getExtension("WEBGL_compressed_texture_s3tc"),this.getExtension("WEBGL_compressed_texture_etc1"),this.getExtension("WEBGL_compressed_texture_pvrtc"),this.getExtension("WEBKIT_WEBGL_compressed_texture_pvrtc"),this.vertexAttribDivisor=this.getExtension("ANGLE_instanced_arrays","vertexAttribDivisor","vertexAttribDivisorANGLE"),this.drawArraysInstanced=this.getExtension("ANGLE_instanced_arrays","drawArraysInstanced","drawArraysInstancedANGLE"),this.drawElementsInstanced=this.getExtension("ANGLE_instanced_arrays","drawElementsInstanced","drawElementsInstancedANGLE"),this.createVertexArray=this.getExtension("OES_vertex_array_object","createVertexArray","createVertexArrayOES"),this.bindVertexArray=this.getExtension("OES_vertex_array_object","bindVertexArray","bindVertexArrayOES"),this.deleteVertexArray=this.getExtension("OES_vertex_array_object","deleteVertexArray","deleteVertexArrayOES"),this.drawBuffers=this.getExtension("WEBGL_draw_buffers","drawBuffers","drawBuffersWEBGL"),this.parameters={},this.parameters.maxTextureUnits=this.gl.getParameter(this.gl.MAX_COMBINED_TEXTURE_IMAGE_UNITS),this.parameters.maxAnisotropy=this.getExtension("EXT_texture_filter_anisotropic")?this.gl.getParameter(this.getExtension("EXT_texture_filter_anisotropic").MAX_TEXTURE_MAX_ANISOTROPY_EXT):0}setSize(t,e){this.width=t,this.height=e,this.gl.canvas.width=t*this.dpr,this.gl.canvas.height=e*this.dpr,Object.assign(this.gl.canvas.style,{width:t+"px",height:e+"px"})}setViewport(t,e,i=0,s=0){this.state.viewport.width===t&&this.state.viewport.height===e||(this.state.viewport.width=t,this.state.viewport.height=e,this.state.viewport.x=i,this.state.viewport.y=s,this.gl.viewport(i,s,t,e))}setScissor(t,e,i=0,s=0){this.gl.scissor(i,s,t,e)}enable(t){!0!==this.state[t]&&(this.gl.enable(t),this.state[t]=!0)}disable(t){!1!==this.state[t]&&(this.gl.disable(t),this.state[t]=!1)}setBlendFunc(t,e,i,s){this.state.blendFunc.src===t&&this.state.blendFunc.dst===e&&this.state.blendFunc.srcAlpha===i&&this.state.blendFunc.dstAlpha===s||(this.state.blendFunc.src=t,this.state.blendFunc.dst=e,this.state.blendFunc.srcAlpha=i,this.state.blendFunc.dstAlpha=s,void 0!==i?this.gl.blendFuncSeparate(t,e,i,s):this.gl.blendFunc(t,e))}setBlendEquation(t,e){t=t||this.gl.FUNC_ADD,this.state.blendEquation.modeRGB===t&&this.state.blendEquation.modeAlpha===e||(this.state.blendEquation.modeRGB=t,this.state.blendEquation.modeAlpha=e,void 0!==e?this.gl.blendEquationSeparate(t,e):this.gl.blendEquation(t))}setCullFace(t){this.state.cullFace!==t&&(this.state.cullFace=t,this.gl.cullFace(t))}setFrontFace(t){this.state.frontFace!==t&&(this.state.frontFace=t,this.gl.frontFace(t))}setDepthMask(t){this.state.depthMask!==t&&(this.state.depthMask=t,this.gl.depthMask(t))}setDepthFunc(t){this.state.depthFunc!==t&&(this.state.depthFunc=t,this.gl.depthFunc(t))}activeTexture(t){this.state.activeTextureUnit!==t&&(this.state.activeTextureUnit=t,this.gl.activeTexture(this.gl.TEXTURE0+t))}bindFramebuffer({target:t=this.gl.FRAMEBUFFER,buffer:e=null}={}){this.state.framebuffer!==e&&(this.state.framebuffer=e,this.gl.bindFramebuffer(t,e))}getExtension(t,e,i){return e&&this.gl[e]?this.gl[e].bind(this.gl):(this.extensions[t]||(this.extensions[t]=this.gl.getExtension(t)),e?this.extensions[t]?this.extensions[t][i].bind(this.extensions[t]):null:this.extensions[t])}sortOpaque(t,e){return t.renderOrder!==e.renderOrder?t.renderOrder-e.renderOrder:t.program.id!==e.program.id?t.program.id-e.program.id:t.zDepth!==e.zDepth?t.zDepth-e.zDepth:e.id-t.id}sortTransparent(t,e){return t.renderOrder!==e.renderOrder?t.renderOrder-e.renderOrder:t.zDepth!==e.zDepth?e.zDepth-t.zDepth:e.id-t.id}sortUI(t,e){return t.renderOrder!==e.renderOrder?t.renderOrder-e.renderOrder:t.program.id!==e.program.id?t.program.id-e.program.id:e.id-t.id}getRenderList({scene:t,camera:e,frustumCull:i,sort:s}){let r=[];if(e&&i&&e.updateFrustum(),t.traverse((t=>{if(!t.visible)return!0;t.draw&&(i&&t.frustumCulled&&e&&!e.frustumIntersectsMesh(t)||r.push(t))})),s){const t=[],i=[],s=[];r.forEach((r=>{r.program.transparent?r.program.depthTest?i.push(r):s.push(r):t.push(r),r.zDepth=0,0===r.renderOrder&&r.program.depthTest&&e&&(r.worldMatrix.getTranslation(y),y.applyMatrix4(e.projectionViewMatrix),r.zDepth=y.z)})),t.sort(this.sortOpaque),i.sort(this.sortTransparent),s.sort(this.sortUI),r=t.concat(i,s)}return r}render({scene:t,camera:e,target:i=null,update:s=!0,sort:r=!0,frustumCull:n=!0,clear:a}){null===i?(this.bindFramebuffer(),this.setViewport(this.width*this.dpr,this.height*this.dpr)):(this.bindFramebuffer(i),this.setViewport(i.width,i.height)),(a||this.autoClear&&!1!==a)&&(!this.depth||i&&!i.depth||(this.enable(this.gl.DEPTH_TEST),this.setDepthMask(!0)),this.gl.clear((this.color?this.gl.COLOR_BUFFER_BIT:0)|(this.depth?this.gl.DEPTH_BUFFER_BIT:0)|(this.stencil?this.gl.STENCIL_BUFFER_BIT:0))),s&&t.updateMatrixWorld(),e&&e.updateMatrixWorld();this.getRenderList({scene:t,camera:e,frustumCull:n,sort:r}).forEach((t=>{t.draw({camera:e})}))}}function R(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}function C(t,e,i,s,r){return t[0]=e,t[1]=i,t[2]=s,t[3]=r,t}function b(t,e){let i=e[0],s=e[1],r=e[2],n=e[3],a=i*i+s*s+r*r+n*n;return a>0&&(a=1/Math.sqrt(a)),t[0]=i*a,t[1]=s*a,t[2]=r*a,t[3]=n*a,t}function T(t,e){return t[0]*e[0]+t[1]*e[1]+t[2]*e[2]+t[3]*e[3]}function G(t,e,i){let s=e[0],r=e[1],n=e[2],a=e[3],o=i[0],h=i[1],l=i[2],c=i[3];return t[0]=s*c+a*o+r*l-n*h,t[1]=r*c+a*h+n*o-s*l,t[2]=n*c+a*l+s*h-r*o,t[3]=a*c-s*o-r*h-n*l,t}const I=R,S=C,Y=T,B=b;class K extends Array{constructor(t=0,e=0,i=0,s=1){return super(t,e,i,s),this.onChange=()=>{},this}get x(){return this[0]}get y(){return this[1]}get z(){return this[2]}get w(){return this[3]}set x(t){this[0]=t,this.onChange()}set y(t){this[1]=t,this.onChange()}set z(t){this[2]=t,this.onChange()}set w(t){this[3]=t,this.onChange()}identity(){var t;return(t=this)[0]=0,t[1]=0,t[2]=0,t[3]=1,this.onChange(),this}set(t,e,i,s){return t.length?this.copy(t):(S(this,t,e,i,s),this.onChange(),this)}rotateX(t){return function(t,e,i){i*=.5;let s=e[0],r=e[1],n=e[2],a=e[3],o=Math.sin(i),h=Math.cos(i);t[0]=s*h+a*o,t[1]=r*h+n*o,t[2]=n*h-r*o,t[3]=a*h-s*o}(this,this,t),this.onChange(),this}rotateY(t){return function(t,e,i){i*=.5;let s=e[0],r=e[1],n=e[2],a=e[3],o=Math.sin(i),h=Math.cos(i);t[0]=s*h-n*o,t[1]=r*h+a*o,t[2]=n*h+s*o,t[3]=a*h-r*o}(this,this,t),this.onChange(),this}rotateZ(t){return function(t,e,i){i*=.5;let s=e[0],r=e[1],n=e[2],a=e[3],o=Math.sin(i),h=Math.cos(i);t[0]=s*h+r*o,t[1]=r*h-s*o,t[2]=n*h+a*o,t[3]=a*h-n*o}(this,this,t),this.onChange(),this}inverse(t=this){return function(t,e){let i=e[0],s=e[1],r=e[2],n=e[3],a=i*i+s*s+r*r+n*n,o=a?1/a:0;t[0]=-i*o,t[1]=-s*o,t[2]=-r*o,t[3]=n*o}(this,t),this.onChange(),this}conjugate(t=this){var e,i;return i=t,(e=this)[0]=-i[0],e[1]=-i[1],e[2]=-i[2],e[3]=i[3],this.onChange(),this}copy(t){return I(this,t),this.onChange(),this}normalize(t=this){return B(this,t),this.onChange(),this}multiply(t,e){return e?G(this,t,e):G(this,this,t),this.onChange(),this}dot(t){return Y(this,t)}fromMatrix3(t){return function(t,e){let i,s=e[0]+e[4]+e[8];if(s>0)i=Math.sqrt(s+1),t[3]=.5*i,i=.5/i,t[0]=(e[5]-e[7])*i,t[1]=(e[6]-e[2])*i,t[2]=(e[1]-e[3])*i;else{let s=0;e[4]>e[0]&&(s=1),e[8]>e[3*s+s]&&(s=2);let r=(s+1)%3,n=(s+2)%3;i=Math.sqrt(e[3*s+s]-e[3*r+r]-e[3*n+n]+1),t[s]=.5*i,i=.5/i,t[3]=(e[3*r+n]-e[3*n+r])*i,t[r]=(e[3*r+s]+e[3*s+r])*i,t[n]=(e[3*n+s]+e[3*s+n])*i}}(this,t),this.onChange(),this}fromEuler(t){return function(t,e,i="YXZ"){let s=Math.sin(.5*e[0]),r=Math.cos(.5*e[0]),n=Math.sin(.5*e[1]),a=Math.cos(.5*e[1]),o=Math.sin(.5*e[2]),h=Math.cos(.5*e[2]);"XYZ"===i?(t[0]=s*a*h+r*n*o,t[1]=r*n*h-s*a*o,t[2]=r*a*o+s*n*h,t[3]=r*a*h-s*n*o):"YXZ"===i?(t[0]=s*a*h+r*n*o,t[1]=r*n*h-s*a*o,t[2]=r*a*o-s*n*h,t[3]=r*a*h+s*n*o):"ZXY"===i?(t[0]=s*a*h-r*n*o,t[1]=r*n*h+s*a*o,t[2]=r*a*o+s*n*h,t[3]=r*a*h-s*n*o):"ZYX"===i?(t[0]=s*a*h-r*n*o,t[1]=r*n*h+s*a*o,t[2]=r*a*o-s*n*h,t[3]=r*a*h+s*n*o):"YZX"===i?(t[0]=s*a*h+r*n*o,t[1]=r*n*h+s*a*o,t[2]=r*a*o-s*n*h,t[3]=r*a*h-s*n*o):"XZY"===i&&(t[0]=s*a*h-r*n*o,t[1]=r*n*h-s*a*o,t[2]=r*a*o+s*n*h,t[3]=r*a*h+s*n*o)}(this,t,t.order),this}fromAxisAngle(t,e){return function(t,e,i){i*=.5;let s=Math.sin(i);t[0]=s*e[0],t[1]=s*e[1],t[2]=s*e[2],t[3]=Math.cos(i)}(this,t,e),this}slerp(t,e){return function(t,e,i,s){let r,n,a,o,h,l=e[0],c=e[1],u=e[2],g=e[3],m=i[0],d=i[1],p=i[2],x=i[3];n=l*m+c*d+u*p+g*x,n<0&&(n=-n,m=-m,d=-d,p=-p,x=-x),1-n>1e-6?(r=Math.acos(n),a=Math.sin(r),o=Math.sin((1-s)*r)/a,h=Math.sin(s*r)/a):(o=1-s,h=s),t[0]=o*l+h*m,t[1]=o*c+h*d,t[2]=o*u+h*p,t[3]=o*g+h*x}(this,this,t,e),this}fromArray(t,e=0){return this[0]=t[e],this[1]=t[e+1],this[2]=t[e+2],this[3]=t[e+3],this}toArray(t=[],e=0){return t[e]=this[0],t[e+1]=this[1],t[e+2]=this[2],t[e+3]=this[3],t}}function D(t,e,i){let s=e[0],r=e[1],n=e[2],a=e[3],o=e[4],h=e[5],l=e[6],c=e[7],u=e[8],g=e[9],m=e[10],d=e[11],p=e[12],x=e[13],f=e[14],A=e[15],v=i[0],M=i[1],w=i[2],E=i[3];return t[0]=v*s+M*o+w*u+E*p,t[1]=v*r+M*h+w*g+E*x,t[2]=v*n+M*l+w*m+E*f,t[3]=v*a+M*c+w*d+E*A,v=i[4],M=i[5],w=i[6],E=i[7],t[4]=v*s+M*o+w*u+E*p,t[5]=v*r+M*h+w*g+E*x,t[6]=v*n+M*l+w*m+E*f,t[7]=v*a+M*c+w*d+E*A,v=i[8],M=i[9],w=i[10],E=i[11],t[8]=v*s+M*o+w*u+E*p,t[9]=v*r+M*h+w*g+E*x,t[10]=v*n+M*l+w*m+E*f,t[11]=v*a+M*c+w*d+E*A,v=i[12],M=i[13],w=i[14],E=i[15],t[12]=v*s+M*o+w*u+E*p,t[13]=v*r+M*h+w*g+E*x,t[14]=v*n+M*l+w*m+E*f,t[15]=v*a+M*c+w*d+E*A,t}function k(t,e){let i=e[0],s=e[1],r=e[2],n=e[4],a=e[5],o=e[6],h=e[8],l=e[9],c=e[10];return t[0]=Math.hypot(i,s,r),t[1]=Math.hypot(n,a,o),t[2]=Math.hypot(h,l,c),t}const Q=function(){const t=[0,0,0];return function(e,i){let s=t;k(s,i);let r=1/s[0],n=1/s[1],a=1/s[2],o=i[0]*r,h=i[1]*n,l=i[2]*a,c=i[4]*r,u=i[5]*n,g=i[6]*a,m=i[8]*r,d=i[9]*n,p=i[10]*a,x=o+u+p,f=0;return x>0?(f=2*Math.sqrt(x+1),e[3]=.25*f,e[0]=(g-d)/f,e[1]=(m-l)/f,e[2]=(h-c)/f):o>u&&o>p?(f=2*Math.sqrt(1+o-u-p),e[3]=(g-d)/f,e[0]=.25*f,e[1]=(h+c)/f,e[2]=(m+l)/f):u>p?(f=2*Math.sqrt(1+u-o-p),e[3]=(m-l)/f,e[0]=(h+c)/f,e[1]=.25*f,e[2]=(g+d)/f):(f=2*Math.sqrt(1+p-o-u),e[3]=(h-c)/f,e[0]=(m+l)/f,e[1]=(g+d)/f,e[2]=.25*f),e}}();class N extends Array{constructor(t=1,e=0,i=0,s=0,r=0,n=1,a=0,o=0,h=0,l=0,c=1,u=0,g=0,m=0,d=0,p=1){return super(t,e,i,s,r,n,a,o,h,l,c,u,g,m,d,p),this}get x(){return this[12]}get y(){return this[13]}get z(){return this[14]}get w(){return this[15]}set x(t){this[12]=t}set y(t){this[13]=t}set z(t){this[14]=t}set w(t){this[15]=t}set(t,e,i,s,r,n,a,o,h,l,c,u,g,m,d,p){return t.length?this.copy(t):(function(t,e,i,s,r,n,a,o,h,l,c,u,g,m,d,p,x){t[0]=e,t[1]=i,t[2]=s,t[3]=r,t[4]=n,t[5]=a,t[6]=o,t[7]=h,t[8]=l,t[9]=c,t[10]=u,t[11]=g,t[12]=m,t[13]=d,t[14]=p,t[15]=x}(this,t,e,i,s,r,n,a,o,h,l,c,u,g,m,d,p),this)}translate(t,e=this){return function(t,e,i){let s,r,n,a,o,h,l,c,u,g,m,d,p=i[0],x=i[1],f=i[2];e===t?(t[12]=e[0]*p+e[4]*x+e[8]*f+e[12],t[13]=e[1]*p+e[5]*x+e[9]*f+e[13],t[14]=e[2]*p+e[6]*x+e[10]*f+e[14],t[15]=e[3]*p+e[7]*x+e[11]*f+e[15]):(s=e[0],r=e[1],n=e[2],a=e[3],o=e[4],h=e[5],l=e[6],c=e[7],u=e[8],g=e[9],m=e[10],d=e[11],t[0]=s,t[1]=r,t[2]=n,t[3]=a,t[4]=o,t[5]=h,t[6]=l,t[7]=c,t[8]=u,t[9]=g,t[10]=m,t[11]=d,t[12]=s*p+o*x+u*f+e[12],t[13]=r*p+h*x+g*f+e[13],t[14]=n*p+l*x+m*f+e[14],t[15]=a*p+c*x+d*f+e[15])}(this,e,t),this}rotate(t,e,i=this){return function(t,e,i,s){let r,n,a,o,h,l,c,u,g,m,d,p,x,f,A,v,M,w,E,y,U,F,R,C,b=s[0],T=s[1],G=s[2],I=Math.hypot(b,T,G);Math.abs(I)<1e-6||(I=1/I,b*=I,T*=I,G*=I,r=Math.sin(i),n=Math.cos(i),a=1-n,o=e[0],h=e[1],l=e[2],c=e[3],u=e[4],g=e[5],m=e[6],d=e[7],p=e[8],x=e[9],f=e[10],A=e[11],v=b*b*a+n,M=T*b*a+G*r,w=G*b*a-T*r,E=b*T*a-G*r,y=T*T*a+n,U=G*T*a+b*r,F=b*G*a+T*r,R=T*G*a-b*r,C=G*G*a+n,t[0]=o*v+u*M+p*w,t[1]=h*v+g*M+x*w,t[2]=l*v+m*M+f*w,t[3]=c*v+d*M+A*w,t[4]=o*E+u*y+p*U,t[5]=h*E+g*y+x*U,t[6]=l*E+m*y+f*U,t[7]=c*E+d*y+A*U,t[8]=o*F+u*R+p*C,t[9]=h*F+g*R+x*C,t[10]=l*F+m*R+f*C,t[11]=c*F+d*R+A*C,e!==t&&(t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15]))}(this,i,t,e),this}scale(t,e=this){return function(t,e,i){let s=i[0],r=i[1],n=i[2];t[0]=e[0]*s,t[1]=e[1]*s,t[2]=e[2]*s,t[3]=e[3]*s,t[4]=e[4]*r,t[5]=e[5]*r,t[6]=e[6]*r,t[7]=e[7]*r,t[8]=e[8]*n,t[9]=e[9]*n,t[10]=e[10]*n,t[11]=e[11]*n,t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15]}(this,e,"number"==typeof t?[t,t,t]:t),this}multiply(t,e){return e?D(this,t,e):D(this,this,t),this}identity(){var t;return(t=this)[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=1,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=1,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,this}copy(t){var e,i;return i=t,(e=this)[0]=i[0],e[1]=i[1],e[2]=i[2],e[3]=i[3],e[4]=i[4],e[5]=i[5],e[6]=i[6],e[7]=i[7],e[8]=i[8],e[9]=i[9],e[10]=i[10],e[11]=i[11],e[12]=i[12],e[13]=i[13],e[14]=i[14],e[15]=i[15],this}fromPerspective({fov:t,aspect:e,near:i,far:s}={}){return function(t,e,i,s,r){let n=1/Math.tan(e/2),a=1/(s-r);t[0]=n/i,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=n,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=(r+s)*a,t[11]=-1,t[12]=0,t[13]=0,t[14]=2*r*s*a,t[15]=0}(this,t,e,i,s),this}fromOrthogonal({left:t,right:e,bottom:i,top:s,near:r,far:n}){return function(t,e,i,s,r,n,a){let o=1/(e-i),h=1/(s-r),l=1/(n-a);t[0]=-2*o,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=-2*h,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=2*l,t[11]=0,t[12]=(e+i)*o,t[13]=(r+s)*h,t[14]=(a+n)*l,t[15]=1}(this,t,e,i,s,r,n),this}fromQuaternion(t){return function(t,e){let i=e[0],s=e[1],r=e[2],n=e[3],a=i+i,o=s+s,h=r+r,l=i*a,c=s*a,u=s*o,g=r*a,m=r*o,d=r*h,p=n*a,x=n*o,f=n*h;t[0]=1-u-d,t[1]=c+f,t[2]=g-x,t[3]=0,t[4]=c-f,t[5]=1-l-d,t[6]=m+p,t[7]=0,t[8]=g+x,t[9]=m-p,t[10]=1-l-u,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1}(this,t),this}setPosition(t){return this.x=t[0],this.y=t[1],this.z=t[2],this}inverse(t=this){return function(t,e){let i=e[0],s=e[1],r=e[2],n=e[3],a=e[4],o=e[5],h=e[6],l=e[7],c=e[8],u=e[9],g=e[10],m=e[11],d=e[12],p=e[13],x=e[14],f=e[15],A=i*o-s*a,v=i*h-r*a,M=i*l-n*a,w=s*h-r*o,E=s*l-n*o,y=r*l-n*h,U=c*p-u*d,F=c*x-g*d,R=c*f-m*d,C=u*x-g*p,b=u*f-m*p,T=g*f-m*x,G=A*T-v*b+M*C+w*R-E*F+y*U;G&&(G=1/G,t[0]=(o*T-h*b+l*C)*G,t[1]=(r*b-s*T-n*C)*G,t[2]=(p*y-x*E+f*w)*G,t[3]=(g*E-u*y-m*w)*G,t[4]=(h*R-a*T-l*F)*G,t[5]=(i*T-r*R+n*F)*G,t[6]=(x*M-d*y-f*v)*G,t[7]=(c*y-g*M+m*v)*G,t[8]=(a*b-o*R+l*U)*G,t[9]=(s*R-i*b-n*U)*G,t[10]=(d*E-p*M+f*A)*G,t[11]=(u*M-c*E-m*A)*G,t[12]=(o*F-a*C-h*U)*G,t[13]=(i*C-s*F+r*U)*G,t[14]=(p*v-d*w-x*A)*G,t[15]=(c*w-u*v+g*A)*G)}(this,t),this}compose(t,e,i){return function(t,e,i,s){let r=e[0],n=e[1],a=e[2],o=e[3],h=r+r,l=n+n,c=a+a,u=r*h,g=r*l,m=r*c,d=n*l,p=n*c,x=a*c,f=o*h,A=o*l,v=o*c,M=s[0],w=s[1],E=s[2];t[0]=(1-(d+x))*M,t[1]=(g+v)*M,t[2]=(m-A)*M,t[3]=0,t[4]=(g-v)*w,t[5]=(1-(u+x))*w,t[6]=(p+f)*w,t[7]=0,t[8]=(m+A)*E,t[9]=(p-f)*E,t[10]=(1-(u+d))*E,t[11]=0,t[12]=i[0],t[13]=i[1],t[14]=i[2],t[15]=1}(this,t,e,i),this}getRotation(t){return Q(t,this),this}getTranslation(t){var e,i;return i=this,(e=t)[0]=i[12],e[1]=i[13],e[2]=i[14],this}getScaling(t){return k(t,this),this}getMaxScaleOnAxis(){return function(t){let e=t[0],i=t[1],s=t[2],r=t[4],n=t[5],a=t[6],o=t[8],h=t[9],l=t[10];const c=e*e+i*i+s*s,u=r*r+n*n+a*a,g=o*o+h*h+l*l;return Math.sqrt(Math.max(c,u,g))}(this)}lookAt(t,e,i){return function(t,e,i,s){let r=e[0],n=e[1],a=e[2],o=s[0],h=s[1],l=s[2],c=r-i[0],u=n-i[1],g=a-i[2],m=c*c+u*u+g*g;0===m?g=1:(m=1/Math.sqrt(m),c*=m,u*=m,g*=m);let d=h*g-l*u,p=l*c-o*g,x=o*u-h*c;m=d*d+p*p+x*x,0===m&&(l?o+=1e-6:h?l+=1e-6:h+=1e-6,d=h*g-l*u,p=l*c-o*g,x=o*u-h*c,m=d*d+p*p+x*x),m=1/Math.sqrt(m),d*=m,p*=m,x*=m,t[0]=d,t[1]=p,t[2]=x,t[3]=0,t[4]=u*x-g*p,t[5]=g*d-c*x,t[6]=c*p-u*d,t[7]=0,t[8]=c,t[9]=u,t[10]=g,t[11]=0,t[12]=r,t[13]=n,t[14]=a,t[15]=1}(this,t,e,i),this}determinant(){return function(t){let e=t[0],i=t[1],s=t[2],r=t[3],n=t[4],a=t[5],o=t[6],h=t[7],l=t[8],c=t[9],u=t[10],g=t[11],m=t[12],d=t[13],p=t[14],x=t[15];return(e*a-i*n)*(u*x-g*p)-(e*o-s*n)*(c*x-g*d)+(e*h-r*n)*(c*p-u*d)+(i*o-s*a)*(l*x-g*m)-(i*h-r*a)*(l*p-u*m)+(s*h-r*o)*(l*d-c*m)}(this)}fromArray(t,e=0){return this[0]=t[e],this[1]=t[e+1],this[2]=t[e+2],this[3]=t[e+3],this[4]=t[e+4],this[5]=t[e+5],this[6]=t[e+6],this[7]=t[e+7],this[8]=t[e+8],this[9]=t[e+9],this[10]=t[e+10],this[11]=t[e+11],this[12]=t[e+12],this[13]=t[e+13],this[14]=t[e+14],this[15]=t[e+15],this}toArray(t=[],e=0){return t[e]=this[0],t[e+1]=this[1],t[e+2]=this[2],t[e+3]=this[3],t[e+4]=this[4],t[e+5]=this[5],t[e+6]=this[6],t[e+7]=this[7],t[e+8]=this[8],t[e+9]=this[9],t[e+10]=this[10],t[e+11]=this[11],t[e+12]=this[12],t[e+13]=this[13],t[e+14]=this[14],t[e+15]=this[15],t}}const j=new N;class P extends Array{constructor(t=0,e=t,i=t,s="YXZ"){return super(t,e,i),this.order=s,this.onChange=()=>{},this}get x(){return this[0]}get y(){return this[1]}get z(){return this[2]}set x(t){this[0]=t,this.onChange()}set y(t){this[1]=t,this.onChange()}set z(t){this[2]=t,this.onChange()}set(t,e=t,i=t){return t.length?this.copy(t):(this[0]=t,this[1]=e,this[2]=i,this.onChange(),this)}copy(t){return this[0]=t[0],this[1]=t[1],this[2]=t[2],this.onChange(),this}reorder(t){return this.order=t,this.onChange(),this}fromRotationMatrix(t,e=this.order){return function(t,e,i="YXZ"){"XYZ"===i?(t[1]=Math.asin(Math.min(Math.max(e[8],-1),1)),Math.abs(e[8])<.99999?(t[0]=Math.atan2(-e[9],e[10]),t[2]=Math.atan2(-e[4],e[0])):(t[0]=Math.atan2(e[6],e[5]),t[2]=0)):"YXZ"===i?(t[0]=Math.asin(-Math.min(Math.max(e[9],-1),1)),Math.abs(e[9])<.99999?(t[1]=Math.atan2(e[8],e[10]),t[2]=Math.atan2(e[1],e[5])):(t[1]=Math.atan2(-e[2],e[0]),t[2]=0)):"ZXY"===i?(t[0]=Math.asin(Math.min(Math.max(e[6],-1),1)),Math.abs(e[6])<.99999?(t[1]=Math.atan2(-e[2],e[10]),t[2]=Math.atan2(-e[4],e[5])):(t[1]=0,t[2]=Math.atan2(e[1],e[0]))):"ZYX"===i?(t[1]=Math.asin(-Math.min(Math.max(e[2],-1),1)),Math.abs(e[2])<.99999?(t[0]=Math.atan2(e[6],e[10]),t[2]=Math.atan2(e[1],e[0])):(t[0]=0,t[2]=Math.atan2(-e[4],e[5]))):"YZX"===i?(t[2]=Math.asin(Math.min(Math.max(e[1],-1),1)),Math.abs(e[1])<.99999?(t[0]=Math.atan2(-e[9],e[5]),t[1]=Math.atan2(-e[2],e[0])):(t[0]=0,t[1]=Math.atan2(e[8],e[10]))):"XZY"===i&&(t[2]=Math.asin(-Math.min(Math.max(e[4],-1),1)),Math.abs(e[4])<.99999?(t[0]=Math.atan2(e[6],e[5]),t[1]=Math.atan2(e[8],e[0])):(t[0]=Math.atan2(-e[9],e[10]),t[1]=0))}(this,t,e),this}fromQuaternion(t,e=this.order){return j.fromQuaternion(t),this.fromRotationMatrix(j,e)}toArray(t=[],e=0){return t[e]=this[0],t[e+1]=this[1],t[e+2]=this[2],t}}class O{constructor(){this.parent=null,this.children=[],this.visible=!0,this.matrix=new N,this.worldMatrix=new N,this.matrixAutoUpdate=!0,this.position=new c,this.quaternion=new K,this.scale=new c(1),this.rotation=new P,this.up=new c(0,1,0),this.rotation.onChange=()=>this.quaternion.fromEuler(this.rotation),this.quaternion.onChange=()=>this.rotation.fromQuaternion(this.quaternion)}setParent(t,e=!0){this.parent&&t!==this.parent&&this.parent.removeChild(this,!1),this.parent=t,e&&t&&t.addChild(this,!1)}addChild(t,e=!0){~this.children.indexOf(t)||this.children.push(t),e&&t.setParent(this,!1)}removeChild(t,e=!0){~this.children.indexOf(t)&&this.children.splice(this.children.indexOf(t),1),e&&t.setParent(null,!1)}updateMatrixWorld(t){this.matrixAutoUpdate&&this.updateMatrix(),(this.worldMatrixNeedsUpdate||t)&&(null===this.parent?this.worldMatrix.copy(this.matrix):this.worldMatrix.multiply(this.parent.worldMatrix,this.matrix),this.worldMatrixNeedsUpdate=!1,t=!0);for(let e=0,i=this.children.length;e<i;e++)this.children[e].updateMatrixWorld(t)}updateMatrix(){this.matrix.compose(this.quaternion,this.position,this.scale),this.worldMatrixNeedsUpdate=!0}traverse(t){if(!t(this))for(let e=0,i=this.children.length;e<i;e++)this.children[e].traverse(t)}decompose(){this.matrix.getTranslation(this.position),this.matrix.getRotation(this.quaternion),this.matrix.getScaling(this.scale),this.rotation.fromQuaternion(this.quaternion)}lookAt(t,e=!1){e?this.matrix.lookAt(this.position,t,this.up):this.matrix.lookAt(t,this.position,this.up),this.matrix.getRotation(this.quaternion),this.rotation.fromQuaternion(this.quaternion)}}const z=new N,V=new c,Z=new c;class J extends O{constructor(t,{near:e=.1,far:i=100,fov:s=45,aspect:r=1,left:n,right:a,bottom:o,top:h,zoom:l=1}={}){super(),Object.assign(this,{near:e,far:i,fov:s,aspect:r,left:n,right:a,bottom:o,top:h,zoom:l}),this.projectionMatrix=new N,this.viewMatrix=new N,this.projectionViewMatrix=new N,this.worldPosition=new c,this.type=n||a?"orthographic":"perspective","orthographic"===this.type?this.orthographic():this.perspective()}perspective({near:t=this.near,far:e=this.far,fov:i=this.fov,aspect:s=this.aspect}={}){return Object.assign(this,{near:t,far:e,fov:i,aspect:s}),this.projectionMatrix.fromPerspective({fov:i*(Math.PI/180),aspect:s,near:t,far:e}),this.type="perspective",this}orthographic({near:t=this.near,far:e=this.far,left:i=this.left,right:s=this.right,bottom:r=this.bottom,top:n=this.top,zoom:a=this.zoom}={}){return Object.assign(this,{near:t,far:e,left:i,right:s,bottom:r,top:n,zoom:a}),i/=a,s/=a,r/=a,n/=a,this.projectionMatrix.fromOrthogonal({left:i,right:s,bottom:r,top:n,near:t,far:e}),this.type="orthographic",this}updateMatrixWorld(){return super.updateMatrixWorld(),this.viewMatrix.inverse(this.worldMatrix),this.worldMatrix.getTranslation(this.worldPosition),this.projectionViewMatrix.multiply(this.projectionMatrix,this.viewMatrix),this}lookAt(t){return super.lookAt(t,!0),this}project(t){return t.applyMatrix4(this.viewMatrix),t.applyMatrix4(this.projectionMatrix),this}unproject(t){return t.applyMatrix4(z.inverse(this.projectionMatrix)),t.applyMatrix4(this.worldMatrix),this}updateFrustum(){this.frustum||(this.frustum=[new c,new c,new c,new c,new c,new c]);const t=this.projectionViewMatrix;this.frustum[0].set(t[3]-t[0],t[7]-t[4],t[11]-t[8]).constant=t[15]-t[12],this.frustum[1].set(t[3]+t[0],t[7]+t[4],t[11]+t[8]).constant=t[15]+t[12],this.frustum[2].set(t[3]+t[1],t[7]+t[5],t[11]+t[9]).constant=t[15]+t[13],this.frustum[3].set(t[3]-t[1],t[7]-t[5],t[11]-t[9]).constant=t[15]-t[13],this.frustum[4].set(t[3]-t[2],t[7]-t[6],t[11]-t[10]).constant=t[15]-t[14],this.frustum[5].set(t[3]+t[2],t[7]+t[6],t[11]+t[10]).constant=t[15]+t[14];for(let t=0;t<6;t++){const e=1/this.frustum[t].distance();this.frustum[t].multiply(e),this.frustum[t].constant*=e}}frustumIntersectsMesh(t){if(!t.geometry.attributes.position)return!0;if(t.geometry.bounds&&t.geometry.bounds.radius!==1/0||t.geometry.computeBoundingSphere(),!t.geometry.bounds)return!0;const e=V;e.copy(t.geometry.bounds.center),e.applyMatrix4(t.worldMatrix);const i=t.geometry.bounds.radius*t.worldMatrix.getMaxScaleOnAxis();return this.frustumIntersectsSphere(e,i)}frustumIntersectsSphere(t,e){const i=Z;for(let s=0;s<6;s++){const r=this.frustum[s];if(i.copy(r).dot(t)+r.constant<-e)return!1}return!0}}function L(t,e,i){let s=e[0],r=e[1],n=e[2],a=e[3],o=e[4],h=e[5],l=e[6],c=e[7],u=e[8],g=i[0],m=i[1],d=i[2],p=i[3],x=i[4],f=i[5],A=i[6],v=i[7],M=i[8];return t[0]=g*s+m*a+d*l,t[1]=g*r+m*o+d*c,t[2]=g*n+m*h+d*u,t[3]=p*s+x*a+f*l,t[4]=p*r+x*o+f*c,t[5]=p*n+x*h+f*u,t[6]=A*s+v*a+M*l,t[7]=A*r+v*o+M*c,t[8]=A*n+v*h+M*u,t}class H extends Array{constructor(t=1,e=0,i=0,s=0,r=1,n=0,a=0,o=0,h=1){return super(t,e,i,s,r,n,a,o,h),this}set(t,e,i,s,r,n,a,o,h){return t.length?this.copy(t):(function(t,e,i,s,r,n,a,o,h,l){t[0]=e,t[1]=i,t[2]=s,t[3]=r,t[4]=n,t[5]=a,t[6]=o,t[7]=h,t[8]=l}(this,t,e,i,s,r,n,a,o,h),this)}translate(t,e=this){return function(t,e,i){let s=e[0],r=e[1],n=e[2],a=e[3],o=e[4],h=e[5],l=e[6],c=e[7],u=e[8],g=i[0],m=i[1];t[0]=s,t[1]=r,t[2]=n,t[3]=a,t[4]=o,t[5]=h,t[6]=g*s+m*a+l,t[7]=g*r+m*o+c,t[8]=g*n+m*h+u}(this,e,t),this}rotate(t,e=this){return function(t,e,i){let s=e[0],r=e[1],n=e[2],a=e[3],o=e[4],h=e[5],l=e[6],c=e[7],u=e[8],g=Math.sin(i),m=Math.cos(i);t[0]=m*s+g*a,t[1]=m*r+g*o,t[2]=m*n+g*h,t[3]=m*a-g*s,t[4]=m*o-g*r,t[5]=m*h-g*n,t[6]=l,t[7]=c,t[8]=u}(this,e,t),this}scale(t,e=this){return function(t,e,i){let s=i[0],r=i[1];t[0]=s*e[0],t[1]=s*e[1],t[2]=s*e[2],t[3]=r*e[3],t[4]=r*e[4],t[5]=r*e[5],t[6]=e[6],t[7]=e[7],t[8]=e[8]}(this,e,t),this}multiply(t,e){return e?L(this,t,e):L(this,this,t),this}identity(){var t;return(t=this)[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=1,t[5]=0,t[6]=0,t[7]=0,t[8]=1,this}copy(t){var e,i;return i=t,(e=this)[0]=i[0],e[1]=i[1],e[2]=i[2],e[3]=i[3],e[4]=i[4],e[5]=i[5],e[6]=i[6],e[7]=i[7],e[8]=i[8],this}fromMatrix4(t){var e,i;return i=t,(e=this)[0]=i[0],e[1]=i[1],e[2]=i[2],e[3]=i[4],e[4]=i[5],e[5]=i[6],e[6]=i[8],e[7]=i[9],e[8]=i[10],this}fromQuaternion(t){return function(t,e){let i=e[0],s=e[1],r=e[2],n=e[3],a=i+i,o=s+s,h=r+r,l=i*a,c=s*a,u=s*o,g=r*a,m=r*o,d=r*h,p=n*a,x=n*o,f=n*h;t[0]=1-u-d,t[3]=c-f,t[6]=g+x,t[1]=c+f,t[4]=1-l-d,t[7]=m-p,t[2]=g-x,t[5]=m+p,t[8]=1-l-u}(this,t),this}fromBasis(t,e,i){return this.set(t[0],t[1],t[2],e[0],e[1],e[2],i[0],i[1],i[2]),this}inverse(t=this){return function(t,e){let i=e[0],s=e[1],r=e[2],n=e[3],a=e[4],o=e[5],h=e[6],l=e[7],c=e[8],u=c*a-o*l,g=-c*n+o*h,m=l*n-a*h,d=i*u+s*g+r*m;d&&(d=1/d,t[0]=u*d,t[1]=(-c*s+r*l)*d,t[2]=(o*s-r*a)*d,t[3]=g*d,t[4]=(c*i-r*h)*d,t[5]=(-o*i+r*n)*d,t[6]=m*d,t[7]=(-l*i+s*h)*d,t[8]=(a*i-s*n)*d)}(this,t),this}getNormalMatrix(t){return function(t,e){let i=e[0],s=e[1],r=e[2],n=e[3],a=e[4],o=e[5],h=e[6],l=e[7],c=e[8],u=e[9],g=e[10],m=e[11],d=e[12],p=e[13],x=e[14],f=e[15],A=i*o-s*a,v=i*h-r*a,M=i*l-n*a,w=s*h-r*o,E=s*l-n*o,y=r*l-n*h,U=c*p-u*d,F=c*x-g*d,R=c*f-m*d,C=u*x-g*p,b=u*f-m*p,T=g*f-m*x,G=A*T-v*b+M*C+w*R-E*F+y*U;G&&(G=1/G,t[0]=(o*T-h*b+l*C)*G,t[1]=(h*R-a*T-l*F)*G,t[2]=(a*b-o*R+l*U)*G,t[3]=(r*b-s*T-n*C)*G,t[4]=(i*T-r*R+n*F)*G,t[5]=(s*R-i*b-n*U)*G,t[6]=(p*y-x*E+f*w)*G,t[7]=(x*M-d*y-f*v)*G,t[8]=(d*E-p*M+f*A)*G)}(this,t),this}}let W=0;class q extends O{constructor(t,{geometry:e,program:i,mode:s=t.TRIANGLES,frustumCulled:r=!0,renderOrder:n=0}={}){super(),t.canvas||console.error("gl not passed as first argument to Mesh"),this.gl=t,this.id=W++,this.geometry=e,this.program=i,this.mode=s,this.frustumCulled=r,this.renderOrder=n,this.modelViewMatrix=new N,this.normalMatrix=new H,this.beforeRenderCallbacks=[],this.afterRenderCallbacks=[]}onBeforeRender(t){return this.beforeRenderCallbacks.push(t),this}onAfterRender(t){return this.afterRenderCallbacks.push(t),this}draw({camera:t}={}){this.beforeRenderCallbacks.forEach((e=>e&&e({mesh:this,camera:t}))),t&&(this.program.uniforms.modelMatrix||Object.assign(this.program.uniforms,{modelMatrix:{value:null},viewMatrix:{value:null},modelViewMatrix:{value:null},normalMatrix:{value:null},projectionMatrix:{value:null},cameraPosition:{value:null}}),this.program.uniforms.projectionMatrix.value=t.projectionMatrix,this.program.uniforms.cameraPosition.value=t.worldPosition,this.program.uniforms.viewMatrix.value=t.viewMatrix,this.modelViewMatrix.multiply(t.viewMatrix,this.worldMatrix),this.normalMatrix.getNormalMatrix(this.modelViewMatrix),this.program.uniforms.modelMatrix.value=this.worldMatrix,this.program.uniforms.modelViewMatrix.value=this.modelViewMatrix,this.program.uniforms.normalMatrix.value=this.normalMatrix);let e=this.program.cullFace&&this.worldMatrix.determinant()<0;this.program.use({flipFaces:e}),this.geometry.draw({mode:this.mode,program:this.program}),this.afterRenderCallbacks.forEach((e=>e&&e({mesh:this,camera:t})))}}const X=new Uint8Array(4);function _(t){return 0==(t&t-1)}let $=1;class tt{constructor(t,{image:e,target:i=t.TEXTURE_2D,type:s=t.UNSIGNED_BYTE,format:r=t.RGBA,internalFormat:n=r,wrapS:a=t.CLAMP_TO_EDGE,wrapT:o=t.CLAMP_TO_EDGE,generateMipmaps:h=!0,minFilter:l=(h?t.NEAREST_MIPMAP_LINEAR:t.LINEAR),magFilter:c=t.LINEAR,premultiplyAlpha:u=!1,unpackAlignment:g=4,flipY:m=i==t.TEXTURE_2D,anisotropy:d=0,level:p=0,width:x,height:f=x}={}){this.gl=t,this.id=$++,this.image=e,this.target=i,this.type=s,this.format=r,this.internalFormat=n,this.minFilter=l,this.magFilter=c,this.wrapS=a,this.wrapT=o,this.generateMipmaps=h,this.premultiplyAlpha=u,this.unpackAlignment=g,this.flipY=m,this.anisotropy=Math.min(d,this.gl.renderer.parameters.maxAnisotropy),this.level=p,this.width=x,this.height=f,this.texture=this.gl.createTexture(),this.store={image:null},this.glState=this.gl.renderer.state,this.state={},this.state.minFilter=this.gl.NEAREST_MIPMAP_LINEAR,this.state.magFilter=this.gl.LINEAR,this.state.wrapS=this.gl.REPEAT,this.state.wrapT=this.gl.REPEAT,this.state.anisotropy=0}bind(){this.glState.textureUnits[this.glState.activeTextureUnit]!==this.id&&(this.gl.bindTexture(this.target,this.texture),this.glState.textureUnits[this.glState.activeTextureUnit]=this.id)}update(t=0){const e=!(this.image===this.store.image&&!this.needsUpdate);if((e||this.glState.textureUnits[t]!==this.id)&&(this.gl.renderer.activeTexture(t),this.bind()),e){if(this.needsUpdate=!1,this.flipY!==this.glState.flipY&&(this.gl.pixelStorei(this.gl.UNPACK_FLIP_Y_WEBGL,this.flipY),this.glState.flipY=this.flipY),this.premultiplyAlpha!==this.glState.premultiplyAlpha&&(this.gl.pixelStorei(this.gl.UNPACK_PREMULTIPLY_ALPHA_WEBGL,this.premultiplyAlpha),this.glState.premultiplyAlpha=this.premultiplyAlpha),this.unpackAlignment!==this.glState.unpackAlignment&&(this.gl.pixelStorei(this.gl.UNPACK_ALIGNMENT,this.unpackAlignment),this.glState.unpackAlignment=this.unpackAlignment),this.minFilter!==this.state.minFilter&&(this.gl.texParameteri(this.target,this.gl.TEXTURE_MIN_FILTER,this.minFilter),this.state.minFilter=this.minFilter),this.magFilter!==this.state.magFilter&&(this.gl.texParameteri(this.target,this.gl.TEXTURE_MAG_FILTER,this.magFilter),this.state.magFilter=this.magFilter),this.wrapS!==this.state.wrapS&&(this.gl.texParameteri(this.target,this.gl.TEXTURE_WRAP_S,this.wrapS),this.state.wrapS=this.wrapS),this.wrapT!==this.state.wrapT&&(this.gl.texParameteri(this.target,this.gl.TEXTURE_WRAP_T,this.wrapT),this.state.wrapT=this.wrapT),this.anisotropy&&this.anisotropy!==this.state.anisotropy&&(this.gl.texParameterf(this.target,this.gl.renderer.getExtension("EXT_texture_filter_anisotropic").TEXTURE_MAX_ANISOTROPY_EXT,this.anisotropy),this.state.anisotropy=this.anisotropy),this.image){if(this.image.width&&(this.width=this.image.width,this.height=this.image.height),this.target===this.gl.TEXTURE_CUBE_MAP)for(let t=0;t<6;t++)this.gl.texImage2D(this.gl.TEXTURE_CUBE_MAP_POSITIVE_X+t,this.level,this.internalFormat,this.format,this.type,this.image[t]);else if(ArrayBuffer.isView(this.image))this.gl.texImage2D(this.target,this.level,this.internalFormat,this.width,this.height,0,this.format,this.type,this.image);else if(this.image.isCompressedTexture)for(let t=0;t<this.image.length;t++)this.gl.compressedTexImage2D(this.target,t,this.internalFormat,this.image[t].width,this.image[t].height,0,this.image[t].data);else this.gl.texImage2D(this.target,this.level,this.internalFormat,this.format,this.type,this.image);this.generateMipmaps&&(this.gl.renderer.isWebgl2||_(this.image.width)&&_(this.image.height)?this.gl.generateMipmap(this.target):(this.generateMipmaps=!1,this.wrapS=this.wrapT=this.gl.CLAMP_TO_EDGE,this.minFilter=this.gl.LINEAR)),this.onUpdate&&this.onUpdate()}else if(this.target===this.gl.TEXTURE_CUBE_MAP)for(let t=0;t<6;t++)this.gl.texImage2D(this.gl.TEXTURE_CUBE_MAP_POSITIVE_X+t,0,this.gl.RGBA,1,1,0,this.gl.RGBA,this.gl.UNSIGNED_BYTE,X);else this.width?this.gl.texImage2D(this.target,this.level,this.internalFormat,this.width,this.height,0,this.format,this.type,null):this.gl.texImage2D(this.target,0,this.gl.RGBA,1,1,0,this.gl.RGBA,this.gl.UNSIGNED_BYTE,X);this.store.image=this.image}}}class et extends Array{constructor(t=0,e=t,i=t,s=t){return super(t,e,i,s),this}get x(){return this[0]}get y(){return this[1]}get z(){return this[2]}get w(){return this[3]}set x(t){this[0]=t}set y(t){this[1]=t}set z(t){this[2]=t}set w(t){this[3]=t}set(t,e,i,s){return t.length?this.copy(t):(C(this,t,e,i,s),this)}copy(t){return R(this,t),this}normalize(){return b(this,this),this}multiply(t){var e,i,s;return i=this,s=t,(e=this)[0]=i[0]*s,e[1]=i[1]*s,e[2]=i[2]*s,e[3]=i[3]*s,this}dot(t){return T(this,t)}fromArray(t,e=0){return this[0]=t[e],this[1]=t[e+1],this[2]=t[e+2],this[3]=t[e+3],this}toArray(t=[],e=0){return t[e]=this[0],t[e+1]=this[1],t[e+2]=this[2],t[e+3]=this[3],t}}class it extends p{constructor(t,{width:e=1,height:i=1,widthSegments:s=1,heightSegments:r=1,attributes:n={}}={}){const a=s,o=r,h=(a+1)*(o+1),l=a*o*6,c=new Float32Array(3*h),u=new Float32Array(3*h),g=new Float32Array(2*h),m=l>65536?new Uint32Array(l):new Uint16Array(l);it.buildPlane(c,u,g,m,e,i,0,a,o),Object.assign(n,{position:{size:3,data:c},normal:{size:3,data:u},uv:{size:2,data:g},index:{data:m}}),super(t,n)}static buildPlane(t,e,i,s,r,n,a,o,h,l=0,c=1,u=2,g=1,m=-1,d=0,p=0){const x=d,f=r/o,A=n/h;for(let v=0;v<=h;v++){let M=v*A-n/2;for(let n=0;n<=o;n++,d++){let A=n*f-r/2;if(t[3*d+l]=A*g,t[3*d+c]=M*m,t[3*d+u]=a/2,e[3*d+l]=0,e[3*d+c]=0,e[3*d+u]=a>=0?1:-1,i[2*d]=n/o,i[2*d+1]=1-v/h,v===h||n===o)continue;let w=x+n+v*(o+1),E=x+n+(v+1)*(o+1),y=x+n+(v+1)*(o+1)+1,U=x+n+v*(o+1)+1;s[6*p]=w,s[6*p+1]=E,s[6*p+2]=U,s[6*p+3]=E,s[6*p+4]=y,s[6*p+5]=U,p++}}}}const st=.5*(Math.sqrt(3)-1),rt=(3-Math.sqrt(3))/6,nt=t=>0|Math.floor(t),at=new Float64Array([1,1,-1,1,1,-1,-1,-1,1,0,-1,0,1,0,-1,0,0,1,0,-1,0,1,0,-1]);function ot(t){return t*(.5-Math.random())}function ht(t,e){return t+Math.random()*(e-t)}const lt=function(t=Math.random){const e=function(t){const e=512,i=new Uint8Array(e);for(let t=0;t<e/2;t++)i[t]=t;for(let s=0;s<e/2-1;s++){const e=s+~~(t()*(256-s)),r=i[s];i[s]=i[e],i[e]=r}for(let t=256;t<e;t++)i[t]=i[t-256];return i}(t),i=new Float64Array(e).map((t=>at[t%12*2])),s=new Float64Array(e).map((t=>at[t%12*2+1]));return function(t,r){let n=0,a=0,o=0;const h=(t+r)*st,l=nt(t+h),c=nt(r+h),u=(l+c)*rt,g=t-(l-u),m=r-(c-u);let d,p;g>m?(d=1,p=0):(d=0,p=1);const x=g-d+rt,f=m-p+rt,A=g-1+2*rt,v=m-1+2*rt,M=255&l,w=255&c;let E=.5-g*g-m*m;if(E>=0){const t=M+e[w];E*=E,n=E*E*(i[t]*g+s[t]*m)}let y=.5-x*x-f*f;if(y>=0){const t=M+d+e[w+p];y*=y,a=y*y*(i[t]*x+s[t]*f)}let U=.5-A*A-v*v;if(U>=0){const t=M+1+e[w+1];U*=U,o=U*U*(i[t]*A+s[t]*v)}return 70*(n+a+o)}}(Math.random);function ct(t){return Math.max(0,Math.min(1,t))}const ut="float PI = 3.141592653589793238;",gt="\n#extension GL_OES_standard_derivatives : enable\nprecision highp float;\n\nuniform float time;\nuniform float progress;\nuniform sampler2D texture1;\nuniform sampler2D texture2;\nuniform vec4 resolution;\nvarying vec2 vUv;\n",mt="\nattribute vec3 position;\nattribute vec3 normal;\nattribute vec2 uv;\nattribute float offset;\nattribute vec3 bary;\n\nuniform mat4 modelViewMatrix;\nuniform mat4 projectionMatrix;\nuniform float progress;\nuniform vec4 resolution;\n\nvarying vec2 vUv;\nvarying float vProgress;\nvarying float vProgress1;\nvarying vec3 vBary;\n",dt="\nmat4 rotationMatrix(vec3 axis, float angle) {\n  axis = normalize(axis);\n  float s = sin(angle);\n  float c = cos(angle);\n  float oc = 1.0 - c;\n\n  return mat4(oc * axis.x * axis.x + c,           oc * axis.x * axis.y - axis.z * s,  oc * axis.z * axis.x + axis.y * s,  0.0,\n              oc * axis.x * axis.y + axis.z * s,  oc * axis.y * axis.y + c,           oc * axis.y * axis.z - axis.x * s,  0.0,\n              oc * axis.z * axis.x - axis.y * s,  oc * axis.y * axis.z + axis.x * s,  oc * axis.z * axis.z + c,           0.0,\n              0.0,                                0.0,                                0.0,                                1.0);\n}\nvec3 rotate(vec3 v, vec3 axis, float angle) {\n  mat4 m = rotationMatrix(axis, angle);\n  return (m * vec4(v, 1.0)).xyz;\n}\n",pt={dots:{uniforms:{},fragment:`\n    ${gt}\n    const float SQRT_2 = 1.414213562373;\n    const vec2 center = vec2(0, 0);// = vec2(0, 0);\n    const float dots = 20.0;// = 20.0;\n\n    vec4 getFromColor(vec2 p) {\n      return texture2D(texture1, p);\n    }\n\n    vec4 getToColor(vec2 p) {\n      return texture2D(texture2, p);\n    }\n\n    void main()\t{\n      vec2 newUV = (vUv - vec2(0.5))*resolution.zw + vec2(0.5);\n\n      bool nextImage = distance(fract(newUV * dots), vec2(0.5, 0.5)) < ( progress / distance(newUV, center));\n      gl_FragColor = nextImage ? getToColor(newUV) : getFromColor(newUV);\n    }\n\n  `},flyeye:{uniforms:{},fragment:`\n    ${gt}\n    const float size = 0.04; // = 0.04\n    const float zoom = 100.0; // = 50.0\n    const float colorSeparation = 0.3; // = 0.3\n\n    vec4 getFromColor(vec2 p) {\n      return texture2D(texture1, p);\n    }\n\n    vec4 getToColor(vec2 p) {\n      return texture2D(texture2, p);\n    }\n\n    void main()\t{\n      vec2 newUV = (vUv - vec2(0.5))*resolution.zw + vec2(0.5);\n\n      float inv = 1. - progress;\n      vec2 disp = size*vec2(cos(zoom*newUV.x), sin(zoom*newUV.y));\n      vec4 texTo = getToColor(newUV + inv*disp);\n      vec4 texFrom = vec4(\n        getFromColor(newUV + progress*disp*(1.0 - colorSeparation)).r,\n        getFromColor(newUV + progress*disp).g,\n        getFromColor(newUV + progress*disp*(1.0 + colorSeparation)).b,\n        1.0);\n      gl_FragColor = texTo*progress + texFrom*inv;\n    }\n\n  `},"morph-x":{uniforms:{intensity:{value:1,type:"f",min:0,max:3}},fragment:`\n  ${gt}\n  uniform float intensity;\n  uniform sampler2D displacement;\n  mat2 getRotM(float angle) {\n    float s = sin(angle);\n    float c = cos(angle);\n    return mat2(c, -s, s, c);\n  }\n  const float PI = 3.1415;\n  const float angle1 = PI *0.25;\n  const float angle2 = -PI *0.75;\n  void main()\t{\n    vec2 newUV = (vUv - vec2(0.5))*resolution.zw + vec2(0.5);\n    vec4 disp = texture2D(displacement, newUV);\n    vec2 dispVec = vec2(disp.r, disp.g);\n    vec2 distortedPosition1 = newUV + getRotM(angle1) * dispVec * intensity * progress;\n    vec4 t1 = texture2D(texture1, distortedPosition1);\n    vec2 distortedPosition2 = newUV + getRotM(angle2) * dispVec * intensity * (1.0 - progress);\n    vec4 t2 = texture2D(texture2, distortedPosition2);\n    gl_FragColor = mix(t1, t2, progress);\n  }\n`},"morph-y":{uniforms:{intensity:{value:.3,type:"f",min:0,max:2}},fragment:`\n  ${gt}\n  uniform float intensity;\n  uniform sampler2D displacement;\n  void main()\t{\n    vec2 newUV = (vUv - vec2(0.5))*resolution.zw + vec2(0.5);\n    vec4 d1 = texture2D(texture1, newUV);\n    vec4 d2 = texture2D(texture2, newUV);\n    float displace1 = (d1.r + d1.g + d1.b)*0.33;\n    float displace2 = (d2.r + d2.g + d2.b)*0.33;\n\n    vec4 t1 = texture2D(texture1, vec2(newUV.x, newUV.y + progress * (displace2 * intensity)));\n    vec4 t2 = texture2D(texture2, vec2(newUV.x, newUV.y + (1.0 - progress) * (displace1 * intensity)));\n    gl_FragColor = mix(t1, t2, progress);\n  }\n`},"page-curl":{uniforms:{},fragment:`\n    ${gt}\n    const float MIN_AMOUNT = -0.16;\n    const float MAX_AMOUNT = 1.5;\n\n    const float PI = 3.141592653589793;\n\n    const float scale = 512.0;\n    const float sharpness = 3.0;\n\n    const float cylinderRadius = 1.0 / PI / 2.0;\n\n    vec4 getFromColor(vec2 p) {\n      return texture2D(texture1, p);\n    }\n\n    vec4 getToColor(vec2 p) {\n      return texture2D(texture2, p);\n    }\n\n    vec3 hitPoint(float hitAngle, float yc, vec3 point, mat3 rrotation) {\n      float hitPoint = hitAngle / (2.0 * PI);\n      point.y = hitPoint;\n      return rrotation * point;\n    }\n\n    vec4 antiAlias(vec4 color1, vec4 color2, float distanc) {\n      distanc *= scale;\n      if(distanc < 0.0)\n        return color2;\n      if(distanc > 2.0)\n        return color1;\n      float dd = pow(1.0 - distanc / 2.0, sharpness);\n      return ((color2 - color1) * dd) + color1;\n    }\n\n    float distanceToEdge(vec3 point) {\n      float dx = abs(point.x > 0.5 ? 1.0 - point.x : point.x);\n      float dy = abs(point.y > 0.5 ? 1.0 - point.y : point.y);\n      if(point.x < 0.0)\n        dx = -point.x;\n      if(point.x > 1.0)\n        dx = point.x - 1.0;\n      if(point.y < 0.0)\n        dy = -point.y;\n      if(point.y > 1.0)\n        dy = point.y - 1.0;\n      if((point.x < 0.0 || point.x > 1.0) && (point.y < 0.0 || point.y > 1.0))\n        return sqrt(dx * dx + dy * dy);\n      return min(dx, dy);\n    }\n\n    vec4 seeThrough(float yc, vec2 p, mat3 rotation, mat3 rrotation, float cylinderAngle) {\n      float hitAngle = PI - (acos(yc / cylinderRadius) - cylinderAngle);\n      vec3 point = hitPoint(hitAngle, yc, rotation * vec3(p, 1.0), rrotation);\n      if(yc <= 0.0 && (point.x < 0.0 || point.y < 0.0 || point.x > 1.0 || point.y > 1.0)) {\n        return getToColor(p);\n      }\n\n      if(yc > 0.0)\n        return getFromColor(p);\n\n      vec4 color = getFromColor(point.xy);\n      vec4 tcolor = vec4(0.0);\n\n      return antiAlias(color, tcolor, distanceToEdge(point));\n    }\n\n    vec4 seeThroughWithShadow(float yc, vec2 p, vec3 point, mat3 rotation, mat3 rrotation, float cylinderAngle, float amount) {\n      float shadow = distanceToEdge(point) * 30.0;\n      shadow = (1.0 - shadow) / 3.0;\n\n      if(shadow < 0.0)\n        shadow = 0.0;\n      else\n        shadow *= amount;\n\n      vec4 shadowColor = seeThrough(yc, p, rotation, rrotation, cylinderAngle);\n      shadowColor.r -= shadow;\n      shadowColor.g -= shadow;\n      shadowColor.b -= shadow;\n\n      return shadowColor;\n    }\n\n    vec4 backside(float yc, vec3 point) {\n      vec4 color = getFromColor(point.xy);\n      float gray = (color.r + color.b + color.g) / 15.0;\n      gray += (8.0 / 10.0) * (pow(1.0 - abs(yc / cylinderRadius), 2.0 / 10.0) / 2.0 + (5.0 / 10.0));\n      color.rgb = vec3(gray);\n      return color;\n    }\n\n    vec4 behindSurface(vec2 p, float yc, vec3 point, mat3 rrotation, float cylinderAngle, float amount) {\n      float shado = (1.0 - ((-cylinderRadius - yc) / amount * 7.0)) / 6.0;\n      shado *= 1.0 - abs(point.x - 0.5);\n\n      yc = (-cylinderRadius - cylinderRadius - yc);\n\n      float hitAngle = (acos(yc / cylinderRadius) + cylinderAngle) - PI;\n      point = hitPoint(hitAngle, yc, point, rrotation);\n\n      if(yc < 0.0 && point.x >= 0.0 && point.y >= 0.0 && point.x <= 1.0 && point.y <= 1.0 && (hitAngle < PI || amount > 0.5)) {\n        shado = 1.0 - (sqrt(pow(point.x - 0.5, 2.0) + pow(point.y - 0.5, 2.0)) / (71.0 / 100.0));\n        shado *= pow(-yc / cylinderRadius, 3.0);\n        shado *= 0.5;\n      } else {\n        shado = 0.0;\n      }\n      return vec4(getToColor(p).rgb - shado, 1.0);\n    }\n\n    void main() {\n      vec2 newUV = (vUv - vec2(0.5)) * resolution.zw + vec2(0.5);\n\n      float amount = progress * (MAX_AMOUNT - MIN_AMOUNT) + MIN_AMOUNT;\n      float cylinderCenter = amount;\n          // 360 degrees * amount\n      float cylinderAngle = 2.0 * PI * amount;\n\n      const float angle = 100.0 * PI / 180.0;\n      float c = cos(-angle);\n      float s = sin(-angle);\n\n      mat3 rotation = mat3(c, s, 0, -s, c, 0, -0.801, 0.8900, 1);\n      c = cos(angle);\n      s = sin(angle);\n\n      mat3 rrotation = mat3(c, s, 0, -s, c, 0, 0.98500, 0.985, 1);\n\n      vec3 point = rotation * vec3(newUV, 1.0);\n\n      float yc = point.y - cylinderCenter;\n\n      if(yc < -cylinderRadius) {\n                        // Behind surface\n        gl_FragColor = behindSurface(newUV, yc, point, rrotation, cylinderAngle, amount);\n        return;\n      }\n\n      if(yc > cylinderRadius) {\n                        // Flat surface\n        gl_FragColor = getFromColor(newUV);\n        return;\n      }\n\n      float hitAngle = (acos(yc / cylinderRadius) + cylinderAngle) - PI;\n\n      float hitAngleMod = mod(hitAngle, 2.0 * PI);\n      if((hitAngleMod > PI && amount < 0.5) || (hitAngleMod > PI / 2.0 && amount < 0.0)) {\n        gl_FragColor = seeThrough(yc, newUV, rotation, rrotation, cylinderAngle);\n        return;\n      }\n\n      point = hitPoint(hitAngle, yc, point, rrotation);\n\n      if(point.x < 0.0 || point.y < 0.0 || point.x > 1.0 || point.y > 1.0) {\n        gl_FragColor = seeThroughWithShadow(yc, newUV, point, rotation, rrotation, cylinderAngle, amount);\n        return;\n      }\n\n      vec4 color = backside(yc, point);\n\n      vec4 otherColor;\n      if(yc < 0.0) {\n        float shado = 1.0 - (sqrt(pow(point.x - 0.5, 2.0) + pow(point.y - 0.5, 2.0)) / 0.71);\n        shado *= pow(-yc / cylinderRadius, 3.0);\n        shado *= 0.5;\n        otherColor = vec4(0.0, 0.0, 0.0, shado);\n      } else {\n        otherColor = getFromColor(newUV);\n      }\n\n      color = antiAlias(color, otherColor, cylinderRadius - abs(yc));\n\n      vec4 cl = seeThroughWithShadow(yc, newUV, point, rotation, rrotation, cylinderAngle, amount);\n      float dist = distanceToEdge(point);\n\n      gl_FragColor = antiAlias(color, cl, dist);\n    }\n  `},"peel-x":{uniforms:{},fragment:`\n    ${gt}\n    void main()\t{\n      vec2 newUV = (vUv - vec2(0.5))*resolution.zw + vec2(0.5);\n      vec2 p = newUV;\n      float x = progress;\n      x = smoothstep(.0,1.0,(x*2.0+p.x-1.0));\n      vec4 f = mix(\n        texture2D(texture1, (p-.5)*(1.-x)+.5),\n        texture2D(texture2, (p-.5)*x+.5),\n        x);\n      gl_FragColor = f;\n    }\n  `},"peel-y":{uniforms:{},fragment:`\n    ${gt}\n    void main()\t{\n      vec2 newUV = (vUv - vec2(0.5))*resolution.zw + vec2(0.5);\n      vec2 p = newUV;\n      float x = progress;\n      x = smoothstep(.0,1.0,(x*2.0+p.y-1.0));\n      vec4 f = mix(\n        texture2D(texture1, (p-.5)*(1.-x)+.5),\n        texture2D(texture2, (p-.5)*x+.5),\n        x);\n      gl_FragColor = f;\n    }\n  `},"polygons-fall":{uniforms:{},detail:12,offsetTop:0,vertex:`\n    ${mt}\n    attribute vec3 centroid1;\n\n    ${dt}\n\n    void main() {\n      ${ut}\n      vUv = uv;\n      vBary = bary;\n\n      vec3 newpos = position;\n\n      float o = 1. - offset;\n      float pr = (progress - 0.5) * (0. + resolution.y / resolution.x) + 0.5;\n      pr = progress;\n      float prog = clamp((pr - o * 0.9) / 0.1, 0., 1.);\n      vProgress = prog;\n      vProgress1 = clamp((pr - clamp(o - 0.1, 0., 1.) * 0.9) / 0.1, 0., 1.);\n      newpos = rotate((newpos - centroid1), vec3(1., 0., 0.), -prog * PI) + centroid1 + vec3(0., -1., 0.) * prog * 0.;\n      gl_Position = projectionMatrix * modelViewMatrix * vec4(newpos, 1.0);\n    }\n  `,fragment:`\n    ${gt}\n    varying float vProgress;\n    varying float vProgress1;\n    ${ut}\n    varying vec3 vBary;\n\n    void main()\t{\n      float width = 2.5 * vProgress1;\n      vec3 d;\n      #ifdef GL_OES_standard_derivatives\n        d = fwidth(vBary);\n      #endif\n      vec3 s = smoothstep(d * (width + 0.5), d * (width - 0.5), vBary);\n      float alpha = max(max(s.x, s.y), s.z);\n      vec3 color = vec3(alpha);\n      vec2 newUV = (vUv - vec2(0.5)) * resolution.zw + vec2(0.5);\n      vec4 t = texture2D(texture1, newUV);\n      float opa = smoothstep(1., 0.5, vProgress);\n      opa = 1. - vProgress;\n      gl_FragColor = vec4(vUv, 0.0, opa);\n      gl_FragColor = vec4(t.rgb + .5 * color * vProgress1, opa);\n    }\n  `},"polygons-morph":{uniforms:{},detail:20,offsetTop:.4,vertex:`\n    ${mt}\n    ${dt}\n\n    void main() {\n      ${ut}\n      vUv = uv;\n      vBary = bary;\n\n      vec3 newpos = position;\n\n      float o = 1. - offset;\n      float prog = clamp((progress - o * 0.6) / 0.4, 0., 1.);\n      vProgress = prog;\n      vProgress1 = clamp((progress - clamp(o - 0.1, -0., 1.) * 0.9) / 0.1, 0., 1.);\n      gl_Position = projectionMatrix * modelViewMatrix * vec4(newpos, 1.0);\n    }\n  `,fragment:`\n    ${gt}\n    varying float vProgress;\n    varying float vProgress1;\n    ${ut}\n    varying vec3 vBary;\n    void main()\t{\n      float width = 2.5 * vProgress1;\n      vec3 d;\n      #ifdef GL_OES_standard_derivatives\n        d = fwidth(vBary);\n      #endif\n      vec3 s = smoothstep(d * (width + 0.5), d * (width - 0.5), vBary);\n      float alpha = max(max(s.x, s.y), s.z);\n      vec3 color = vec3(alpha);\n\n      vec2 newUV = (vUv - vec2(0.5)) * resolution.zw + vec2(0.5);\n      vec4 t = texture2D(texture1, newUV);\n      float opa = smoothstep(1., 0.5, vProgress);\n      opa = 1. - vProgress;\n      gl_FragColor = vec4(t.rgb + 1. * color * vProgress1, opa);\n    }\n  `},"polygons-wind":{uniforms:{},detail:40,offsetTop:1,vertex:`\n    ${mt}\n    attribute vec3 control0;\n    attribute vec3 control1;\n\n    ${dt}\n\n    float easeOut(float t){\n      return  t * t * t;\n    }\n\n    vec3 bezier4(vec3 a, vec3 b, vec3 c, vec3 d, float t) {\n      return mix(mix(mix(a, b, t), mix(b, c, t), t), mix(mix(b, c, t), mix(c, d, t), t), t);\n    }\n\n    void main() {\n      ${ut}\n      vUv = uv;\n      vBary = bary;\n\n      vec3 newpos = position;\n\n      float o = 1. - offset;\n      float prog = clamp((progress - o * 0.6) / 0.4, 0., 1.);\n      vProgress = prog;\n      vProgress1 = clamp((progress - clamp(o - 0.2, -0., 1.) * 0.6) / 0.4, 0., 1.);\n      newpos = bezier4(newpos, control0, control1, newpos, easeOut(prog));\n      gl_Position = projectionMatrix * modelViewMatrix * vec4(newpos, 1.0);\n    }\n  `,fragment:`\n    ${gt}\n    varying float vProgress;\n    varying float vProgress1;\n    ${ut}\n    varying vec3 vBary;\n    void main()\t{\n      float width = 2.5 * vProgress1;\n      vec3 d;\n      #ifdef GL_OES_standard_derivatives\n        d = fwidth(vBary);\n      #endif\n      vec3 s = smoothstep(d * (width + 0.5), d * (width - 0.5), vBary);\n      float alpha = max(max(s.x, s.y), s.z);\n      vec3 color = vec3(alpha);\n\n      vec2 newUV = (vUv - vec2(0.5)) * resolution.zw + vec2(0.5);\n      vec4 t = texture2D(texture1, newUV);\n      float opa = smoothstep(1., 0.5, vProgress);\n      opa = 1. - vProgress;\n      gl_FragColor = vec4(vUv, 0.0, opa);\n      opa = smoothstep(0.5, 1., opa);\n      gl_FragColor = vec4(t.rgb + 1. * color * vProgress1, opa);\n    }\n  `},pixelize:{uniforms:{},fragment:`\n    ${gt}\n    ivec2 squaresMin = ivec2(50);\n    int steps = 20;\n\n    void main()\t{\n      vec2 newUV = (vUv - vec2(0.5))*resolution.zw + vec2(0.5);\n\n      float d = min(progress, 1.0 - progress);\n      float dist = steps>0 ? ceil(d * float(steps)) / float(steps) : d;\n      vec2 squareSize = 2.0 * dist / vec2(squaresMin);\n\n      vec2 p = dist>0.0 ? (floor(newUV / squareSize) + 0.5) * squareSize : newUV;\n\n      vec2 uv1 = newUV;\n      vec2 uv2 = newUV;\n\n      vec4 t1 = texture2D(texture1,p);\n      vec4 t2 = texture2D(texture2,p);\n\n      gl_FragColor = mix(t1, t2, progress);\n    }\n  `},ripple:{uniforms:{radius:{value:.9,type:"f",min:.1,max:2},width:{value:.35,type:"f",min:0,max:1}},fragment:`\n    ${gt}\n    uniform float width;\n    uniform float radius;\n    uniform sampler2D displacement;\n    float parabola( float x, float k ) {\n      return pow( 4. * x * ( 1. - x ), k );\n    }\n    void main()\t{\n      vec2 newUV = (vUv - vec2(0.5))*resolution.zw + vec2(0.5);\n      vec2 p = newUV;\n      vec2 start = vec2(0.5,0.5);\n      vec2 aspect = resolution.wz;\n      vec2 uv = newUV;\n      float dt = parabola(progress, 1.);\n      vec4 noise = texture2D(displacement, fract(vUv+time*0.04));\n      float prog = progress*0.66 + noise.g * 0.04;\n      float circ = 1. - smoothstep(-width, 0.0, radius * distance(start*aspect, uv*aspect) - prog*(1.+width));\n      float intpl = pow(abs(circ), 1.);\n      vec4 t1 = texture2D( texture1, (uv - 0.5) * (1.0 - intpl) + 0.5 ) ;\n      vec4 t2 = texture2D( texture2, (uv - 0.5) * intpl + 0.5 );\n      gl_FragColor = mix( t1, t2, intpl );\n    }\n  `},shutters:{uniforms:{intensity:{value:50,type:"f",min:1,max:100}},fragment:`\n    ${gt}\n    uniform float intensity;\n    mat2 rotate(float a) {\n      float s = sin(a);\n      float c = cos(a);\n      return mat2(c, -s, s, c);\n    }\n    const float PI = 3.1415;\n    const float angle1 = PI *0.25;\n    const float angle2 = PI *0.25;\n\n    void main()\t{\n      vec2 newUV = (vUv - vec2(0.5))*resolution.zw + vec2(0.5);\n\n      vec2 uvDivided = fract(newUV*vec2(intensity,1.));\n\n      vec2 uvDisplaced1 = newUV + rotate(angle1)*uvDivided*progress*0.1;\n      vec2 uvDisplaced2 = newUV + rotate(angle2)*uvDivided*(1. - progress)*0.1;\n\n      vec4 t1 = texture2D(texture1,uvDisplaced1);\n      vec4 t2 = texture2D(texture2,uvDisplaced2);\n\n      gl_FragColor = mix(t1, t2, progress);\n    }\n\n  `},slices:{uniforms:{size:{value:.25,type:"f",min:.1,max:1}},fragment:`\n    ${gt}\n    uniform float size; // = 0.2\n    float count = 20.; // = 10.0\n    float smoothness = .5; // = 0.5\n    void main()\t{\n      vec2 newUV = (vUv - vec2(0.5))*resolution.zw + vec2(0.5);\n\n      float pr = smoothstep(-smoothness, 0.0, newUV.x - progress * (1.0 + smoothness));\n      float s = step(pr, fract(count * newUV.x));\n\n      vec2 uv1 = newUV;\n      vec2 uv2 = newUV;\n\n      vec4 t1 = texture2D(texture1,uv1);\n      vec4 t2 = texture2D(texture2,uv2);\n      gl_FragColor = mix(t1, t2, s);\n\n    }\n  `},squares:{uniforms:{},fragment:`\n    ${gt}\n    ivec2 squares = ivec2(10,10);\n    vec2 direction = vec2(1.0, -0.5);\n    float smoothness = 1.6;\n\n    const vec2 center = vec2(0.5, 0.5);\n    void main() {\n      vec2 newUV = (vUv - vec2(0.5))*resolution.zw + vec2(0.5);\n\n      vec2 v = normalize(direction);\n      v /= abs(v.x)+abs(v.y);\n      float d = v.x * center.x + v.y * center.y;\n      float offset = smoothness;\n      float pr = smoothstep(-offset, 0.0, v.x * newUV.x + v.y * newUV.y - (d-0.5+progress*(1.+offset)));\n      vec2 squarep = fract(newUV*vec2(squares));\n      vec2 squaremin = vec2(pr/2.0);\n      vec2 squaremax = vec2(1.0 - pr/2.0);\n      float a = (1.0 - step(progress, 0.0)) * step(squaremin.x, squarep.x) * step(squaremin.y, squarep.y) * step(squarep.x, squaremax.x) * step(squarep.y, squaremax.y);\n\n      vec2 uv1 = newUV;\n      vec2 uv2 = newUV;\n\n      vec4 t1 = texture2D(texture1,newUV);\n      vec4 t2 = texture2D(texture2,newUV);\n\n      gl_FragColor = mix(t1, t2, a);\n    }\n  `},stretch:{uniforms:{intensity:{value:50,type:"f",min:1,max:100}},fragment:`\n    ${gt}\n    uniform float intensity;\n    mat2 rotate(float a) {\n      float s = sin(a);\n      float c = cos(a);\n      return mat2(c, -s, s, c);\n    }\n    const float PI = 3.1415;\n    const float angle1 = PI *0.25;\n    const float angle2 = -PI *0.75;\n    const float noiseSeed = 2.;\n    float random() {\n      return fract(sin(noiseSeed + dot(gl_FragCoord.xy / resolution.xy / 10.0, vec2(12.9898, 4.1414))) * 43758.5453);\n    }\n    float hash(float n) { return fract(sin(n) * 1e4); }\n    float hash(vec2 p) { return fract(1e4 * sin(17.0 * p.x + p.y * 0.1) * (0.1 + abs(sin(p.y * 13.0 + p.x)))); }\n    float hnoise(vec2 x) {\n      vec2 i = floor(x);\n      vec2 f = fract(x);\n      float a = hash(i);\n      float b = hash(i + vec2(1.0, 0.0));\n      float c = hash(i + vec2(0.0, 1.0));\n      float d = hash(i + vec2(1.0, 1.0));\n      vec2 u = f * f * (3.0 - 2.0 * f);\n      return mix(a, b, u.x) + (c - a) * u.y * (1.0 - u.x) + (d - b) * u.x * u.y;\n    }\n    void main()\t{\n      vec2 newUV = (vUv - vec2(0.5))*resolution.zw + vec2(0.5);\n\n      float hn = hnoise(newUV.xy * resolution.xy / 100.0);\n      vec2 d = vec2(0.,normalize(vec2(0.5,0.5) - newUV.xy).y);\n      vec2 uv1 = newUV + d * progress / 5.0 * (1.0 + hn / 2.0);\n      vec2 uv2 = newUV - d * (1.0 - progress) / 5.0 * (1.0 + hn / 2.0);\n      vec4 t1 = texture2D(texture1,uv1);\n      vec4 t2 = texture2D(texture2,uv2);\n      gl_FragColor = mix(t1, t2, progress);\n    }\n  `},"wave-x":{uniforms:{},fragment:`\n  ${gt}\n  uniform sampler2D displacement;\n  vec2 mirrored(vec2 v) {\n    vec2 m = mod(v,2.);\n    return mix(m,2.0 - m, step(1.0 ,m));\n  }\n  void main()\t{\n    vec2 newUV = (vUv - vec2(0.5))*resolution.zw + vec2(0.5);\n    vec4 noise = texture2D(displacement, mirrored(newUV+time*0.04));\n    float prog = (1.0 - progress)*0.8 -0.05 + noise.g * 0.06;\n    float intpl = pow(abs(smoothstep(0., 1., (prog*2. - vUv.x + 0.5))), 10.);\n\n    vec4 t1 = texture2D( texture2, (newUV - 0.5) * (1.0 - intpl) + 0.5 ) ;\n    vec4 t2 = texture2D( texture1, (newUV - 0.5) * intpl + 0.5 );\n    gl_FragColor = mix( t1, t2, intpl );\n  }\n  `},wind:{uniforms:{},fragment:`\n    ${gt}\n    float size = 0.2;\n\n    float rand (vec2 co) {\n      return fract(sin(dot(co.xy ,vec2(12.9898,78.233))) * 43758.5453);\n    }\n\n    void main()\t{\n      vec2 newUV = (vUv - vec2(0.5))*resolution.zw + vec2(0.5);\n\n      float r = rand(vec2(0, newUV.y));\n      float m = smoothstep(0.0, -size, newUV.x*(1.0-size) + size*r - ((progress) * (1.0 + size)));\n\n      vec2 uv1 = newUV;\n      vec2 uv2 = newUV;\n\n      vec4 t1 = texture2D(texture1,uv1);\n      vec4 t2 = texture2D(texture2,uv2);\n      gl_FragColor = mix(t1, t2, m);\n\n    }\n  `}},xt="\nattribute vec2 uv;\nattribute vec3 position;\nuniform mat4 modelViewMatrix;\nuniform mat4 projectionMatrix;\nvarying vec2 vUv;\n\nvoid main() {\n  vUv = uv;\n  gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n}\n",ft=()=>{const t=Math.floor(Math.random()*Object.keys(pt).length);return pt[Object.keys(pt)[t]]};class At{constructor(t){const e="random"===t.shader?ft():pt[t.shader];this.shader=e,this.scene=new O,this.swiper=t.swiper,this.vertex=e.vertex||xt,this.fragment=e.fragment,this.uniforms=e.uniforms||{},this.renderer=new F({dpr:2,webgl:2}),this.gl=this.renderer.gl,this.width=window.innerWidth,this.height=window.innerHeight,this.renderer.setSize(this.width,this.height),this.gl.clearColor(1,1,1,1),this.container=this.swiper.el,this.images=[],this.displacementTexture=null,this.container.querySelectorAll(".swiper-gl-image").forEach((t=>{this.images.push(t.src)})),this.width=this.swiper.width,this.height=this.swiper.height,this.container.prepend(this.gl.canvas),this.camera=new J(this.gl,{fov:45}),this.camera.perspective({aspect:this.gl.canvas.width/this.gl.canvas.height}),this.camera.position.set(0,0,2),this.time=0,this.current=0,this.textures=[],this.init((()=>{this.addObjects(),this.resize(),this.render()}))}animateUniform(t,e,i){const s=t.value;let r,n=null;window.cancelAnimationFrame(this.animateUniformFrame);const a=e>t.value?"next":"prev",o=(t,e)=>"next"===a&&t>=e||"prev"===a&&t<=e,h=()=>{r=(new Date).getTime(),null===n&&(n=r);const a=Math.max(Math.min((r-n)/this.swiper.params.speed,1),0),l=.5-Math.cos(a*Math.PI)/2;let c=s+l*(e-s);if(o(c,e)&&(c=e),t.value=c,o(c,e))return cancelAnimationFrame(this.animateUniformFrame),void(i&&i());this.animateUniformFrame=requestAnimationFrame(h)};h()}init(t){const e=[],i=this;this.images.forEach(((t,s)=>{const r=new Promise((e=>{const r=new Image,n=new tt(this.gl);r.onload=()=>{n.image=r,i.textures[s]=n,e()},r.src=t}));e.push(r)})),e.push(new Promise((t=>{const e=new Image,s=new tt(this.gl);e.onload=()=>{s.image=e,i.displacementTexture=s,t()},e.src="data:image/jpeg;base64,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"}))),Promise.all(e).then((()=>{this.initialized=!0,this.onInit&&this.onInit(),t()}))}resize(){if(!this.initialized||this.destroyed)return;const{width:t,height:e}=this.swiper;this.width=t,this.height=e,this.renderer.setSize(t,e);const i=this.camera.position.z;if(this.camera.perspective({aspect:t/e,fov:180/Math.PI*2*Math.atan(1/(2*i))}),!this.textures[0].image)return;const s=this.textures[0].image.height/this.textures[0].image.width;let r,n;e/t>s?(r=t/e*s,n=1):(r=1,n=e/t/s),this.material.uniforms.resolution.value.x=t,this.material.uniforms.resolution.value.y=e,this.material.uniforms.resolution.value.z=r,this.material.uniforms.resolution.value.w=n,this.shader.vertex&&this.vertexMaterial&&(this.vertexMaterial.uniforms.resolution.value.x=t,this.vertexMaterial.uniforms.resolution.value.y=e,this.vertexMaterial.uniforms.resolution.value.z=r,this.vertexMaterial.uniforms.resolution.value.w=n),this.shader.vertex?(this.nextMesh.scale.set(this.camera.aspect/2,.5,.5),this.currentMesh.scale.set(this.camera.aspect/2,.5,.5)):(this.plane.scale.x=this.camera.aspect,this.plane.scale.y=1)}createMaterial(){return new A(this.gl,{extensions:{derivatives:"#extension GL_OES_standard_derivatives : enable"},uniforms:{time:{type:"f",value:0},progress:{type:"f",value:0},intensity:{type:"f",value:0},width:{type:"f",value:0},radius:{type:"f",value:0},size:{type:"f",value:0},texture1:{type:"f",value:this.textures[0]},texture2:{type:"f",value:this.textures[1]},displacement:{type:"f",value:this.displacementTexture},resolution:{type:"v4",value:new et}},vertex:this.shader.vertex||xt,fragment:this.shader.fragment,...this.shader.vertex?{transparent:!0,depthWrite:!1}:{}})}addObjects(){if(this.scene.children.forEach((t=>{this.scene.removeChild(t)})),this.scene.children.forEach((t=>{this.scene.removeChild(t)})),this.material=this.createMaterial(),this.shader.vertex){const t=function(t,e,i){i=i||0;const s=e,r=2/s,n=r*Math.sqrt(3)/2,a=2/n,o=[],h=[],l=[],c=[],u=[],g=[],m=[];let d=0;const x=[];let f=0;for(let t=0;t<a;t+=1){f=t*n,d=t%2==1?-r/2:0;for(let p=0;p<=s;p+=1){const s=Math.sign(p*r+d-1);h.push(p*r+d-1,f-1,0),m.push((p*r+d)/2,f/2),h.push(p*r+r/2+d-1,n+f-1,0),m.push((p*r+r/2+d)/2,(n+f)/2),h.push(p*r-r/2+d-1,n+f-1,0),m.push((p*r-r/2+d)/2,(n+f)/2);let A=lt(p/a,t/a)+Math.random();const v=ct(f/2+2*A/e);let M=Math.random();o.push(v,ct(v+.1*i),ct(v+.1*i)),g.push(M,M,M);const w=[p*r+d-1,f-1,0];l.push(...w,...w,...w);const E=[2*s*ht(-.3,.3),-2*ht(-.3,.3)*1.5,-ot(.5)],y=[2*s*ht(.3,.6),-2*ht(.3,.6)*1.5,-ot(.5)];c.push(...E,...E,...E),u.push(...y,...y,...y),x.push(0,0,1,0,1,0,1,0,0),h.push(p*r+d-1,f-1,0),m.push((p*r+d)/2,f/2),h.push(p*r+r+d-1,f-1,0),m.push((p*r+r+d)/2,f/2),h.push(p*r+r/2+d-1,n+f-1,0),m.push((p*r+r/2+d)/2,(n+f)/2),A=lt((p+1)/a,t/a)+Math.random();const U=ct(f/2+2*A/e);M=Math.random(),o.push(U,U,ct(U+.1*i)),g.push(M,M,M);const F=[p*r+d-1,f-1,0];c.push(...E,...E,...E),u.push(...y,...y,...y),l.push(...F,...F,...F),x.push(0,0,1,0,1,0,1,0,0)}}const A=new p(t);return A.addAttribute("position",{size:3,data:new Float32Array(h)}),A.addAttribute("bary",{size:3,data:new Float32Array(x)}),A.addAttribute("uv",{size:2,data:new Float32Array(m)}),A.addAttribute("offset",{size:1,data:new Float32Array(o)}),A.addAttribute("centroid1",{size:3,data:new Float32Array(l)}),A.addAttribute("control0",{size:3,data:new Float32Array(c)}),A.addAttribute("control1",{size:3,data:new Float32Array(u)}),A.addAttribute("random",{size:1,data:new Float32Array(g)}),A}(this.gl,this.shader.detail,this.shader.offsetTop),e=this.textures[1];this.vertexMaterial=this.createMaterial(),this.vertexMaterial.uniforms.texture1.value=e,this.currentMesh=new q(this.gl,{geometry:t,program:this.material}),this.nextMesh=new q(this.gl,{geometry:t,program:this.vertexMaterial}),this.nextMesh.position.z=-1e-4,this.currentMesh.setParent(this.scene),this.nextMesh.setParent(this.scene)}else{const t=new it(this.gl,{width:1,height:1,widthSegments:2,heightSegments:2});this.plane=new q(this.gl,{geometry:t,program:this.material}),this.plane.setParent(this.scene)}}replaceShader(t){let e,i;this.shader.vertex?(e=this.material.uniforms.texture1.value,i=this.vertexMaterial.uniforms.texture1.value):(e=this.material.uniforms.texture1.value,i=this.material.uniforms.texture2.value);const s="random"===t?ft():pt[t],{fragment:r,uniforms:n,vertex:a}=s;this.shader=s,this.vertex=a||xt,this.fragment=r||"",this.uniforms=n||{},this.addObjects(),this.shader.vertex?(this.material.uniforms.texture1.value=i,this.vertexMaterial.uniforms.texture1.value=i):(this.material.uniforms.texture1.value=e,this.material.uniforms.texture2.value=i,this.material.uniforms.progress.value=1),this.resize(),this.swiper.params.gl.shader=t}replaceRandomShader(){const t=ft(),{fragment:e,uniforms:i,vertex:s}=t;this.shader=t,this.fragment=e||"",this.uniforms=i||{},this.vertex=s||xt,this.addObjects(),this.resize()}setProgress(t,e,i,s){if(!this.initialized)return void(this.onInit=()=>{requestAnimationFrame((()=>{this.setProgress(t,e,i,s)}))});const r=this.textures[e],n=this.textures[t];this.material.uniforms.texture1.value=n,this.shader.vertex?this.vertexMaterial.uniforms.texture1.value=r:this.material.uniforms.texture2.value=r,s?(0===i&&0===this.material.uniforms.progress.value&&(this.material.uniforms.progress.value=1),1===i&&1===this.material.uniforms.progress.value&&(this.material.uniforms.progress.value=0),this.animateUniform(this.material.uniforms.progress,i,(()=>{"random"===this.swiper.params.gl.shader&&(this.replaceRandomShader(),this.material.uniforms.texture1.value=n,this.material.uniforms.texture2.value=r,this.material.uniforms.progress.value=i),1===i&&(this.material.uniforms.texture1.value=r),this.material.uniforms.progress.value=0}))):this.material.uniforms.progress.value=Math.abs(i)}render(){this.swiper.destroyed||this.destroyed||(this.time+=.05,this.material.uniforms.time.value=this.time,Object.keys(this.uniforms).forEach((t=>{this.material.uniforms[t].value=this.uniforms[t].value})),requestAnimationFrame(this.render.bind(this)),this.renderer.render({scene:this.scene,camera:this.camera}))}destroy(){this.initialized=!1,this.destroyed=!0,this.renderer.clear(),this.renderer.dispose(),this.renderer.domElement.remove()}}return function({swiper:t,on:e,extendParams:i}){t.gl=null;let s=!1;i({gl:{shader:"random"}});const r=()=>{t.gl=new At({debug:!0,swiper:t,shader:t.params.gl.shader})};let n,a;e("beforeInit",(()=>{if("gl"!==t.params.effect)return;if(!function(){try{const t=document.createElement("canvas");return!!window.WebGLRenderingContext&&(t.getContext("webgl")||t.getContext("experimental-webgl"))}catch(t){return!1}}())return void(s=!0);t.classNames.push(`${t.params.containerModifierClass}gl`);const e={watchSlidesProgress:!0};Object.assign(t.params,e),Object.assign(t.originalParams,e)})),e("init",(()=>{"gl"!==t.params.effect||s||t.gl||r()})),e("resize",(()=>{"gl"!==t.params.effect||s||t.gl.resize()})),e("setTranslate",(()=>{if("gl"!==t.params.effect||s)return;let e,i,o;t.gl||r(),t.slides.forEach(((s,r)=>{const n=s.progress;(n>0&&n<1||0===n&&t.progress<a)&&(e=r,i=r+1,o=n),(n<0&&n>-1||0===n&&t.progress>a)&&(e=r-1,i=r,o=1+n)})),a=t.progress||0,void 0===e&&void 0===i||t.gl.setProgress(e,i,o,n)})),e("setTransition",((e,i)=>{"gl"!==t.params.effect||s||(n=i>0&&!t.params.cssMode)})),e("destroy",(()=>{"gl"!==t.params.effect||s||t.gl&&(t.gl.destroy(),t.gl=null)}))}}));
//# sourceMappingURL=swiper-gl.min.js.map