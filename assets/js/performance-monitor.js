/**
 * Performance Monitor for JavaScript Lazy Loading
 * Tracks loading performance and provides insights
 * Compatible with PHP 7.3+
 */
class PerformanceMonitor {
    constructor() {
        this.metrics = {
            pageLoadStart: performance.now(),
            scriptsLoaded: 0,
            totalScripts: 0,
            loadTimes: {},
            errors: []
        };
        
        this.init();
    }
    
    init() {
        this.trackPageLoad();
        this.trackScriptLoading();
        this.trackUserInteractions();
        
        // Report metrics after page load
        window.addEventListener('load', () => {
            setTimeout(() => this.reportMetrics(), 1000);
        });
    }
    
    trackPageLoad() {
        // Track DOM Content Loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.metrics.domContentLoaded = performance.now() - this.metrics.pageLoadStart;
            });
        } else {
            this.metrics.domContentLoaded = performance.now() - this.metrics.pageLoadStart;
        }
        
        // Track window load
        window.addEventListener('load', () => {
            this.metrics.windowLoaded = performance.now() - this.metrics.pageLoadStart;
        });
    }
    
    trackScriptLoading() {
        // Override the lazy loader's loadScript method to track performance
        if (window.lazyScriptLoader) {
            const originalLoadScript = window.lazyScriptLoader.loadScript.bind(window.lazyScriptLoader);
            
            window.lazyScriptLoader.loadScript = (src, options = {}) => {
                const startTime = performance.now();
                this.metrics.totalScripts++;
                
                return originalLoadScript(src, options).then(() => {
                    const loadTime = performance.now() - startTime;
                    this.metrics.loadTimes[src] = loadTime;
                    this.metrics.scriptsLoaded++;
                    
                    console.log(`Script loaded: ${src} (${loadTime.toFixed(2)}ms)`);
                }).catch(error => {
                    this.metrics.errors.push({
                        script: src,
                        error: error.message,
                        time: performance.now()
                    });
                    throw error;
                });
            };
        }
    }
    
    trackUserInteractions() {
        // Track First Input Delay (FID)
        let firstInputDelay = null;
        
        const trackFID = (event) => {
            if (firstInputDelay === null) {
                firstInputDelay = performance.now() - event.timeStamp;
                this.metrics.firstInputDelay = firstInputDelay;
                
                // Remove listeners after first interaction
                ['click', 'touchstart', 'keydown'].forEach(eventType => {
                    document.removeEventListener(eventType, trackFID, true);
                });
            }
        };
        
        ['click', 'touchstart', 'keydown'].forEach(eventType => {
            document.addEventListener(eventType, trackFID, true);
        });
    }
    
    measureCLS() {
        // Cumulative Layout Shift measurement
        let clsValue = 0;
        let clsEntries = [];
        
        const observer = new PerformanceObserver((entryList) => {
            for (const entry of entryList.getEntries()) {
                if (!entry.hadRecentInput) {
                    clsValue += entry.value;
                    clsEntries.push(entry);
                }
            }
        });
        
        observer.observe({ type: 'layout-shift', buffered: true });
        
        // Store CLS value after 5 seconds
        setTimeout(() => {
            this.metrics.cumulativeLayoutShift = clsValue;
            observer.disconnect();
        }, 5000);
    }
    
    measureLCP() {
        // Largest Contentful Paint measurement
        const observer = new PerformanceObserver((entryList) => {
            const entries = entryList.getEntries();
            const lastEntry = entries[entries.length - 1];
            this.metrics.largestContentfulPaint = lastEntry.startTime;
        });
        
        observer.observe({ type: 'largest-contentful-paint', buffered: true });
        
        // Stop observing after page load
        window.addEventListener('load', () => {
            setTimeout(() => observer.disconnect(), 1000);
        });
    }
    
    reportMetrics() {
        // Calculate additional metrics
        this.metrics.totalLoadTime = performance.now() - this.metrics.pageLoadStart;
        this.metrics.averageScriptLoadTime = this.calculateAverageLoadTime();
        this.metrics.scriptLoadingEfficiency = this.calculateEfficiency();
        
        // Measure Core Web Vitals
        this.measureCLS();
        this.measureLCP();
        
        // Log performance report
        console.group('🚀 Performance Report - JavaScript Lazy Loading');
        console.log('📊 Page Load Metrics:');
        console.log(`   DOM Content Loaded: ${this.metrics.domContentLoaded?.toFixed(2)}ms`);
        console.log(`   Window Loaded: ${this.metrics.windowLoaded?.toFixed(2)}ms`);
        console.log(`   Total Load Time: ${this.metrics.totalLoadTime.toFixed(2)}ms`);
        
        console.log('\n📦 Script Loading:');
        console.log(`   Scripts Loaded: ${this.metrics.scriptsLoaded}/${this.metrics.totalScripts}`);
        console.log(`   Average Load Time: ${this.metrics.averageScriptLoadTime.toFixed(2)}ms`);
        console.log(`   Loading Efficiency: ${this.metrics.scriptLoadingEfficiency.toFixed(1)}%`);
        
        if (this.metrics.firstInputDelay !== undefined) {
            console.log(`\n⚡ First Input Delay: ${this.metrics.firstInputDelay.toFixed(2)}ms`);
        }
        
        if (this.metrics.errors.length > 0) {
            console.log('\n❌ Loading Errors:');
            this.metrics.errors.forEach(error => {
                console.log(`   ${error.script}: ${error.error}`);
            });
        }
        
        console.log('\n📈 Recommendations:');
        this.generateRecommendations();
        
        console.groupEnd();
        
        // Send metrics to analytics if available
        this.sendToAnalytics();
    }
    
    calculateAverageLoadTime() {
        const loadTimes = Object.values(this.metrics.loadTimes);
        if (loadTimes.length === 0) return 0;
        
        return loadTimes.reduce((sum, time) => sum + time, 0) / loadTimes.length;
    }
    
    calculateEfficiency() {
        if (this.metrics.totalScripts === 0) return 100;
        return (this.metrics.scriptsLoaded / this.metrics.totalScripts) * 100;
    }
    
    generateRecommendations() {
        const recommendations = [];
        
        // Check DOM Content Loaded time
        if (this.metrics.domContentLoaded > 1500) {
            recommendations.push('Consider reducing critical CSS and JavaScript');
        }
        
        // Check script loading efficiency
        if (this.metrics.scriptLoadingEfficiency < 90) {
            recommendations.push('Some scripts failed to load - check network connectivity');
        }
        
        // Check average script load time
        if (this.metrics.averageScriptLoadTime > 500) {
            recommendations.push('Consider using a CDN or optimizing script sizes');
        }
        
        // Check First Input Delay
        if (this.metrics.firstInputDelay > 100) {
            recommendations.push('First Input Delay is high - consider reducing main thread blocking');
        }
        
        if (recommendations.length === 0) {
            console.log('   ✅ Performance looks good!');
        } else {
            recommendations.forEach(rec => console.log(`   💡 ${rec}`));
        }
    }
    
    sendToAnalytics() {
        // Send performance data to Google Analytics if available
        if (typeof gtag !== 'undefined') {
            gtag('event', 'page_performance', {
                'custom_map': {
                    'metric1': 'dom_content_loaded',
                    'metric2': 'window_loaded',
                    'metric3': 'scripts_loaded',
                    'metric4': 'first_input_delay'
                },
                'metric1': Math.round(this.metrics.domContentLoaded || 0),
                'metric2': Math.round(this.metrics.windowLoaded || 0),
                'metric3': this.metrics.scriptsLoaded,
                'metric4': Math.round(this.metrics.firstInputDelay || 0)
            });
        }
        
        // Store in localStorage for debugging
        try {
            localStorage.setItem('performance_metrics', JSON.stringify(this.metrics));
        } catch (e) {
            // Ignore localStorage errors
        }
    }
    
    // Public method to get current metrics
    getMetrics() {
        return { ...this.metrics };
    }
}

// Initialize performance monitoring in development/debug mode
if (window.location.hostname === 'localhost' || 
    window.location.search.includes('debug=1') ||
    localStorage.getItem('performance_debug') === 'true') {
    
    window.performanceMonitor = new PerformanceMonitor();
    
    // Add console command to toggle performance monitoring
    window.togglePerformanceDebug = function() {
        const current = localStorage.getItem('performance_debug') === 'true';
        localStorage.setItem('performance_debug', (!current).toString());
        console.log(`Performance debugging ${!current ? 'enabled' : 'disabled'}. Reload page to apply.`);
    };
    
    console.log('🔧 Performance monitoring enabled. Use togglePerformanceDebug() to toggle.');
}
