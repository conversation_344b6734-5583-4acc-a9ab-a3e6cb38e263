/**
 * YouTube Facade - Async Video Loading System
 * Loads YouTube videos only when user interacts with them
 * Compatible with PHP 7.3+ and modern browsers
 */
class YouTubeFacade {
    constructor() {
        this.loadedVideos = new Set();
        this.observers = new Map();
        this.init();
    }
    
    init() {
        // Initialize facades when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initializeFacades());
        } else {
            this.initializeFacades();
        }
    }
    
    /**
     * Initialize all YouTube facades on the page
     */
    initializeFacades() {
        // Find existing YouTube iframes and convert them to facades
        const existingIframes = document.querySelectorAll('iframe[src*="youtube.com"], iframe[src*="youtu.be"]');
        existingIframes.forEach(iframe => this.convertIframeToFacade(iframe));
        
        // Find elements marked for YouTube facade
        const facadeElements = document.querySelectorAll('[data-youtube-id], .youtube-facade');
        facadeElements.forEach(element => this.createFacade(element));
        
        // Setup intersection observer for lazy loading
        this.setupIntersectionObserver();
    }
    
    /**
     * Convert existing iframe to facade
     */
    convertIframeToFacade(iframe) {
        const src = iframe.src;
        const videoId = this.extractVideoId(src);
        
        if (!videoId) return;
        
        // Extract iframe attributes
        const width = iframe.width || iframe.offsetWidth || '100%';
        const height = iframe.height || iframe.offsetHeight || '315';
        const title = iframe.title || 'YouTube video player';
        const allowFullscreen = iframe.allowFullscreen;
        
        // Create facade container
        const facade = this.createFacadeElement(videoId, {
            width,
            height,
            title,
            allowFullscreen,
            autoplay: src.includes('autoplay=1'),
            muted: src.includes('mute=1'),
            loop: src.includes('loop=1'),
            controls: !src.includes('controls=0'),
            playlist: this.extractPlaylist(src)
        });
        
        // Replace iframe with facade
        iframe.parentNode.replaceChild(facade, iframe);
        
        console.log(`YouTube facade created for video: ${videoId}`);
    }
    
    /**
     * Create facade for element with data attributes
     */
    createFacade(element) {
        const videoId = element.dataset.youtubeId;
        if (!videoId) return;
        
        const options = {
            width: element.dataset.width || '100%',
            height: element.dataset.height || '315',
            title: element.dataset.title || 'YouTube video player',
            autoplay: element.dataset.autoplay === 'true',
            muted: element.dataset.muted === 'true',
            loop: element.dataset.loop === 'true',
            controls: element.dataset.controls !== 'false',
            allowFullscreen: element.dataset.allowFullscreen !== 'false'
        };
        
        const facade = this.createFacadeElement(videoId, options);
        element.appendChild(facade);
    }
    
    /**
     * Create the facade element
     */
    createFacadeElement(videoId, options = {}) {
        const container = document.createElement('div');
        container.className = 'youtube-facade-container';
        container.style.cssText = `
            position: relative;
            width: ${options.width};
            height: ${options.height};
            background: #000;
            cursor: pointer;
            overflow: hidden;
            border-radius: 8px;
        `;
        
        // Create thumbnail
        const thumbnail = document.createElement('div');
        thumbnail.className = 'youtube-facade-thumbnail';
        thumbnail.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('https://img.youtube.com/vi/${videoId}/maxresdefault.jpg');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            transition: opacity 0.3s ease;
        `;
        
        // Create play button
        const playButton = document.createElement('div');
        playButton.className = 'youtube-facade-play-button';
        playButton.innerHTML = `
            <svg width="68" height="48" viewBox="0 0 68 48" fill="none">
                <path d="M66.52,7.74c-0.78-2.93-2.49-5.41-5.42-6.19C55.79,.13,34,0,34,0S12.21,.13,6.9,1.55 C3.97,2.33,2.27,4.81,1.48,7.74C0.06,13.05,0,24,0,24s0.06,10.95,1.48,16.26c0.78,2.93,2.49,5.41,5.42,6.19 C12.21,47.87,34,48,34,48s21.79-0.13,27.1-1.55c2.93-0.78,4.64-3.26,5.42-6.19C67.94,34.95,68,24,68,24S67.94,13.05,66.52,7.74z" fill="#f00"></path>
                <path d="M 45,24 27,14 27,34" fill="#fff"></path>
            </svg>
        `;
        playButton.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            transition: transform 0.2s ease;
            filter: drop-shadow(0 4px 8px rgba(0,0,0,0.3));
        `;
        
        // Create loading overlay
        const loadingOverlay = document.createElement('div');
        loadingOverlay.className = 'youtube-facade-loading';
        loadingOverlay.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            display: none;
            align-items: center;
            justify-content: center;
            color: white;
            font-family: Arial, sans-serif;
        `;
        loadingOverlay.innerHTML = `
            <div style="text-align: center;">
                <div style="width: 40px; height: 40px; border: 3px solid #333; border-top: 3px solid #fff; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 10px;"></div>
                <div>Loading video...</div>
            </div>
        `;
        
        // Add hover effects
        container.addEventListener('mouseenter', () => {
            playButton.style.transform = 'translate(-50%, -50%) scale(1.1)';
        });
        
        container.addEventListener('mouseleave', () => {
            playButton.style.transform = 'translate(-50%, -50%) scale(1)';
        });
        
        // Add click handler
        container.addEventListener('click', () => {
            this.loadVideo(container, videoId, options);
        });
        
        // Store video data
        container.dataset.videoId = videoId;
        container.dataset.options = JSON.stringify(options);
        
        // Assemble facade
        container.appendChild(thumbnail);
        container.appendChild(playButton);
        container.appendChild(loadingOverlay);
        
        // Add CSS animation for loading spinner
        if (!document.getElementById('youtube-facade-styles')) {
            const style = document.createElement('style');
            style.id = 'youtube-facade-styles';
            style.textContent = `
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
                .youtube-facade-container:hover .youtube-facade-thumbnail {
                    opacity: 0.8;
                }
            `;
            document.head.appendChild(style);
        }
        
        return container;
    }
    
    /**
     * Load the actual YouTube video
     */
    async loadVideo(container, videoId, options = {}) {
        if (this.loadedVideos.has(videoId)) return;
        
        // Show loading state
        const loadingOverlay = container.querySelector('.youtube-facade-loading');
        const thumbnail = container.querySelector('.youtube-facade-thumbnail');
        const playButton = container.querySelector('.youtube-facade-play-button');
        
        loadingOverlay.style.display = 'flex';
        playButton.style.display = 'none';
        
        try {
            // Build YouTube URL
            const params = new URLSearchParams();
            if (options.autoplay) params.set('autoplay', '1');
            if (options.muted) params.set('mute', '1');
            if (options.loop) params.set('loop', '1');
            if (!options.controls) params.set('controls', '0');
            if (options.playlist) params.set('playlist', options.playlist);
            
            // Add additional parameters for better performance
            params.set('rel', '0'); // Don't show related videos
            params.set('modestbranding', '1'); // Minimal YouTube branding
            params.set('iv_load_policy', '3'); // Hide annotations
            
            const iframeSrc = `https://www.youtube.com/embed/${videoId}?${params.toString()}`;
            
            // Create iframe
            const iframe = document.createElement('iframe');
            iframe.src = iframeSrc;
            iframe.width = options.width || '100%';
            iframe.height = options.height || '315';
            iframe.title = options.title || 'YouTube video player';
            iframe.frameBorder = '0';
            iframe.allow = 'accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share';
            iframe.referrerPolicy = 'strict-origin-when-cross-origin';
            if (options.allowFullscreen) iframe.allowFullscreen = true;
            
            iframe.style.cssText = `
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                border: none;
                border-radius: 8px;
            `;
            
            // Wait for iframe to load
            await new Promise((resolve, reject) => {
                iframe.onload = resolve;
                iframe.onerror = reject;
                
                // Timeout after 10 seconds
                setTimeout(() => reject(new Error('Timeout')), 10000);
            });
            
            // Replace facade with iframe
            container.appendChild(iframe);
            
            // Fade out thumbnail and loading
            thumbnail.style.opacity = '0';
            loadingOverlay.style.opacity = '0';
            
            setTimeout(() => {
                thumbnail.remove();
                loadingOverlay.remove();
                playButton.remove();
            }, 300);
            
            this.loadedVideos.add(videoId);
            
            // Track performance
            if (window.performanceMonitor) {
                window.performanceMonitor.metrics.youtubeVideosLoaded = 
                    (window.performanceMonitor.metrics.youtubeVideosLoaded || 0) + 1;
            }
            
            console.log(`YouTube video loaded: ${videoId}`);
            
        } catch (error) {
            console.error('Failed to load YouTube video:', error);
            
            // Hide loading and show error
            loadingOverlay.innerHTML = `
                <div style="text-align: center; color: #ff6b6b;">
                    <div style="font-size: 24px; margin-bottom: 10px;">⚠️</div>
                    <div>Failed to load video</div>
                    <div style="font-size: 12px; margin-top: 5px;">Click to retry</div>
                </div>
            `;
            
            // Allow retry
            container.addEventListener('click', () => {
                this.loadedVideos.delete(videoId);
                this.loadVideo(container, videoId, options);
            }, { once: true });
        }
    }
    
    /**
     * Setup intersection observer for lazy loading
     */
    setupIntersectionObserver() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const container = entry.target;
                    const videoId = container.dataset.videoId;
                    const options = JSON.parse(container.dataset.options || '{}');
                    
                    // Auto-load if specified
                    if (options.autoload) {
                        this.loadVideo(container, videoId, options);
                        observer.unobserve(container);
                    }
                }
            });
        }, { rootMargin: '100px' });
        
        // Observe all facade containers
        document.querySelectorAll('.youtube-facade-container').forEach(container => {
            observer.observe(container);
        });
        
        this.observers.set('youtube', observer);
    }
    
    /**
     * Extract video ID from YouTube URL
     */
    extractVideoId(url) {
        const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
        const match = url.match(regex);
        return match ? match[1] : null;
    }
    
    /**
     * Extract playlist ID from YouTube URL
     */
    extractPlaylist(url) {
        const regex = /[?&]playlist=([^&]+)/;
        const match = url.match(regex);
        return match ? match[1] : null;
    }
    
    /**
     * Public method to manually load a video
     */
    loadVideoById(videoId) {
        const container = document.querySelector(`[data-video-id="${videoId}"]`);
        if (container) {
            const options = JSON.parse(container.dataset.options || '{}');
            this.loadVideo(container, videoId, options);
        }
    }
    
    /**
     * Clean up observers
     */
    destroy() {
        this.observers.forEach(observer => observer.disconnect());
        this.observers.clear();
    }
}

// Initialize YouTube facade system
window.youtubeFacade = new YouTubeFacade();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = YouTubeFacade;
}
